package servlet.pactoPay;

import br.com.pactosolucoes.atualizadb.processo.PovoarMovParcelaTentativaConvenio;
import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaColaborador;
import br.com.pactosolucoes.autorizacaocobranca.modelo.*;
import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.bi.service.impl.BiMSService;
import br.com.pactosolucoes.bi.service.impl.BiMSServiceImpl;
import br.com.pactosolucoes.comuns.json.ItemCobrancaPactoJSON;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.pactopay.TipoConsultaHistoricoCartaoEnum;
import br.com.pactosolucoes.integracao.pactopay.front.*;
import br.com.pactosolucoes.integracao.pactopay.front.cartao.CobrancaDetalheDTO;
import br.com.pactosolucoes.integracao.pactopay.front.cartao.HistoricoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pactopay.PactoPayDashCartao;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.validator.CreditCardValidator;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.adm.CreditoDCCService;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.gatewaypagamento.PagamentoService;
import servicos.propriedades.PropsService;
import servlet.arquitetura.SuperServlet;

import javax.faces.model.SelectItem;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 29/04/2021
 */
public class PactoPayDashServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("cobrancas")) {
                envelopeRespostaDTO = obterCobrancasCliente(request);
            } else if (operacao.equalsIgnoreCase("consultarclientes")) {
                envelopeRespostaDTO = consultarClientes(request);
            } else if (operacao.equalsIgnoreCase("cobrancasparcela")) {
                envelopeRespostaDTO = obterCobrancasParcela(request);
            } else if (operacao.equalsIgnoreCase("bloquear") || operacao.equalsIgnoreCase("desbloquear")) {
                envelopeRespostaDTO = bloquearDesbloquearCobrancas(request, operacao.equalsIgnoreCase("bloquear"));
            } else if (operacao.equalsIgnoreCase("excluirauto")) {
                envelopeRespostaDTO = excluirAutorizacaoCobranca(request);
            } else if (operacao.equalsIgnoreCase("incluirauto")) {
                envelopeRespostaDTO = incluirAutorizacaoCobranca(request);
            } else if (operacao.equalsIgnoreCase("bandeira")) {
                envelopeRespostaDTO = obterBandeira(request);
            } else if (operacao.equalsIgnoreCase("parcelascliente")) {
                envelopeRespostaDTO = obterParcelasTelaCliente(request);
            } else if (operacao.equalsIgnoreCase("parcelas")) {
                envelopeRespostaDTO = obterParcelas(request);
            } else if (operacao.equalsIgnoreCase("remessa")) {
                envelopeRespostaDTO = obterRemessas(request);
            } else if (operacao.equalsIgnoreCase("remessaitens")) {
                envelopeRespostaDTO = obterItensRemessa(request);
            } else if (operacao.equalsIgnoreCase("imprimirrecibo")) {
                imprimirRecibo(request, response);
                return;
            } else if (operacao.equalsIgnoreCase("imprimirtransacao")) {
                imprimirTransacao(request, response);
                return;
            } else if (operacao.equalsIgnoreCase("pendente")) {
                envelopeRespostaDTO = obterPendentes(request);
            } else if (operacao.equalsIgnoreCase("pendentelista")) {
                envelopeRespostaDTO = obterPendentesLista(request);
            } else if (operacao.equalsIgnoreCase("cobrar")) {
                envelopeRespostaDTO = cobrarParcelas(request);
            } else if (operacao.equalsIgnoreCase("retentarParcela")) {
                envelopeRespostaDTO = retentarParcela(request);
            } else if (operacao.equalsIgnoreCase("retentarTransacao")) {
                envelopeRespostaDTO = retentarTransacao(request);
            } else if (operacao.equalsIgnoreCase("foto")) {
                envelopeRespostaDTO = obterFoto(request);
            } else if (operacao.equalsIgnoreCase("credito")) {
                envelopeRespostaDTO = obterCreditoPacto(request);
            } else if (operacao.equalsIgnoreCase("creditolista")) {
                envelopeRespostaDTO = obterCreditoPactoLista(request);
            } else if (operacao.equalsIgnoreCase("creditohistorico")) {
                envelopeRespostaDTO = obterCreditoPactoHistorico(request);
            } else if (operacao.equalsIgnoreCase("creditohistoricoitens")) {
                envelopeRespostaDTO = obterCreditoPactoHistoricoItens(request);
            } else if (operacao.equalsIgnoreCase("convenios")) {
                envelopeRespostaDTO = consultarConveniosCobranca(request);
            } else if (operacao.equalsIgnoreCase("conveniosGeral")) {
                envelopeRespostaDTO = consultarConveniosCobrancaGeral(request);
            } else if (operacao.equalsIgnoreCase("tipoprodutos")) {
                envelopeRespostaDTO = obterTipoProdutos();
            } else if (operacao.equalsIgnoreCase("tipoparcelacobrar")) {
                envelopeRespostaDTO = obterTipoParcelasCobrar();
            } else if (operacao.equalsIgnoreCase("tipoautorizacao")) {
                envelopeRespostaDTO = obterTipoAutorizacao();
            } else if (operacao.equalsIgnoreCase("tipoConvenio")) {
                envelopeRespostaDTO = obterTipoConvenios();
            } else if (operacao.equalsIgnoreCase("tipoTransacao")) {
                envelopeRespostaDTO = obterTipoTransacao();
            } else if (operacao.equalsIgnoreCase("notificar")) {
                envelopeRespostaDTO = notificarRecursoEmpresa(request);
            } else if (operacao.equalsIgnoreCase("bloquearcatraca")) {
                envelopeRespostaDTO = bloquearCatraca(request);
            } else if (operacao.equalsIgnoreCase("desbloquearcatraca")) {
                envelopeRespostaDTO = desbloquearCatraca(request);
            } else if (operacao.equalsIgnoreCase("infoRetorno")) {
                envelopeRespostaDTO = infoRetornos(request);
            } else if (operacao.equalsIgnoreCase("cancelarCobranca")) {
                envelopeRespostaDTO = cancelarCobranca(request);
            } else if (operacao.equalsIgnoreCase("eficienciaCobranca")) {
                envelopeRespostaDTO = eficienciaCobranca(request);
            } else if (operacao.equalsIgnoreCase("dashcartao")) {
                envelopeRespostaDTO = consultarDashCartao(request);
            } else if (operacao.equalsIgnoreCase("dashcartaolista")) {
                envelopeRespostaDTO = consultarDashCartaoLista(request);
            } else if (operacao.equalsIgnoreCase("obterCobrancaDetalhe")) {
                envelopeRespostaDTO = obterCobrancaDetalhe(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO obterCobrancasCliente(ServletRequest request) throws Exception {
        String tipo = request.getParameter("tipo");
        if (tipo.equalsIgnoreCase("cartao")) {
            return obterCobrancasCartao(request);
        } else if (tipo.equalsIgnoreCase("pix")) {
            return obterCobrancasPix(request);
        } else if (tipo.equalsIgnoreCase("debito")) {
            return obterCobrancasDebitoConta(request);
        } else if (tipo.equalsIgnoreCase("boleto")) {
            return obterCobrancasBoleto(request);
        } else {
            throw new Exception("Tipo não informado");
        }
    }

    private EnvelopeRespostaDTO obterCobrancasCartao(ServletRequest request) throws Exception {
        Transacao transacaoDAO;
        Usuario usuarioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        RemessaItem remessaItemDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            remessaItemDAO = new RemessaItem(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            StringBuilder sql = new StringBuilder();
            sql.append("select * from ( \n");
            sql.append("select  \n");
            sql.append("'TRANSACAO' as tipo, \n");
            sql.append("t.codigo, \n");
            sql.append("t.dataprocessamento as data \n");
            sql.append("FROM Transacao t \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = t.conveniocobranca \n");
            sql.append("WHERE t.codigo in ( \n");
            sql.append("select   \n");
            sql.append("t.codigo  \n");
            sql.append("from transacao t  \n");
            sql.append("where t.pessoapagador = ").append(pessoa).append(" \n");
            sql.append("union  \n");
            sql.append("select  \n");
            sql.append("t.codigo  \n");
            sql.append("from transacao t  \n");
            sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo  \n");
            sql.append("inner join movparcela mp on mp.codigo = tm.movparcela  \n");
            sql.append("where mp.pessoa = ").append(pessoa).append(") \n");
            sql.append("union \n");
            sql.append("select  \n");
            sql.append("'REMESSA' as tipo, \n");
            sql.append("ri.codigo, \n");
            sql.append("r.dataregistro as data \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("INNER JOIN remessa r on r.codigo = ri.remessa \n");
            sql.append("WHERE ri.codigo in ( \n");
            sql.append("select  \n");
            sql.append("distinct(ri.codigo) as codigo \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("INNER JOIN remessa r on r.codigo = ri.remessa \n");
            sql.append("WHERE r.tipo in (2,8,12) \n");
            sql.append("and coalesce(ri.pessoa,0) = ").append(pessoa).append(" \n");
            sql.append("union  \n");
            sql.append("select  \n");
            sql.append("distinct(ri.codigo) as codigo \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql.append("inner join movparcela mp on mp.codigo = coalesce(ri.movparcela,0) \n");
            sql.append("where r.tipo in (2,8,12) \n");
            sql.append("and mp.pessoa = ").append(pessoa).append(" \n");
            sql.append("union  \n");
            sql.append("select  \n");
            sql.append("distinct(ri.codigo) as codigo \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql.append("inner join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
            sql.append("inner join movparcela mp on mp.codigo = coalesce(rim.movparcela,0) \n");
            sql.append("where r.tipo in (2,8,12) \n");
            sql.append("and mp.pessoa = ").append(pessoa).append(") \n");
            sql.append(") as sql \n");
            sql.append("order by sql.data desc \n");

            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<CobrancaDTO> lista = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(total)) {

                sql.append("LIMIT ").append(maxResults).append(" \n");
                sql.append("OFFSET ").append(indiceInicial).append(" \n");

                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

                Map<Integer, UsuarioVO> mapaUsuario = new HashMap<>();
                Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();

                while (rs.next()) {
                    try {
                        String tipo = rs.getString("tipo");
                        int codigo = rs.getInt("codigo");

                        CobrancaDTO cobrancaDTO = null;
                        if (tipo.equalsIgnoreCase("TRANSACAO")) {
                            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TELACONSULTA);

                            if (!UteisValidacao.emptyNumber(transacaoVO.getUsuarioResponsavel().getCodigo())) {
                                UsuarioVO usuarioVO = mapaUsuario.get(transacaoVO.getUsuarioResponsavel().getCodigo());
                                if (usuarioVO == null) {
                                    usuarioVO = usuarioDAO.consultarPorChavePrimaria(transacaoVO.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                    mapaUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                                }
                                transacaoVO.setUsuarioResponsavel(usuarioVO);
                            }

                            if (!UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                                ConvenioCobrancaVO convenioVO = mapConve.get(transacaoVO.getConvenioCobrancaVO().getCodigo());
                                if (convenioVO == null) {
                                    convenioVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    mapConve.put(convenioVO.getCodigo(), convenioVO);
                                }
                                transacaoVO.setConvenioCobrancaVO(convenioVO);
                            }

                            cobrancaDTO = new CobrancaDTO(transacaoVO);
                            cobrancaDTO.setQtdParcelas(transacaoDAO.consultarQtdParcelasTransacao(transacaoVO.getCodigo()));
                        } else if (tipo.equalsIgnoreCase("REMESSA")) {
                            RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                            if (!UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getUsuario().getCodigo())) {
                                UsuarioVO usuarioVO = mapaUsuario.get(remessaItemVO.getRemessa().getUsuario().getCodigo());
                                if (usuarioVO == null) {
                                    usuarioVO = usuarioDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                    mapaUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                                }
                                remessaItemVO.getRemessa().setUsuario(usuarioVO);
                            }

                            cobrancaDTO = new CobrancaDTO(remessaItemVO);
                            cobrancaDTO.setQtdParcelas(remessaItemDAO.consultarQtdParcelasItem(remessaItemVO.getCodigo()));
                        }
                        if (cobrancaDTO != null) {
                            lista.add(cobrancaDTO);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
            usuarioDAO = null;
            convenioCobrancaDAO = null;
            remessaItemDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterCobrancasPix(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        Pix pixDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            pixDAO = new Pix(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            Integer total = pixDAO.quantidadePorPessoa(pessoa);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<CobrancaPixDTO> lista = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(total)) {
                Map<Integer, UsuarioVO> mapUsuario = new HashMap<>();
                Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();

                List<PixVO> listaPix = pixDAO.consultarPorPessoaTelaCliente(pessoa, maxResults, indiceInicial);
                for (PixVO pixVO : listaPix) {
                    try {

                        if (!UteisValidacao.emptyNumber(pixVO.getConveniocobranca().getCodigo())) {
                            ConvenioCobrancaVO convenioVO = mapConve.get(pixVO.getConveniocobranca().getCodigo());
                            if (convenioVO == null) {
                                convenioVO = convenioCobrancaDAO.consultarPorChavePrimaria(pixVO.getConveniocobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                mapConve.put(convenioVO.getCodigo(), convenioVO);
                            }
                            pixVO.setConveniocobranca(convenioVO);
                        }

                        if (!UteisValidacao.emptyNumber(pixVO.getUsuarioResponsavel().getCodigo())) {
                            UsuarioVO usuarioVO = mapUsuario.get(pixVO.getUsuarioResponsavel().getCodigo());
                            if (usuarioVO == null) {
                                usuarioVO = usuarioDAO.consultarPorChavePrimaria(pixVO.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                mapUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                            }
                            pixVO.setUsuarioResponsavel(usuarioVO);
                        }

                        CobrancaPixDTO cobrancaPixDTO = new CobrancaPixDTO(pixVO);
                        cobrancaPixDTO.setQtdParcelas(pixDAO.consultarQtdParcelas(pixVO.getCodigo()));
                        lista.add(cobrancaPixDTO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            convenioCobrancaDAO = null;
            pixDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterCobrancasDebitoConta(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        RemessaItem remessaItemDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            remessaItemDAO = new RemessaItem(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            TipoCobrancaEnum[] tiposCobranca = {TipoCobrancaEnum.EDI_DCO};
            Integer total = remessaItemDAO.obterCountRemessaCliente(pessoa, tiposCobranca);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<CobrancaBoletoDTO> lista = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(total)) {
                List<RemessaItemVO> listaItens = remessaItemDAO.consultarTelaCliente(pessoa, maxResults, indiceInicial, tiposCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0);

                Map<Integer, UsuarioVO> mapUsuario = new HashMap<>();
                Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();

                for (RemessaItemVO remessaItemVO : listaItens) {
                    try {
                        if (!UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo())) {
                            ConvenioCobrancaVO convenioVO = mapConve.get(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo());
                            if (convenioVO == null) {
                                convenioVO = convenioCobrancaDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                mapConve.put(convenioVO.getCodigo(), convenioVO);
                            }
                            remessaItemVO.getRemessa().setConvenioCobranca(convenioVO);
                        }

                        if (!UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getUsuario().getCodigo())) {
                            UsuarioVO usuarioVO = mapUsuario.get(remessaItemVO.getRemessa().getUsuario().getCodigo());
                            if (usuarioVO == null) {
                                usuarioVO = usuarioDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                mapUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                            }
                            remessaItemVO.getRemessa().setUsuario(usuarioVO);
                        }

                        lista.add(new CobrancaBoletoDTO(remessaItemVO));
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            convenioCobrancaDAO = null;
            remessaItemDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterCobrancasBoleto(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        RemessaItem remessaItemDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            remessaItemDAO = new RemessaItem(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            TipoCobrancaEnum[] tiposCobranca = {TipoCobrancaEnum.BOLETO};
            Integer total = remessaItemDAO.obterCountRemessaCliente(pessoa, tiposCobranca);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<CobrancaBoletoDTO> lista = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(total)) {
                List<RemessaItemVO> listaItens = remessaItemDAO.consultarTelaCliente(pessoa, maxResults, indiceInicial, tiposCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0);

                Map<Integer, UsuarioVO> mapUsuario = new HashMap<>();
                Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();

                for (RemessaItemVO remessaItemVO : listaItens) {
                    try {
                        if (!UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo())) {
                            ConvenioCobrancaVO convenioVO = mapConve.get(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo());
                            if (convenioVO == null) {
                                convenioVO = convenioCobrancaDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                mapConve.put(convenioVO.getCodigo(), convenioVO);
                            }
                            remessaItemVO.getRemessa().setConvenioCobranca(convenioVO);
                        }

                        if (!UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getUsuario().getCodigo())) {
                            UsuarioVO usuarioVO = mapUsuario.get(remessaItemVO.getRemessa().getUsuario().getCodigo());
                            if (usuarioVO == null) {
                                usuarioVO = usuarioDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                mapUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                            }
                            remessaItemVO.getRemessa().setUsuario(usuarioVO);
                        }

                        lista.add(new CobrancaBoletoDTO(remessaItemVO));
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            convenioCobrancaDAO = null;
            remessaItemDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterFoto(ServletRequest request) throws Exception {
        Pessoa pessoaDAO;
        try (Connection con = obterConexao(request)) {
            pessoaDAO = new Pessoa(con);
            String fotokey = pessoaDAO.obterFotoKey(Integer.parseInt(request.getParameter("p")));
            return EnvelopeRespostaDTO.of(Uteis.getPaintFotoDaNuvem(fotokey));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pessoaDAO = null;
        }
    }

    private EnvelopeRespostaDTO obterCreditoPacto(ServletRequest request) throws Exception {
        Empresa empresaDAO;
        CreditoDCCService creditoDCCService;
        Connection con = null;
        try {
            con = obterConexao(request);
            empresaDAO = new Empresa(con);
            creditoDCCService = new CreditoDCCService(con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(obterEmpresa(request, true), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            CreditoPactoDTO creditoPactoDTO = new CreditoPactoDTO();
            String msg = "";
            Integer credito = 0;

            if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo()) ||
                    empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo())) {

                if (empresaVO.getCreditoDCC() >= RemessaService.PADRAO_CREDITO_DCC) {
                    credito = empresaVO.getCreditoDCC();
                    msg = empresaVO.getCreditoDCC() + " crédito(s)";
                } else if (empresaVO.getCreditoDCC() >= RemessaService.INICIO_LIMITE_EMERGENCIAL_DCC) {
                    credito = empresaVO.getCreditoDCC();
                    msg = "Restam apenas " + empresaVO.getCreditoDCC() + " crédito(s)";
                } else if (empresaVO.getCreditoDCC() > RemessaService.LIMITE_EMERGENCIAL_DCC) {
                    credito = (-1 * empresaVO.getCreditoDCC());
                    msg = "Limite emergencial! Você já consumiu " + (-1 * empresaVO.getCreditoDCC()) + " de " + (-1 * RemessaService.LIMITE_EMERGENCIAL_DCC) + " crédito(s)";
                } else {
                    msg = "Você não possui saldo de transações";
                }

            } else {
                Integer qtdRemessa = creditoDCCService.consultarItensCobrancaPactoRemessasItemTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO));
                Integer qtdTransacao = creditoDCCService.consultarItensCobrancaPactoTransacaoTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO));
                Integer qtdPix = creditoDCCService.consultarItensCobrancaPactoPixTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO));
                Integer qtdBoletosOnline = creditoDCCService.consultarItensCobrancaPactoBoletoOnlineTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO));
                Integer qtdBoletosRemessa = creditoDCCService.consultarItensCobrancaPactoBoletoRemessaTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO));
                Integer qtd = (qtdRemessa + qtdTransacao + qtdPix + qtdBoletosOnline + qtdBoletosRemessa);
                credito = qtd;
                msg = (qtd + " crédito(s) utilizado(s)");
                creditoPactoDTO.setUtilizados(true);
                creditoPactoDTO.setRemessa(qtdRemessa);
                creditoPactoDTO.setTransacao(qtdTransacao);
                creditoPactoDTO.setPix(qtdPix);
                creditoPactoDTO.setBoletoOnline(qtdBoletosOnline);
                creditoPactoDTO.setBoletoRemessa(qtdBoletosRemessa);
            }

            creditoPactoDTO.setQuantidade(credito);
            creditoPactoDTO.setDescricao(msg);
            return EnvelopeRespostaDTO.of(creditoPactoDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresaDAO = null;
            creditoDCCService = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterCreditoPactoLista(ServletRequest request) throws Exception {
        Empresa empresaDAO;
        CreditoDCCService creditoDCCService;
        Connection con = null;
        try {
            con = obterConexao(request);
            empresaDAO = new Empresa(con);
            creditoDCCService = new CreditoDCCService(con);

            String tipo = request.getParameter("tipo");
            boolean geral = (tipo == null || tipo.equalsIgnoreCase("geral"));

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(obterEmpresa(request, true), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            TipoCobrancaPactoEnum tipoCobrancaPactoEnum = TipoCobrancaPactoEnum.getConsultarPorCodigo(empresaVO.getTipoCobrancaPacto());
            if (tipoCobrancaPactoEnum == null || tipoCobrancaPactoEnum.isPrePago()) {
                throw new Exception("Recurso não disponível para empresa pré-pago");
            }

            List<ItemCobrancaPactoJSON> lista = new ArrayList<>();
            if (geral || tipo.equalsIgnoreCase("remessa")) {
                lista.addAll(creditoDCCService.consultarItensCobrancaPactoRemessasItem(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO)));
            }
            if (geral || tipo.equalsIgnoreCase("transacao")) {
                lista.addAll(creditoDCCService.consultarItensCobrancaPactoTransacao(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO)));
            }
            if (geral || tipo.equalsIgnoreCase("pix")) {
                lista.addAll(creditoDCCService.consultarItensCobrancaPactoPix(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto(empresaVO)));
            }

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(10L);
            }
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }

            int indiceInicial = paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            int maxResults = (indiceInicial + paginadorDTO.getSize().intValue());

            paginadorDTO.setQuantidadeTotalElementos((long) lista.size());

            Ordenacao.ordenarLista(lista, "data");

            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            if (!UteisValidacao.emptyString(filtroDTO.getParametro())) {
                List<ItemCobrancaPactoJSON> listaFiltrada = new ArrayList<>();
                for (ItemCobrancaPactoJSON item : lista) {
                    JSONObject json = new JSONObject(item);
                    String[] keys = JSONObject.getNames(json);
                    for (String key : keys) {
                        try {
                            if (json.optString(key).toUpperCase().contains(filtroDTO.getParametro().toUpperCase())) {
                                listaFiltrada.add(item);
                                break;
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
                lista = listaFiltrada;
                paginadorDTO.setQuantidadeTotalElementos((long) lista.size());
            }

            if (maxResults > lista.size()) {
                maxResults = lista.size();
            }
            if (indiceInicial < lista.size()) {
                lista = lista.subList(indiceInicial, maxResults);
            } else {
                lista = new ArrayList<>();
            }

            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresaDAO = null;
            creditoDCCService = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterCreditoPactoHistorico(ServletRequest request) throws Exception {
        LogCobrancaPacto logCobrancaPactoDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            logCobrancaPactoDAO = new LogCobrancaPacto(con);

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            List<LogCobrancaPactoVO> listaVO = logCobrancaPactoDAO.consultarTodas(obterEmpresa(request, true), paginadorDTO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<LogCobrancaPactoJSON> lista = new ArrayList<>();
            for (LogCobrancaPactoVO item : listaVO) {
                lista.add(new LogCobrancaPactoJSON(item));
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            logCobrancaPactoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterCreditoPactoHistoricoItens(ServletRequest request) throws Exception {
        LogCobrancaPacto logCobrancaPactoDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            logCobrancaPactoDAO = new LogCobrancaPacto(con);

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            if (paginadorDTO.getSize() == null) {
                paginadorDTO.setSize(10L);
            }
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(0L);
            }

            int indiceInicial = paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            int maxResults = (indiceInicial + paginadorDTO.getSize().intValue());

            LogCobrancaPactoVO logCobrancaPactoVO = logCobrancaPactoDAO.consultarChavePrimaria(Integer.parseInt(request.getParameter("codigo")), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<ItemCobrancaPactoJSON> lista = logCobrancaPactoVO.getListaItens();
            Ordenacao.ordenarLista(lista, "nome");
            paginadorDTO.setQuantidadeTotalElementos((long) lista.size());

            if (maxResults > lista.size()) {
                maxResults = lista.size();
            }
            if (indiceInicial < lista.size()) {
                lista = lista.subList(indiceInicial, maxResults);
            } else {
                lista = new ArrayList<>();
            }


            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            logCobrancaPactoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterTipoConvenios() {
        try {
            JSONArray jsonArray = new JSONArray();
            for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
                JSONObject json = new JSONObject();
                json.put("codigo", tipo.getCodigo());
                json.put("descricao", tipo.getDescricao());
                jsonArray.put(json);
            }
            return EnvelopeRespostaDTO.of(jsonArray);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO obterTipoTransacao() {
        try {
            JSONArray jsonArray = new JSONArray();
            for (TipoTransacaoEnum tipo : TipoTransacaoEnum.values()) {
                JSONObject json = new JSONObject();
                json.put("codigo", tipo.getId());
                json.put("descricao", tipo.getDescricao());
                jsonArray.put(json);
            }
            return EnvelopeRespostaDTO.of(jsonArray);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO obterParcelas(ServletRequest request) throws Exception {
        MovParcela movParcelaDAO;
        ConvenioCobranca convenioCobrancaDAO;
        Cliente clienteDAO;
        Pessoa pessoaDAO;
        ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresaDAO;
        Empresa empresaDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            movParcelaDAO = new MovParcela(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            clienteDAO = new Cliente(con);
            pessoaDAO = new Pessoa(con);
            configuracaoReenvioMovParcelaEmpresaDAO = new ConfiguracaoReenvioMovParcelaEmpresa(con);
            empresaDAO = new Empresa(con);

            Integer convenio = UteisValidacao.converterInteiro(request.getParameter("convenio"));
            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            Integer tipoParcelaParametro = UteisValidacao.converterInteiro(request.getParameter("tipoParcela"));
            TipoConsultaParcelasEnum tipoParcela = UteisValidacao.emptyNumber(tipoParcelaParametro) ? null : TipoConsultaParcelasEnum.obterID(tipoParcelaParametro);

            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            ConvenioCobrancaVO convenioCobrancaVO = null;
            if (convenio != null && convenio != 0) {
                convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenio, empresa, Uteis.NIVELMONTARDADOS_TODOS);
                validarConvenioCobranca(convenioCobrancaVO);
            }

            List<MovParcelaVO> lista = movParcelaDAO.consultarParcelasEmAbertoPactoPay(convenioCobrancaVO, tipoParcela, filtroDTO.getInicioDate(), filtroDTO.getFimDate(), paginadorDTO, filtroDTO, null);

            validarParcelasQueSeraoCobradasHoje(movParcelaDAO, convenioCobrancaDAO, pessoaDAO, filtroDTO, lista, configuracaoReenvioMovParcelaEmpresaDAO, empresaDAO);
            validarParcelasFoiCobradasHojeManualmente(movParcelaDAO, convenioCobrancaDAO, pessoaDAO, filtroDTO, lista, configuracaoReenvioMovParcelaEmpresaDAO, empresaDAO);

            EmpresaVO empresaVO = convenioCobrancaVO != null ? convenioCobrancaVO.getEmpresa() : null;
            TipoCobrancaEnum tipoCobrancaEnum = convenioCobrancaVO != null ? convenioCobrancaVO.getTipo().getTipoCobranca() : null;
            movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, tipoCobrancaEnum, lista, Calendario.hoje());

            List<ParcelaDTO> listaFinal = new ArrayList<>();
            Map<Integer, ClienteVO> mapCliente = new HashMap<>();
            for (MovParcelaVO movParcelaVO : lista) {
                ClienteVO clienteVO = mapCliente.get(movParcelaVO.getPessoa().getCodigo());
                if (clienteVO == null) {
                    clienteVO = clienteDAO.consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        mapCliente.put(movParcelaVO.getPessoa().getCodigo(), clienteVO);
                    }
                }

                ParcelaDTO parcelaDTO = new ParcelaDTO(movParcelaVO, clienteVO);
                try {
                    parcelaDTO.setPendenteRetorno(movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)); //metodo pode lançar exceção
                    //O metodo acima valida se a parcela está em Remessa DCC/DCO/Boleto Remessa, não valida boleto online como Pjbank, por isso a condição abaixo
                    if (!parcelaDTO.isPendenteRetorno()) {
                        parcelaDTO.setPendenteRetorno(movParcelaDAO.parcelaEstaBloqueadaPorBoletoPendente(movParcelaVO, false));
                    }
                } catch (Exception ex) {
                    parcelaDTO.setPendenteRetorno(false);
                }
                listaFinal.add(parcelaDTO);
            }

            return EnvelopeRespostaDTO.of(listaFinal, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            movParcelaDAO = null;
            convenioCobrancaDAO = null;
            clienteDAO = null;
            pessoaDAO = null;
            configuracaoReenvioMovParcelaEmpresaDAO = null;
            empresaDAO = null;
            finalizarConexao(con);
        }
    }

    private void validarParcelasQueSeraoCobradasHoje(MovParcela movParcelaDAO, ConvenioCobranca convenioCobrancaDAO, Pessoa pessoaDAO, FiltroPactoPayDTO filtroDTO,
                                                     List<MovParcelaVO> listaParcelas, ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresaDAO,
                                                     Empresa empresaDAO) throws Exception {
        List<TipoConvenioCobrancaEnum> tipoConvenioCobrancaEnumList = TipoConvenioCobrancaEnum.obterListaTipoCobranca(TipoCobrancaEnum.EDI_DCC);
        tipoConvenioCobrancaEnumList.addAll(TipoConvenioCobrancaEnum.obterListaTipoCobranca(TipoCobrancaEnum.ONLINE));
        Integer[] tiposConvenio = tipoConvenioCobrancaEnumList.stream()
                .map(tipoConvenioCobrancaEnumItem -> tipoConvenioCobrancaEnumItem.getCodigo())
                .toArray(Integer[]::new);

        List<ConvenioCobrancaVO> listaConvenioCobrancaVO = new ArrayList<>();
        obterListaConveniosConsultaPactoPay(convenioCobrancaDAO, filtroDTO, configuracaoReenvioMovParcelaEmpresaDAO, empresaDAO, tiposConvenio, listaConvenioCobrancaVO);

        if (!UteisValidacao.emptyList(listaConvenioCobrancaVO) && !UteisValidacao.emptyList(listaParcelas)) {
            Map<Integer, Integer> mapaCodParcelas = new HashMap<>();
            List<MovParcelaVO> listaParcelasCobrarHoje = new ArrayList<>();
            if (!UteisValidacao.emptyList(listaConvenioCobrancaVO)) {
                for (ConvenioCobrancaVO convenioitemLista : listaConvenioCobrancaVO) {
                    listaParcelasCobrarHoje.addAll(movParcelaDAO.consultarParcelasEmAbertoParaPagamento(convenioitemLista, Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS));
                }
            }

            if (!UteisValidacao.emptyList(listaParcelasCobrarHoje)) {
                //preencher mapa
                for (MovParcelaVO movParcelaVO : listaParcelasCobrarHoje) {
                    mapaCodParcelas.put(movParcelaVO.getCodigo(), movParcelaVO.getCodigo());
                }

                //preencher variável booleana de parcelas que serão cobradas Hoje. No front não deixa selecionar parcelas que serão cobradas hoje.
                for (MovParcelaVO parcela : listaParcelas) {
                    if (!UteisValidacao.emptyNumber(mapaCodParcelas.getOrDefault(parcela.getCodigo(), 0))) {
                        if (!alunoPossuiBloqueioDeCobrancaAutomatica(pessoaDAO, parcela)) {
                            parcela.setSeraCobradaHojeAutomaticamente(true);
                        }
                    }
                }
            }
        }
    }

    private static void obterListaConveniosConsultaPactoPay(ConvenioCobranca convenioCobrancaDAO, FiltroPactoPayDTO filtroDTO,
                                                            ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresaDAO, Empresa empresaDAO,
                                                            Integer[] tiposConvenio, List<ConvenioCobrancaVO> listaConvenioCobrancaVO) throws Exception {
        for (Integer codEmpresa : filtroDTO.getEmpresas()) {

            //Consultar todos os convênios da empresa
            List<ConvenioCobrancaVO> listaTodosConveniosEmpresa = convenioCobrancaDAO.consultarPorEmpresaESituacao(
                    codEmpresa, false, Uteis.NIVELMONTARDADOS_TODOS, tiposConvenio, SituacaoConvenioCobranca.ATIVO, false);

            listaTodosConveniosEmpresa = retornarApenasConveniosConfiguradosRetentativaAutomaticaEmpresa(configuracaoReenvioMovParcelaEmpresaDAO, empresaDAO,
                    codEmpresa, listaTodosConveniosEmpresa);

            if (UteisValidacao.emptyList(filtroDTO.getConvenios())) {
                for (ConvenioCobrancaVO conv : listaTodosConveniosEmpresa) {
                    if (conv.getTipo() != null && conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) { //adicionar todos os onlines como se tivessem filtrados
                        listaConvenioCobrancaVO.add(conv);
                    }
                }
            } else {
                for (ConvenioCobrancaVO conv : listaTodosConveniosEmpresa) {
                    if (filtroDTO.getConvenios().contains(conv.getCodigo()) && !conv.isBloquearCobrancaAutomatica()) {
                        listaConvenioCobrancaVO.add(conv); //adicionar somente os que vieram do filtro da tela
                    }
                }
            }
        }
    }

    private void validarParcelasFoiCobradasHojeManualmente(MovParcela movParcelaDAO, ConvenioCobranca convenioCobrancaDAO, Pessoa pessoaDAO, FiltroPactoPayDTO filtroDTO,
                                                     List<MovParcelaVO> listaParcelas, ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresaDAO,
                                                     Empresa empresaDAO) throws Exception {
        List<TipoConvenioCobrancaEnum> tipoConvenioCobrancaEnumList = TipoConvenioCobrancaEnum.obterListaTipoCobranca(TipoCobrancaEnum.EDI_DCC);
        tipoConvenioCobrancaEnumList.addAll(TipoConvenioCobrancaEnum.obterListaTipoCobranca(TipoCobrancaEnum.ONLINE));
        Integer[] tiposConvenio = tipoConvenioCobrancaEnumList.stream()
                .map(tipoConvenioCobrancaEnumItem -> tipoConvenioCobrancaEnumItem.getCodigo())
                .toArray(Integer[]::new);

        List<ConvenioCobrancaVO> listaConvenioCobrancaVO = new ArrayList<>();
        obterListaConveniosConsultaPactoPay(convenioCobrancaDAO, filtroDTO, configuracaoReenvioMovParcelaEmpresaDAO, empresaDAO, tiposConvenio, listaConvenioCobrancaVO);

        if (!UteisValidacao.emptyList(listaConvenioCobrancaVO) && !UteisValidacao.emptyList(listaParcelas)) {
            List<Integer> listaParcelasCobradasHojeManualmente = new ArrayList<>();
            if (!UteisValidacao.emptyList(listaConvenioCobrancaVO)) {
                for (ConvenioCobrancaVO convenioitemLista : listaConvenioCobrancaVO) {
                    listaParcelasCobradasHojeManualmente.addAll(movParcelaDAO.consultarCodigoParcelasCobradasHojeManualmente(convenioitemLista));
                }
            }

            if (!UteisValidacao.emptyList(listaParcelasCobradasHojeManualmente)) {
                //preencher variável booleana de parcelas que serão cobradas Hoje. No front não deixa selecionar parcelas que serão cobradas hoje.
                for (MovParcelaVO parcela : listaParcelas) {
                    if (listaParcelasCobradasHojeManualmente.contains(parcela.getCodigo())) {
                        parcela.setFoiCobradaHojeManualmente(true);
                    }
                }
            }
        }
    }

    private static List<ConvenioCobrancaVO> retornarApenasConveniosConfiguradosRetentativaAutomaticaEmpresa(ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresaDAO,
                                                                                                            Empresa empresaDAO, Integer codEmpresa,
                                                                                                            List<ConvenioCobrancaVO> listaTodosConveniosEmpresa) throws Exception {
        //Consultar se a empresa possui configuração de reenvio automático de cobrança, lista apenas dos convênios configurados e
        //retira da lista completa acima os que não estão configurados.
        Boolean possuiConfiguracaoReenvioMovParcelaEmpresa = empresaDAO.isReenvioAutomaticoDeCobranca(codEmpresa);
        if (possuiConfiguracaoReenvioMovParcelaEmpresa != null && possuiConfiguracaoReenvioMovParcelaEmpresa) {
            List<Integer> listaCodigoConveniosRetentativaAutomaticaEmpresa = new ArrayList<>();
            listaCodigoConveniosRetentativaAutomaticaEmpresa.addAll(configuracaoReenvioMovParcelaEmpresaDAO.consultarApenasCodigoConveniosConfiguracaoReenvioMovParcelaEmpresa(codEmpresa));

            if (!UteisValidacao.emptyList(listaCodigoConveniosRetentativaAutomaticaEmpresa)) {
                List<ConvenioCobrancaVO> listaConveniosConfigurados = new ArrayList<>();
                for (ConvenioCobrancaVO conv : listaTodosConveniosEmpresa) {
                    if (listaCodigoConveniosRetentativaAutomaticaEmpresa.contains(conv.getCodigo())) {
                        listaConveniosConfigurados.add(conv);
                    }
                }
                listaTodosConveniosEmpresa = listaConveniosConfigurados;
            }
        }
        return listaTodosConveniosEmpresa;
    }

    public boolean alunoPossuiBloqueioDeCobrancaAutomatica(Pessoa pessoaDAO, MovParcelaVO movParcelaVO) throws Exception {
        pessoaDAO.obterInformacoesDeBloqueioCobrancaAutomatica(movParcelaVO.getPessoa(), true);
        if (movParcelaVO.getPessoa().getTipoBloqueioCobrancaAutomatica() != null && movParcelaVO.getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS)) {
            return true;
        }
        if (movParcelaVO.getPessoa().getTipoBloqueioCobrancaAutomatica() != null
                && movParcelaVO.getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS)
                && movParcelaVO.getPessoa().getDataBloqueioCobrancaAutomatica() != null
                && Calendario.menor(movParcelaVO.getPessoa().getDataBloqueioCobrancaAutomatica(), movParcelaVO.getDataVencimento())) {
            return true;
        }
        return false;
    }

    private EnvelopeRespostaDTO cobrarParcelas(HttpServletRequest request) throws Exception {
        RemessaService remessaService;
        ConvenioCobranca convenioCobrancaDAO;
        Usuario usuarioDAO;
        MovParcela movParcelaDAO;
        Empresa empresaDAO;
        PagamentoService pagamentoService;
        Connection con = null;
        try {
            con = obterConexao(request);
            String chave = request.getParameter("key");
            Conexao.guardarConexaoForJ2SE(chave, con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            usuarioDAO = new Usuario(con);
            movParcelaDAO = new MovParcela(con);
            empresaDAO = new Empresa(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer convenio = jsonBody.getInt("convenio");
            Integer empresa = jsonBody.getInt("empresa");
            String username = jsonBody.optString("username");
            JSONArray parcelas = jsonBody.getJSONArray("parcelas");
            if(!UteisValidacao.emptyNumber(Uteis.getQtdLimitePactoPay()) &&
                    parcelas.length() > Uteis.getQtdLimitePactoPay()){
                throw new Exception("Não é possível cobrar mais de " + Uteis.getQtdLimitePactoPay() + " parcelas de uma só vez.");
            }
            Integer usuario = jsonBody.optInt("usuario");

            if (UteisValidacao.emptyNumber(convenio)) {
                throw new Exception("Convênio não informado");
            }
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }
            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário não informada");
            }
            if (UteisValidacao.emptyNumber(parcelas.length())) {
                throw new Exception("Selecione pelo menos uma parcela e tente novamente!");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenio, empresa, Uteis.NIVELMONTARDADOS_TODOS);
            validarConvenioCobranca(convenioCobrancaVO);
            String ipCliente = obterIpCliente(request);

            List<MovParcelaVO> listaParcelas = new ArrayList<>();
            List<Integer> parcelasIncorretas = new ArrayList<>();
            Set<Integer> parcelasAdicionadas = new HashSet<>();
            for (int e = 0; e < parcelas.length(); e++) {
                Integer parcela = parcelas.getInt(e);
                try {
                    MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(parcela, Uteis.NIVELMONTARDADOS_MINIMOS);

                        if (!movParcelaVO.getSituacao().equalsIgnoreCase("EA") ||
                                movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                            parcelasIncorretas.add(movParcelaVO.getCodigo());
                            continue;
                        }

                        //validar se a parcela já foi adicionada na lista
                        if (parcelasAdicionadas.contains(movParcelaVO.getCodigo())) {
                            continue;
                        }
                        listaParcelas.add(movParcelaVO);
                        parcelasAdicionadas.add(movParcelaVO.getCodigo());

                } catch (Exception ex) {
                    parcelasIncorretas.add(parcela);
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyList(listaParcelas)) {
                throw new Exception("Nenhuma parcela para realizar a cobrança.");
            }

            String msg = "";
            Integer total = 0;
            if (convenioCobrancaVO.getTipo().isTransacaoOnline()) {
                pagamentoService = new PagamentoService(con, convenioCobrancaVO);
                List<String> msgErro = new ArrayList<>();
                Set<Integer> transacaoCriadas = pagamentoService.processarCobrancaNaoPresencial(listaParcelas, usuarioVO, msgErro, true, OrigemCobrancaEnum.PACTO_PAY_COBRAR, false, ipCliente);
                total = transacaoCriadas.size();
                msg = "Foi criado " + transacaoCriadas.size() + " transações.";
            } else if (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC)) {
                List<String> msgErro = new ArrayList<>();
                remessaService = new RemessaService(con);
                movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, convenioCobrancaVO.getTipo().getTipoCobranca(), listaParcelas, Calendario.hoje());
                List<RemessaVO> remessas = remessaService.preencherRemessas(listaParcelas, convenioCobrancaVO, empresaVO, true, usuarioVO, msgErro);
                for (RemessaVO remessa : remessas) {
                    remessaService.gravarRemessa(remessa, empresaVO);
                }
                total = remessas.size();
                msg = "Foi criado " + remessas.size() + " remessa(s). ";

                //enviar remessas remoto
                msg += remessaService.processoEnvioRemessasGestaoRemessas(convenioCobrancaVO, usuarioVO);
            }

            if (UteisValidacao.emptyNumber(total)) {
                throw new Exception("Nenhuma cobrança realizada");
            }

            return EnvelopeRespostaDTO.of(msg);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            remessaService = null;
            convenioCobrancaDAO = null;
            usuarioDAO = null;
            movParcelaDAO = null;
            empresaDAO = null;
            pagamentoService = null;
            finalizarConexao(con);
        }
    }

    private String obterIpCliente(HttpServletRequest request) {
        String ipCliente = "";
        try {
            String xForwardedForHeader = request.getHeader("X-Forwarded-For");
            if (xForwardedForHeader == null) {
                ipCliente = request.getRemoteAddr();
            } else {
                // As of https://en.wikipedia.org/wiki/X-Forwarded-For
                // The general format of the field is: X-Forwarded-For: client, proxy1, proxy2 ...
                // we only want the client
                ipCliente = new StringTokenizer(xForwardedForHeader, ",").nextToken().trim();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ipCliente;
    }

    private EnvelopeRespostaDTO retentarParcela(HttpServletRequest request) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        Usuario usuarioDAO;
        MovParcela movParcelaDAO;
        AutorizacaoCobrancaCliente autoClienteDAO;
        AutorizacaoCobrancaColaborador autoColaboradorDAO;
        PagamentoService pagamentoService;
        Connection con = null;
        try {
            con = obterConexao(request);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            usuarioDAO = new Usuario(con);
            movParcelaDAO = new MovParcela(con);
            autoClienteDAO = new AutorizacaoCobrancaCliente(con);
            autoColaboradorDAO = new AutorizacaoCobrancaColaborador(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer parcela = jsonBody.getInt("parcela");
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");
            Integer convenio = jsonBody.getInt("usuario");

            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário não informada");
            }
            if (UteisValidacao.emptyNumber(parcela)) {
                throw new Exception("Parcela não informada");
            }

            MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(parcela, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            if (UteisValidacao.emptyNumber(convenio)) {
                AutorizacaoCobrancaClienteVO autoCliente = autoClienteDAO.obterAutorizacaoMaisRecente(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                if (autoCliente != null &&
                        !UteisValidacao.emptyNumber(autoCliente.getCodigo())) {
                    convenio = autoCliente.getConvenio().getCodigo();
                } else {
                    AutorizacaoCobrancaColaboradorVO autoColaborador = autoColaboradorDAO.obterAutorizacaoMaisRecente(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (autoColaborador != null &&
                            !UteisValidacao.emptyNumber(autoColaborador.getCodigo())) {
                        convenio = autoColaborador.getConvenio().getCodigo();
                    }
                }
            }

            if (UteisValidacao.emptyNumber(convenio)) {
                throw new Exception("Convênio não identificado");
            }

            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenio, movParcelaVO.getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            validarConvenioCobranca(convenioCobrancaVO);
            String ipCliente = obterIpCliente(request);

            if (convenioCobrancaVO.getTipo().isTransacaoOnline()) {
                UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                pagamentoService = new PagamentoService(con, convenioCobrancaVO);
                List<String> msgErro = new ArrayList<>();
                List<MovParcelaVO> listaParcelas = new ArrayList<>();
                listaParcelas.add(movParcelaVO);

                Set<Integer> transacaoCriadas = pagamentoService.processarCobrancaNaoPresencial(listaParcelas, usuarioVO, msgErro, true, OrigemCobrancaEnum.PACTO_PAY_RETENTATIVA, false, ipCliente);
                if (UteisValidacao.emptyNumber(transacaoCriadas.size())) {
                    throw new Exception("Nenhuma cobrança realizada");
                }
                return EnvelopeRespostaDTO.of("Foi criado " + transacaoCriadas.size() + " transações.");
            } else {
                throw new Exception("Convênio não é online");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            usuarioDAO = null;
            movParcelaDAO = null;
            autoClienteDAO = null;
            autoColaboradorDAO = null;
            pagamentoService = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO retentarTransacao(HttpServletRequest request) throws Exception {
        Transacao transacaoDAO;
        Empresa empresaDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            empresaDAO = new Empresa(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer transacao = jsonBody.getInt("transacao");
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário não informada");
            }
            if (UteisValidacao.emptyNumber(transacao)) {
                throw new Exception("Transação não informada");
            }

            String ipCliente = obterIpCliente(request);
            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao, Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            EmpresaVO emp = empresaDAO.consultarPorCodigo(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            transacaoVO.setEmpresaVO(emp);
            transacaoVO.setListaParcelas(transacaoDAO.obterParcelasDaTransacao(transacaoVO));

            String retorno = transacaoDAO.retentativaTransacao(transacaoVO, usuarioVO, transacaoVO.getConvenioCobrancaVO().getCodigo(), ipCliente, null);
            return EnvelopeRespostaDTO.of(retorno);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
            empresaDAO = null;
            finalizarConexao(con);
        }
    }

    private void validarConvenioCobranca(ConvenioCobrancaVO obj) throws Exception {
        if (obj.getSituacao().equals(SituacaoConvenioCobranca.INATIVO)) {
            throw new Exception("Convênio \"" + obj.getDescricao() + "\" não está ativo");
        }
    }

    private EnvelopeRespostaDTO obterPendentes(ServletRequest request) throws Exception {
        MovParcela movParcelaDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            movParcelaDAO = new MovParcela(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            return EnvelopeRespostaDTO.of(movParcelaDAO.consultarParcelasEmAbertoPactoPayPendentesTotalizador(null, filtroDTO.getInicioDate(), filtroDTO.getFimDate(), filtroDTO));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            movParcelaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterPendentesLista(ServletRequest request) throws Exception {
        MovParcela movParcelaDAO;
        Cliente clienteDAO;
        Connection con = null;
        ConvenioCobranca convenioCobrancaDAO;
        Pessoa pessoaDAO;
        ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresaDAO;
        Empresa empresaDAO;

        try {
            con = obterConexao(request);
            movParcelaDAO = new MovParcela(con);
            clienteDAO = new Cliente(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            pessoaDAO = new Pessoa(con);
            configuracaoReenvioMovParcelaEmpresaDAO = new ConfiguracaoReenvioMovParcelaEmpresa(con);
            empresaDAO = new Empresa(con);

            OperacaoRetornoCobrancaEnum operacaoRetornoCobrancaEnum = OperacaoRetornoCobrancaEnum.obterPorCodigo(Integer.parseInt(request.getParameter("operacao")));
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            List<MovParcelaVO> lista = movParcelaDAO.consultarParcelasEmAbertoPactoPayPendentesLista(null, filtroDTO.getInicioDate(), filtroDTO.getFimDate(), paginadorDTO,
                    operacaoRetornoCobrancaEnum, filtroDTO);

            validarParcelasQueSeraoCobradasHoje(movParcelaDAO, convenioCobrancaDAO, pessoaDAO, filtroDTO, lista, configuracaoReenvioMovParcelaEmpresaDAO, empresaDAO);
            validarParcelasFoiCobradasHojeManualmente(movParcelaDAO, convenioCobrancaDAO, pessoaDAO, filtroDTO, lista, configuracaoReenvioMovParcelaEmpresaDAO, empresaDAO);

//            EmpresaVO empresaVO = convenioCobrancaVO != null ? convenioCobrancaVO.getEmpresa() : null;
//            TipoCobrancaEnum tipoCobrancaEnum = convenioCobrancaVO != null ? convenioCobrancaVO.getTipo().getTipoCobranca() : null;
//            movParcelaDAO.montarMultaJurosParcelaVencidaTransacaoDCCDCOTransacao(empresaVO, tipoCobrancaEnum, lista, Calendario.hoje());

            List<ParcelaDTO> listaFinal = new ArrayList<>();
            Map<Integer, ClienteVO> mapCliente = new HashMap<>();
            for (MovParcelaVO movParcelaVO : lista) {
                ClienteVO clienteVO = mapCliente.get(movParcelaVO.getPessoa().getCodigo());
                if (clienteVO == null) {
                    clienteVO = clienteDAO.consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        mapCliente.put(movParcelaVO.getPessoa().getCodigo(), clienteVO);
                    }
                }
                ParcelaDTO parcelaDTO = new ParcelaDTO(movParcelaVO, clienteVO);
                try {
                    parcelaDTO.setPendenteRetorno(movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO));
                } catch (Exception ex) {
                    parcelaDTO.setPendenteRetorno(true);
                }
                obterDadosContratoParcela(parcelaDTO, con);
                listaFinal.add(parcelaDTO);
            }

            return EnvelopeRespostaDTO.of(listaFinal, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            movParcelaDAO = null;
            clienteDAO = null;
            convenioCobrancaDAO = null;
            pessoaDAO = null;
            configuracaoReenvioMovParcelaEmpresaDAO = null;
            empresaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterRemessas(ServletRequest request) throws Exception {
        Remessa remessaDAO;
        RemessaItem remessaItemDAO;
        RemessaService remessaService;
        Connection con = null;
        try {
            con = obterConexao(request);
            remessaDAO = new Remessa(con);
            remessaItemDAO = new RemessaItem(con);

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);
            List<RemessaDTO> listaRemessasRetorno = new ArrayList<>();

            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);

            Integer[] tiposRemessaEnum = new Integer[]{TipoRemessaEnum.EDI_CIELO.getId(), TipoRemessaEnum.GET_NET.getId(), TipoRemessaEnum.DCC_BIN.getId()};
            List<RemessaVO> listaRemessas = remessaDAO.consultar(filtroDTO.getInicioDate(), filtroDTO.getFimDate(), filtroDTO.getEmpresas(), filtroDTO.getConvenios(),
                    null, false, paginadorDTO, tiposRemessaEnum);
            if (UteisValidacao.emptyList(listaRemessas)) {
                throw new Exception("Nenhum registro encontrado.");
            } else {
                remessaService = new RemessaService(con);
                for (RemessaVO remessaVO : listaRemessas) {
                    remessaService.getL().lerHeaderETrailerRemessa(remessaVO);
                    LayoutRemessaBase.lerRetorno(remessaVO);
                    remessaVO.setValorAceito(remessaItemDAO.consultarPorCodigoValorRemessaAceito(remessaVO));
                    listaRemessasRetorno.add(new RemessaDTO(remessaVO));
                }
            }

            return EnvelopeRespostaDTO.of(listaRemessasRetorno, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            remessaDAO = null;
            remessaItemDAO = null;
            remessaService = null;
            CacheControl.clear();
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterItensRemessa(ServletRequest request) throws Exception {
        RemessaItem remessaItemDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            remessaItemDAO = new RemessaItem(con);

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            List<RemessaItemDTO> retorno = new ArrayList<>();

            Integer remessa = UteisValidacao.converterInteiro(request.getParameter("remessa"));

            List<RemessaItemVO> itens = remessaItemDAO.consultarPorCodigoRemessa(remessa, paginadorDTO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (RemessaItemVO itemVO : itens) {
                RemessaItemDTO remessaItemDTO = new RemessaItemDTO(itemVO);
                remessaItemDTO.setQtdParcelas(remessaItemDAO.consultarQtdParcelasItem(itemVO.getCodigo()));
                retorno.add(remessaItemDTO);
            }
            return EnvelopeRespostaDTO.of(retorno, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            remessaItemDAO = null;
            finalizarConexao(con);
        }
    }

    private void imprimirTransacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Transacao transacaoDAO;
        Empresa empresaDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            empresaDAO = new Empresa(con);

            Integer transacao = UteisValidacao.converterInteiro(request.getParameter("transacao"));
            TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
            transacaoVO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            TransacaoImpressaoTO transacaoImpressaoTO = new TransacaoImpressaoTO(transacaoVO);

            String arquivo = gerarArquivoPDFEstornoTransacao(transacaoImpressaoTO, request);
            File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
            String nomeDownload = "COMPROVANTE-" + transacaoImpressaoTO.getCodigoExterno() + ".pdf";

            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
            response.setContentLength((int) pdfFile.length());

            FileInputStream fileInputStream = new FileInputStream(pdfFile);
            OutputStream responseOutputStream = response.getOutputStream();
            int bytes;
            while ((bytes = fileInputStream.read()) != -1) {
                responseOutputStream.write(bytes);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
            empresaDAO = null;
            finalizarConexao(con);
        }
    }

    private String gerarArquivoPDFEstornoTransacao(TransacaoImpressaoTO transacaoImpressaoTO, HttpServletRequest request) {
        List<TransacaoImpressaoTO> lista = new ArrayList<TransacaoImpressaoTO>();
        lista.add(transacaoImpressaoTO);

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "transacao" + File.separator + "ComprovanteCancelamentoTransacao.jrxml");
        params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "transacao" + File.separator);
        params.put("tipoRelatorio", "PDF");
        params.put("tipoImplementacao", "OBJETO");
        params.put("nomeRelatorio", "COMPROVANTE");
        params.put("nomeEmpresa", "");
        params.put("listaObjetos", lista);
        return new SuperControleRelatorio().imprimirRetornaNomeArquivo(request, params);
    }

    private EnvelopeRespostaDTO bloquearDesbloquearCobrancas(ServletRequest request, boolean bloquear) throws Exception {
        Pessoa pessoaDAO;
        Usuario usuarioDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            pessoaDAO = new Pessoa(con);
            usuarioDAO = new Usuario(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));
            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("u"));
            String username = request.getParameter("username");

            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Pessoa não informada");
            }

            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário não informado");
            }

            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            pessoaDAO.obterInformacoesDeBloqueioCobrancaAutomatica(pessoaVO);

            if (bloquear) {
                if (pessoaVO.getDataBloqueioCobrancaAutomatica() != null) {
                    throw new Exception("Cobranças do cliente já está bloqueada");
                }
                TipoBloqueioCobrancaEnum tipo = TipoBloqueioCobrancaEnum.obterTipoBloqueioCobrancaEnum(Integer.parseInt(request.getParameter("tipo")));
                Date dataBloqueio = Calendario.hoje();
                if (tipo.equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS)) {
                    dataBloqueio = UteisValidacao.converterData(request.getParameter("data"), "yyyyMMdd");
                }
                pessoaDAO.alterarDataBloqueioCobrancaAutomatica(dataBloqueio, tipo, pessoaVO.getCodigo(),
                        usuarioVO, true, "Bloqueio - PactoPay");
                return EnvelopeRespostaDTO.of("Cobranças bloqueadas");
            } else {
                if (pessoaVO.getDataBloqueioCobrancaAutomatica() == null) {
                    throw new Exception("Cobranças do cliente já está desbloqueada");
                }
                pessoaDAO.alterarDataBloqueioCobrancaAutomatica(null, null, pessoaVO.getCodigo(),
                        usuarioVO, true, "Desbloqueio - PactoPay");
                return EnvelopeRespostaDTO.of("Cobranças desbloqueadas");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pessoaDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO excluirAutorizacaoCobranca(ServletRequest request) throws Exception {
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO;
        Usuario usuarioDAO;
        ClienteMensagem clienteMensagemDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            con.setAutoCommit(false);
            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);
            usuarioDAO = new Usuario(con);
            clienteMensagemDAO = new ClienteMensagem(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));

            Integer autorizacao = jsonBody.getInt("codigo");
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            if (UteisValidacao.emptyNumber(autorizacao)) {
                throw new Exception("Código da Autorização não informada");
            }
            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário responsável não informado");
            }

            AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = autorizacaoCobrancaClienteDAO.consultarPorChavePrimaria(autorizacao, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (!autorizacaoCobrancaClienteVO.isAtiva()) {
                throw new Exception("Autorização de cobrança não está ativa");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(false, autorizacaoCobrancaClienteVO, OrigemCobrancaEnum.PACTO_PAY.getDescricao(), usuarioVO);

            //processar mensagem cartão vencido
            clienteMensagemDAO.processarMensagensCartaoVencidoCliente(autorizacaoCobrancaClienteVO.getCliente().getCodigo(), usuarioVO);

            con.commit();
            return EnvelopeRespostaDTO.of("Autorização de cobrança excluida");
        } catch (Exception ex) {
            if (con != null) {
                con.rollback();
            }
            ex.printStackTrace();
            throw ex;
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
            autorizacaoCobrancaClienteDAO = null;
            usuarioDAO = null;
            clienteMensagemDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO incluirAutorizacaoCobranca_anterior(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        Cliente clienteDAO;
        ConvenioCobranca convenioCobrancaDAO;
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO;
        ClienteMensagem clienteMensagemDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            con.setAutoCommit(false);
            usuarioDAO = new Usuario(con);
            clienteDAO = new Cliente(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);
            clienteMensagemDAO = new ClienteMensagem(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));

            Integer pessoa = jsonBody.getInt("pessoa");
            Integer convenio = jsonBody.getInt("convenio");
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Pessoa não informada");
            }

            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário responsável não informado");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenio, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            AutorizacaoCobrancaClienteVO obj = new AutorizacaoCobrancaClienteVO();
            Integer autorizacaoEditar = jsonBody.optInt("codigo");
            if (!UteisValidacao.emptyNumber(autorizacaoEditar)) {
                obj = autorizacaoCobrancaClienteDAO.consultarPorChavePrimaria(autorizacaoEditar, Uteis.NIVELMONTARDADOS_TODOS);
                if (!obj.isAtiva()) {
                    throw new Exception("Autorização não está ativa");
                }
                obj.setObjetoVOAntesAlteracao(obj.getClone(true));

                if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                    obterNazgDTO(obj);
                }
            }

            obj.setUsuarioVO(usuarioVO);
            obj.setCliente(clienteVO);
            obj.setConvenio(convenioCobrancaVO);
            obj.setOrigemCobrancaEnum(UteisValidacao.emptyNumber(jsonBody.optInt("origem")) ? OrigemCobrancaEnum.PACTO_PAY : OrigemCobrancaEnum.obterPorCodigo(jsonBody.getInt("origem")));
            obj.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.valueOf(jsonBody.getInt("tipo")));
            obj.setNomeTitularCartao(jsonBody.optString("titular"));
            obj.setCvv(jsonBody.optString("cvv"));
            obj.setNumeroCartao(jsonBody.optString("card").replace("_", "").replace(" ", ""));
            obj.setValidadeCartao(jsonBody.optString("vencimento"));
            obj.setCpfTitular(jsonBody.optString("documento"));
            obj.setVencimentoFatura(jsonBody.optInt("fatura"));
            obj.setClienteTitularCartao(jsonBody.optBoolean("clienteTitular"));
            obj.setTipoACobrar(TipoObjetosCobrarEnum.valueOf(jsonBody.optInt("tipoCobrar")));

            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                //boleto sempre cobra tudo
                obj.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
            }

            if (obj.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                JSONArray arrayProdutos = jsonBody.optJSONArray("produtos");
                if (arrayProdutos == null || arrayProdutos.length() == 0) {
                    throw new Exception("Informe os produtos que serão cobrados");
                }
                StringBuilder listaProdutos = new StringBuilder();
                for (int e = 0; e < arrayProdutos.length(); e++) {
                    TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(arrayProdutos.getString(e));
                    if (tipoProduto == null) {
                        throw new Exception("Tipo Produto não encontrado");
                    }
                    listaProdutos.append(",").append(tipoProduto.getCodigo());
                }
                obj.setListaObjetosACobrar(listaProdutos.toString().replaceFirst(",", ""));
            } else {
                obj.setListaObjetosACobrar("");
            }

            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
                obj.setAgencia(Integer.parseInt(jsonBody.optString("agencia")));
                obj.setAgenciaDV(jsonBody.optString("agenciadv"));
                obj.setContaCorrente(new Long(jsonBody.optString("contacorrente")));
                obj.setContaCorrenteDV(jsonBody.optString("contacorrentedv"));
                obj.setAutorizarClienteDebito(jsonBody.optBoolean("autorizarclientedebito"));
                obj.setBanco(convenioCobrancaVO.getBanco());
            }

            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                    !obj.getNumeroCartao().contains("****")) {
                CreditCardValidator cartaoCredito = new CreditCardValidator();
                obj.setOperadoraCartao(cartaoCredito.operadora(
                        obj.getNumeroCartao()) == null ? null : OperadorasExternasAprovaFacilEnum.valueOf(cartaoCredito.operadora(obj.getNumeroCartao())));
                if (obj.getOperadoraCartao() == null) {
                    try {
                        UteisValidacao.validarNumeroCartaoCreditoElo(obj.getNumeroCartao());
                        obj.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.ELO);
                    } catch (Exception e) {
                    }
                }
                if (obj.getOperadoraCartao() == null) {
                    throw new Exception("Cartão inválido, revise os dados do cartão e tente novamente.");
                }
            }

            if (UteisValidacao.emptyNumber(obj.getCodigo())) {
                autorizacaoCobrancaClienteDAO.incluir(obj, usuarioVO);
            } else {
                autorizacaoCobrancaClienteDAO.alterar(obj, usuarioVO);
            }

            //processar mensagem cartão vencido
            clienteMensagemDAO.processarMensagensCartaoVencidoCliente(obj.getCliente().getCodigo(), usuarioVO);

            con.commit();
            return EnvelopeRespostaDTO.of(UteisValidacao.emptyNumber(autorizacaoEditar) ? "Autorização incluída com sucesso." : "Autorização alterada com sucesso.");
        } catch (Exception ex) {
            if (con != null) {
                con.rollback();
            }
            ex.printStackTrace();
            throw ex;
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
            usuarioDAO = null;
            clienteDAO = null;
            autorizacaoCobrancaClienteDAO = null;
            clienteMensagemDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO incluirAutorizacaoCobranca(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        Cliente clienteDAO;
        ConvenioCobranca convenioCobrancaDAO;
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO;
        ClienteMensagem clienteMensagemDAO;
        Transacao transacaoDAO;
        Empresa empresaDAO;
        Connection con = null;
        RespostaCartaoDTO respostaCartaoDTO = new RespostaCartaoDTO();
        try {
            con = obterConexao(request);
            con.setAutoCommit(false);
            usuarioDAO = new Usuario(con);
            clienteDAO = new Cliente(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            transacaoDAO = new Transacao(con);
            empresaDAO = new Empresa(con);
            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);
            clienteMensagemDAO = new ClienteMensagem(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));

            Integer pessoa = jsonBody.getInt("pessoa");
            Integer convenio = jsonBody.getInt("convenio");
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");
            Boolean usarIdVindi = jsonBody.optBoolean("usarIdVindi");

            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Pessoa não informada");
            }

            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário responsável não informado");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenio, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            AutorizacaoCobrancaClienteVO obj = new AutorizacaoCobrancaClienteVO();
            Integer autorizacaoEditar = jsonBody.optInt("codigo");
            if (!UteisValidacao.emptyNumber(autorizacaoEditar)) {
                obj = autorizacaoCobrancaClienteDAO.consultarPorChavePrimaria(autorizacaoEditar, Uteis.NIVELMONTARDADOS_TODOS);
                if (!obj.isAtiva()) {
                    throw new Exception("Autorização não está ativa");
                }
                obj.setObjetoVOAntesAlteracao(obj.getClone(true));

                if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) && !usarIdVindi) {
                    obterNazgDTO(obj);
                }
            }

            obj.setUsuarioVO(usuarioVO);
            obj.setCliente(clienteVO);
            obj.setConvenio(convenioCobrancaVO);
            obj.setOrigemCobrancaEnum(UteisValidacao.emptyNumber(jsonBody.optInt("origem")) ? OrigemCobrancaEnum.PACTO_PAY : OrigemCobrancaEnum.obterPorCodigo(jsonBody.getInt("origem")));
            obj.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.valueOf(jsonBody.getInt("tipo")));
            obj.setNomeTitularCartao(jsonBody.optString("titular"));
            obj.setCvv(jsonBody.optString("cvv"));
            obj.setNumeroCartao(jsonBody.optString("card").replace("_", "").replace(" ", ""));
            obj.setValidadeCartao(jsonBody.optString("vencimento"));
            obj.setCpfTitular(jsonBody.optString("documento"));
            obj.setVencimentoFatura(jsonBody.optInt("fatura"));
            obj.setClienteTitularCartao(jsonBody.optBoolean("clienteTitular"));
            obj.setTipoACobrar(TipoObjetosCobrarEnum.valueOf(jsonBody.optInt("tipoCobrar")));
            obj.setUsarIdVindiPessoa(usarIdVindi);

            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                //boleto sempre cobra tudo
                obj.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
            }

            if (obj.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                JSONArray arrayProdutos = jsonBody.optJSONArray("produtos");
                if (arrayProdutos == null || arrayProdutos.length() == 0) {
                    throw new Exception("Informe os produtos que serão cobrados");
                }
                StringBuilder listaProdutos = new StringBuilder();
                for (int e = 0; e < arrayProdutos.length(); e++) {
                    TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(arrayProdutos.getString(e));
                    if (tipoProduto == null) {
                        throw new Exception("Tipo Produto não encontrado");
                    }
                    listaProdutos.append(",").append(tipoProduto.getCodigo());
                }
                obj.setListaObjetosACobrar(listaProdutos.toString().replaceFirst(",", ""));
            } else {
                obj.setListaObjetosACobrar("");
            }

            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
                obj.setAgencia(Integer.parseInt(jsonBody.optString("agencia")));
                obj.setAgenciaDV(jsonBody.optString("agenciadv"));
                obj.setContaCorrente(new Long(jsonBody.optString("contacorrente")));
                obj.setContaCorrenteDV(jsonBody.optString("contacorrentedv"));
                obj.setAutorizarClienteDebito(jsonBody.optBoolean("autorizarclientedebito"));
                obj.setBanco(convenioCobrancaVO.getBanco());
            }

            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                    !obj.getNumeroCartao().contains("****")) {
                obj.setCartaoMascarado(APF.getCartaoMascarado(obj.getNumeroCartao()));
                if (!obj.isUsarIdVindiPessoa()) {
                    CreditCardValidator cartaoCredito = new CreditCardValidator();
                    obj.setOperadoraCartao(cartaoCredito.operadora(
                            obj.getNumeroCartao()) == null ? null : OperadorasExternasAprovaFacilEnum.valueOf(cartaoCredito.operadora(obj.getNumeroCartao())));
                    if (obj.getOperadoraCartao() == null) {
                        try {
                            UteisValidacao.validarNumeroCartaoCreditoElo(obj.getNumeroCartao());
                            obj.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.ELO);
                        } catch (Exception e) {
                        }
                    }
                    if (obj.getOperadoraCartao() == null) {
                        throw new Exception("Cartão inválido, revise os dados do cartão e tente novamente.");
                    }
                }
            }

            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                if (jsonBody.optBoolean("verificar", true)) {
                    JSONObject jsonRetorno = transacaoDAO.realizaCobrancaVerificarCartao(clienteVO, null, obj, usuarioVO, jsonBody.optString("ipcliente"));
                    respostaCartaoDTO.setSucesso(!jsonRetorno.getBoolean("erro"));
                    if (!respostaCartaoDTO.isSucesso()) {
                        respostaCartaoDTO.setApresentarConfirmar(jsonRetorno.getString("modal").equalsIgnoreCase("show"));
                        if (respostaCartaoDTO.getApresentarConfirmar()) {
                            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                            respostaCartaoDTO.setMensagem(jsonRetorno.optString("message"));
                            respostaCartaoDTO.setPermiteAdicionar(empresaVO.isPermiteCadastrarCartaoMesmoAssim());
                            con.commit();
                            return EnvelopeRespostaDTO.of(respostaCartaoDTO);
                        }
                    }
                }
            }

            if (UteisValidacao.emptyNumber(obj.getCodigo())) {
                autorizacaoCobrancaClienteDAO.incluir(obj, usuarioVO);
            } else {

                AutorizacaoCobrancaClienteVO objExiste = autorizacaoCobrancaClienteDAO.consultarPorChavePrimaria(autorizacaoEditar, Uteis.NIVELMONTARDADOS_TODOS);
                if (objExiste.getNumeroCartao().equalsIgnoreCase(obj.getNumeroCartao()) &&
                        objExiste.getNomeTitularCartao().equalsIgnoreCase(obj.getNomeTitularCartao()) &&
                        objExiste.getCpfTitular().equalsIgnoreCase(obj.getCpfTitular()) &&
                        (objExiste.isClienteTitularCartao() == obj.isClienteTitularCartao())) {

                    if (!UteisValidacao.emptyString(obj.getValidadeCartao()) && obj.getValidadeCartao().length() == 5) {
                        obj.setValidadeCartao(obj.getValidadeCartao().replace("/", "/20"));
                    }

                    if (objExiste.getValidadeCartao().equalsIgnoreCase(obj.getValidadeCartao())) {
                        boolean alterouTipoACobrar = false;

                        //validar se alterou somente o tipo a cobrar
                        if (!objExiste.getTipoACobrar().equals(obj.getTipoACobrar())) {
                            autorizacaoCobrancaClienteDAO.alterarTipoACobrar(obj);
                            String valorAnterior = (objExiste.getTipoACobrar().getId() + " - " + objExiste.getTipoACobrar().getDescricao());
                            if (objExiste.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                                valorAnterior += (" | " + (objExiste.getListaObjetosACobrar() == null ? "" : objExiste.getListaObjetosACobrar()));
                            }

                            String valorAlterado = (obj.getTipoACobrar().getId() + " - " + obj.getTipoACobrar().getDescricao());
                            if (obj.getTipoACobrar().equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                                valorAlterado += (" | " + (obj.getListaObjetosACobrar()));
                            }
                            autorizacaoCobrancaClienteDAO.registrarLog(obj.getCodigo(), obj.getCliente().getPessoa().getCodigo(),
                                    "TipoACobrar", valorAnterior, valorAlterado, usuarioVO);
                            alterouTipoACobrar = true;
                        }
                        boolean alterouConvenio = false;
                        //validar se alterou somente convênio
                        if (!objExiste.getConvenio().getCodigo().equals(obj.getConvenio().getCodigo()) &&
                                !UteisValidacao.emptyString(objExiste.getTokenAragorn())) {
                            autorizacaoCobrancaClienteDAO.alterarConvenioCobranca(obj);
                            String valorAnterior = (objExiste.getConvenio().getCodigo() + " - " + objExiste.getConvenio().getDescricao());
                            String valorAlterado = (obj.getConvenio().getCodigo() + " - " + obj.getConvenio().getDescricao());
                            autorizacaoCobrancaClienteDAO.registrarLog(obj.getCodigo(), obj.getCliente().getPessoa().getCodigo(),
                                    "ConvenioCobranca", valorAnterior, valorAlterado, usuarioVO);
                            alterouConvenio = true;
                        }

                        if (alterouConvenio && alterouTipoACobrar) {
                            con.commit();
                            respostaCartaoDTO.setCodigo(obj.getCodigo());
                            respostaCartaoDTO.setSucesso(true);
                            respostaCartaoDTO.setMensagem("Tipo de parcelas a cobrar e convênio da cobrança alterados com sucesso.");
                            return EnvelopeRespostaDTO.of(respostaCartaoDTO);
                        }
                        if (alterouConvenio) {
                            con.commit();
                            respostaCartaoDTO.setCodigo(obj.getCodigo());
                            respostaCartaoDTO.setSucesso(true);
                            respostaCartaoDTO.setMensagem("Convênio da cobrança alterado com sucesso.");
                            return EnvelopeRespostaDTO.of(respostaCartaoDTO);
                        }
                        if (alterouTipoACobrar) {
                            con.commit();
                            respostaCartaoDTO.setCodigo(obj.getCodigo());
                            respostaCartaoDTO.setSucesso(true);
                            respostaCartaoDTO.setMensagem("Tipo de parcelas a cobrar alterado com sucesso.");
                            return EnvelopeRespostaDTO.of(respostaCartaoDTO);
                        }

                    }
                }
                autorizacaoCobrancaClienteDAO.alterar(obj, usuarioVO);
            }

            //processar mensagem cartão vencido
            clienteMensagemDAO.processarMensagensCartaoVencidoCliente(obj.getCliente().getCodigo(), usuarioVO);

            con.commit();
            respostaCartaoDTO.setCodigo(obj.getCodigo());
            respostaCartaoDTO.setSucesso(true);
            respostaCartaoDTO.setMensagem(UteisValidacao.emptyNumber(autorizacaoEditar) ? "Autorização incluída com sucesso." : "Autorização alterada com sucesso.");
            return EnvelopeRespostaDTO.of(respostaCartaoDTO);
        } catch (Exception ex) {
            if (con != null) {
                con.rollback();
            }
            ex.printStackTrace();
            throw ex;
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
            }
            usuarioDAO = null;
            clienteDAO = null;
            autorizacaoCobrancaClienteDAO = null;
            clienteMensagemDAO = null;
            empresaDAO = null;
            transacaoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterBandeira(ServletRequest request) {
        BandeiraCartaoDTO dto = new BandeiraCartaoDTO();
        try {
            JSONObject jsonBody = new JSONObject(obterBody(request));
            String card = jsonBody.optString("card");

            if (UteisValidacao.emptyString(card)) {
                throw new Exception("Cartão não informado");
            }

            String numeroCartao = card.replaceAll(" ", "");
            dto.setCard_mask(APF.getCartaoMascarado(numeroCartao));

            OperadorasExternasAprovaFacilEnum operadora = null;
            if (ValidaBandeira.numeroCartaoValido(numeroCartao)) {
                ValidaBandeira.Bandeira bandeiraCard = ValidaBandeira.buscarBandeira(numeroCartao);
                String bandeiraCartao = String.valueOf(bandeiraCard);
                operadora = Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, bandeiraCartao.toUpperCase());
            } else {
                throw new Exception("Cartão inválido");
            }
            dto.setValido(true);
            dto.setMensagem("Cartão válido");
            dto.setBandeira(operadora.getDescricao().toUpperCase());
        } catch (Exception ex) {
            dto.setValido(false);
            dto.setMensagem(ex.getMessage());
        }
        return EnvelopeRespostaDTO.of(dto);
    }

    private void obterNazgDTO(AutorizacaoCobrancaVO obj) throws Exception {
        AragornService aragornService = new AragornService();
        obj.setNazgDTO(aragornService.obterNazg(obj.getTokenAragorn()));
        aragornService = null;
    }

    private EnvelopeRespostaDTO obterParcelasTelaCliente(ServletRequest request) throws Exception {
        MovParcela movParcelaDAO;
        Cliente clienteDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            movParcelaDAO = new MovParcela(con);
            clienteDAO = new Cliente(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            Integer total = movParcelaDAO.obterCountParcelaCliente(pessoa, null);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<ParcelaClienteDTO> listaFinal = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(total)) {
                List<MovParcelaVO> lista = movParcelaDAO.consultarTelaCliente(pessoa, null, maxResults, indiceInicial, "codigo", true);

                Map<Integer, ClienteVO> mapCliente = new HashMap<>();
                for (MovParcelaVO movParcelaVO : lista) {
                    ClienteVO clienteVO = mapCliente.get(movParcelaVO.getPessoa().getCodigo());
                    if (clienteVO == null) {
                        clienteVO = clienteDAO.consultarPorCodigoPessoa(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                            mapCliente.put(movParcelaVO.getPessoa().getCodigo(), clienteVO);
                        }
                    }

                    ParcelaClienteDTO parcelaClienteDTO = new ParcelaClienteDTO(movParcelaVO, clienteVO);

                    StringBuilder sql = new StringBuilder();
                    sql.append("select \n");
                    sql.append("c.codigo as contrato, \n");
                    sql.append("c.vigenciade as inicio, \n");
                    sql.append("c.vigenciaateajustada as fim, \n");
                    sql.append("e.nome as empresa, \n");
                    sql.append("pl.descricao as plano, \n");
                    sql.append("fp.descricao as formapagamento, \n");
                    sql.append("us.nome as usuariopagamento, \n");
                    sql.append("us.username as username, \n");
                    sql.append("pm.recibopagamento as recibo, \n");
                    sql.append("mp.nrtentativas \n");
                    sql.append("from movparcela mp \n");
                    sql.append("inner join empresa e on e.codigo = mp.empresa \n");
                    sql.append("left join contrato c on c.codigo = mp.contrato \n");
                    sql.append("left join plano pl on pl.codigo = c.plano \n");
                    sql.append("left join pagamentomovparcela pm on pm.movparcela = mp.codigo \n");
                    sql.append("left join movpagamento mov on mov.codigo = pm.movpagamento \n");
                    sql.append("left join formapagamento fp on fp.codigo = mov.formapagamento \n");
                    sql.append("left join usuario us on us.codigo = mov.responsavelpagamento \n");
                    sql.append("where mp.codigo = ").append(parcelaClienteDTO.getCodigo());

                    try (Statement stm = con.createStatement()) {
                        try (ResultSet rs = stm.executeQuery(sql.toString())) {
                            if (rs.next()) {
                                Integer contrato = rs.getInt("contrato");
                                if (!UteisValidacao.emptyNumber(contrato)) {
                                    parcelaClienteDTO.setContrato(contrato);
                                    parcelaClienteDTO.setInicioContrato(Calendario.getDataAplicandoFormatacao(rs.getDate("inicio"), "dd/MM/yyyy"));
                                    parcelaClienteDTO.setFimContrato(Calendario.getDataAplicandoFormatacao(rs.getDate("fim"), "dd/MM/yyyy"));
                                    parcelaClienteDTO.setPlano(rs.getString("plano"));
                                }

                                parcelaClienteDTO.setNrTentativas(rs.getInt("nrtentativas"));
                                parcelaClienteDTO.setRecibo(rs.getInt("recibo"));
                                parcelaClienteDTO.setEmpresa(rs.getString("empresa"));
                                parcelaClienteDTO.setFormaPagamento(rs.getString("formapagamento"));
                                parcelaClienteDTO.setUsuarioPagamento(rs.getString("usuariopagamento"));

                                String usuariousername = rs.getString("username");
                                parcelaClienteDTO.setTipoOperacao("");
                                if (usuariousername != null &&
                                        (usuariousername.equalsIgnoreCase("admin") ||
                                                usuariousername.equalsIgnoreCase("RECOR"))) {
                                    parcelaClienteDTO.setTipoOperacao("Automático");
                                } else {
                                    parcelaClienteDTO.setTipoOperacao("Manual");
                                }
                            }
                        }
                    }
                    listaFinal.add(parcelaClienteDTO);
                }
            }
            return EnvelopeRespostaDTO.of(listaFinal, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
            movParcelaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterCobrancasParcela(ServletRequest request) throws Exception {
        Transacao transacaoDAO;
        Usuario usuarioDAO;
        ConvenioCobranca convenioCobrancaDAO;
        RemessaItem remessaItemDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            remessaItemDAO = new RemessaItem(con);

            Integer parcela = UteisValidacao.converterInteiro(request.getParameter("p"));

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;

            StringBuilder sql = new StringBuilder();
            sql.append("select * from ( \n");
            sql.append("select  \n");
            sql.append("'TRANSACAO' as tipo, \n");
            sql.append("t.codigo, \n");
            sql.append("t.dataprocessamento as data, \n");
            sql.append("(select count(*) from transacaomovparcela  where transacao = t.codigo) as qtdparcelas \n");
            sql.append("FROM Transacao t \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = t.conveniocobranca \n");
            sql.append("WHERE t.codigo in ( \n");
            sql.append("select  \n");
            sql.append("t.codigo  \n");
            sql.append("from transacao t  \n");
            sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo  \n");
            sql.append("inner join movparcela mp on mp.codigo = tm.movparcela  \n");
            sql.append("where mp.codigo = ").append(parcela).append(") \n");
            sql.append("union \n");
            sql.append("select  \n");
            sql.append("'REMESSA' as tipo, \n");
            sql.append("ri.codigo, \n");
            sql.append("r.dataregistro as data, \n");
            sql.append("0 as qtdparcelas \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("INNER JOIN remessa r on r.codigo = ri.remessa \n");
            sql.append("WHERE ri.codigo in ( \n");
            sql.append("select  \n");
            sql.append("distinct(ri.codigo) as codigo \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("INNER JOIN remessa r on r.codigo = ri.remessa \n");
            sql.append("WHERE r.tipo in (2,8,12) \n");
            sql.append("and coalesce(ri.movparcela,0) = ").append(parcela).append(" \n");
            sql.append("union  \n");
            sql.append("select  \n");
            sql.append("distinct(ri.codigo) as codigo \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql.append("inner join movparcela mp on mp.codigo = coalesce(ri.movparcela,0) \n");
            sql.append("where r.tipo in (2,8,12) \n");
            sql.append("and mp.codigo = ").append(parcela).append(" \n");
            sql.append("union  \n");
            sql.append("select  \n");
            sql.append("distinct(ri.codigo) as codigo \n");
            sql.append("FROM remessaitem ri  \n");
            sql.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql.append("inner join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
            sql.append("inner join movparcela mp on mp.codigo = coalesce(rim.movparcela,0) \n");
            sql.append("where r.tipo in (2,8,12) \n");
            sql.append("and mp.codigo = ").append(parcela).append(") \n");
            sql.append(") as sql \n");
            sql.append("order by sql.data desc \n");

            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<CobrancaDTO> lista = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(total)) {

                sql.append("LIMIT ").append(maxResults).append(" \n");
                sql.append("OFFSET ").append(indiceInicial).append(" \n");

                Map<Integer, UsuarioVO> mapaUsuario = new HashMap<>();
                Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();

                try (Statement stm = con.createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sql.toString())) {
                        while (rs.next()) {
                            try {
                                String tipo = rs.getString("tipo");
                                int codigo = rs.getInt("codigo");

                                CobrancaDTO cobrancaDTO = null;
                                if (tipo.equalsIgnoreCase("TRANSACAO")) {
                                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_CONSULTA_PACTO_PAY);

                                    if (!UteisValidacao.emptyNumber(transacaoVO.getUsuarioResponsavel().getCodigo())) {
                                        UsuarioVO usuarioVO = mapaUsuario.get(transacaoVO.getUsuarioResponsavel().getCodigo());
                                        if (usuarioVO == null) {
                                            usuarioVO = usuarioDAO.consultarPorChavePrimaria(transacaoVO.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                            mapaUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                                        }
                                        transacaoVO.setUsuarioResponsavel(usuarioVO);
                                    }

                                    if (!UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                                        ConvenioCobrancaVO convenioVO = mapConve.get(transacaoVO.getConvenioCobrancaVO().getCodigo());
                                        if (convenioVO == null) {
                                            convenioVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                            mapConve.put(convenioVO.getCodigo(), convenioVO);
                                        }
                                        transacaoVO.setConvenioCobrancaVO(convenioVO);
                                    }

                                    cobrancaDTO = new CobrancaDTO(transacaoVO);
                                    cobrancaDTO.setQtdParcelas(rs.getInt("qtdparcelas"));
                                } else if (tipo.equalsIgnoreCase("REMESSA")) {
                                    RemessaItemVO remessaItemVO = remessaItemDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                                    if (!UteisValidacao.emptyNumber(remessaItemVO.getRemessa().getUsuario().getCodigo())) {
                                        UsuarioVO usuarioVO = mapaUsuario.get(remessaItemVO.getRemessa().getUsuario().getCodigo());
                                        if (usuarioVO == null) {
                                            usuarioVO = usuarioDAO.consultarPorChavePrimaria(remessaItemVO.getRemessa().getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                                            mapaUsuario.put(usuarioVO.getCodigo(), usuarioVO);
                                        }
                                        remessaItemVO.getRemessa().setUsuario(usuarioVO);
                                    }

                                    cobrancaDTO = new CobrancaDTO(remessaItemVO);
                                    cobrancaDTO.setQtdParcelas(remessaItemDAO.consultarQtdParcelasItem(remessaItemVO.getCodigo()));
                                }
                                if (cobrancaDTO != null) {
                                    lista.add(cobrancaDTO);
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
            usuarioDAO = null;
            convenioCobrancaDAO = null;
            remessaItemDAO = null;
            finalizarConexao(con);
        }
    }

    private void imprimirRecibo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        Usuario usuarioDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            reciboPagamentoDAO = new ReciboPagamento(con);
            usuarioDAO = new Usuario(con);

            Integer recibo = UteisValidacao.converterInteiro(request.getParameter("recibo"));
            String chave = request.getParameter("key");
            request.getSession().setAttribute("key", chave);

            Conexao.guardarConexaoForJ2SE(chave, con);
            request.getSession().setAttribute("key", chave);
            ReciboPagamentoVO reciboVO = reciboPagamentoDAO.consultarPorChavePrimaria(recibo, Uteis.NIVELMONTARDADOS_TODOS);
            String url = reciboPagamentoDAO.imprimirReciboPDF(false, false, reciboVO.getEmpresa(), reciboVO.getEmpresa(), usuarioDAO.getUsuarioRecorrencia(),
                    reciboVO, null, "PDF", request);

            String[] arq = url.split("/");
            String nomeDownload = arq[arq.length - 1];

            File pdfFile = new File(request.getRealPath("relatorio") + File.separator + nomeDownload);
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
            response.setContentLength((int) pdfFile.length());

            FileInputStream fileInputStream = new FileInputStream(pdfFile);
            OutputStream responseOutputStream = response.getOutputStream();
            int bytes;
            while ((bytes = fileInputStream.read()) != -1) {
                responseOutputStream.write(bytes);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            reciboPagamentoDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    private void obterDadosContratoParcela(ParcelaDTO parcelaDTO, Connection con) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("c.codigo as contrato, \n");
        sql.append("c.vigenciade as inicio, \n");
        sql.append("c.vigenciaateajustada as fim, \n");
        sql.append("e.nome as empresa, \n");
        sql.append("pl.descricao as plano \n");
        sql.append("from movparcela mp \n");
        sql.append("inner join empresa e on e.codigo = mp.empresa \n");
        sql.append("left join contrato c on c.codigo = mp.contrato \n");
        sql.append("left join plano pl on pl.codigo = c.plano \n");
        sql.append("where mp.codigo = ").append(parcelaDTO.getCodigo());

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    parcelaDTO.setEmpresa(rs.getString("empresa"));
                    Integer contrato = rs.getInt("contrato");
                    if (!UteisValidacao.emptyNumber(contrato)) {
                        parcelaDTO.setContrato(contrato);
                        parcelaDTO.setInicioContrato(Calendario.getDataAplicandoFormatacao(rs.getDate("inicio"), "dd/MM/yyyy"));
                        parcelaDTO.setFimContrato(Calendario.getDataAplicandoFormatacao(rs.getDate("fim"), "dd/MM/yyyy"));
                        parcelaDTO.setPlano(rs.getString("plano"));
                    }
                }
            }
        }
    }

    private EnvelopeRespostaDTO consultarClientes(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            con = obterConexao(request);

            String parametro = request.getParameter("p");
            Integer empresa = obterEmpresa(request, false);

//            boolean comFoto = false;
//            try {
//                comFoto = (request.getParameter("f").equalsIgnoreCase("t") || request.getParameter("f").equalsIgnoreCase("true"));
//            } catch (Exception ignored) {
//            }

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;


//            boolean somenteNumeros = UteisValidacao.somenteNumeros(parametro);
            String letras = Uteis.tirarCaracteres(parametro, false);
            String numeros = Uteis.tirarCaracteres(parametro, true);
            boolean buscarCPF = numeros != null && numeros.length() >= 11;

            StringBuilder sql = new StringBuilder();
            sql.append("select * from (\n");
            sql.append("select \n");
            sql.append("3 as peso, \n");
            sql.append("cl.matricula, \n");
            sql.append("p.nome, \n");
            sql.append("p.codigo as pessoa, \n");
            sql.append("cl.codigo as cliente, \n");
            sql.append("e.nome as empresa, \n");
            sql.append("p.fotokey \n");
            sql.append("from cliente cl \n");
            sql.append("inner join empresa e on e.codigo = cl.empresa \n");
            sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
            sql.append("inner join situacaoclientesinteticodw sw on sw.codigopessoa = p.codigo \n");
            if (UteisValidacao.emptyString(letras)) {
                sql.append("where false \n");
            } else {
                sql.append("where sw.nomeconsulta LIKE remove_acento_upper('").append(letras.replaceAll(" ", "%")).append("%') \n");
            }

            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and cl.empresa = ").append(empresa).append(" \n");
            }

            if (!UteisValidacao.emptyString(numeros)) {
                sql.append("union \n");
                sql.append("select \n");
                sql.append("2 as peso, \n");
                sql.append("cl.matricula, \n");
                sql.append("p.nome, \n");
                sql.append("p.codigo as pessoa, \n");
                sql.append("cl.codigo as cliente, \n");
                sql.append("e.nome as empresa, \n");
                sql.append("p.fotokey \n");
                sql.append("from cliente cl \n");
                sql.append("inner join empresa e on e.codigo = cl.empresa \n");
                sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
                sql.append("where (cl.matricula = '").append(numeros).append("' or cl.codigomatricula = ").append(numeros).append(") \n");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append("and cl.empresa = ").append(empresa).append(" \n");
                }
            }

            if (buscarCPF) {
                sql.append("union \n");
                sql.append("select \n");
                sql.append("1 as peso, \n");
                sql.append("cl.matricula, \n");
                sql.append("p.nome, \n");
                sql.append("p.codigo as pessoa, \n");
                sql.append("cl.codigo as cliente, \n");
                sql.append("e.nome as empresa, \n");
                sql.append("p.fotokey \n");
                sql.append("from cliente cl \n");
                sql.append("inner join empresa e on e.codigo = cl.empresa \n");
                sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
                sql.append("where (p.cfp = '").append(Uteis.formatarCpfCnpj(numeros, true)).append("' or p.cfp = '").append(Uteis.formatarCpfCnpj(numeros, false)).append("') \n");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append("and cl.empresa = ").append(empresa).append(" \n");
                }
            }

            sql.append(") as sql \n");
            sql.append("order by peso,nome \n");

            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);

            paginadorDTO.setQuantidadeTotalElementos(total.longValue());
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            List<AlunoBuscaDTO> lista = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(total)) {
                sql.append("LIMIT ").append(maxResults).append(" \n");
                sql.append("OFFSET ").append(indiceInicial).append(" \n");

                try (Statement stm = con.createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sql.toString())) {
                        while (rs.next()) {
                            try {
                                AlunoBuscaDTO dto = new AlunoBuscaDTO();
                                dto.setMatricula(rs.getString("matricula"));
                                dto.setNome(rs.getString("nome"));
                                dto.setCliente(rs.getInt("cliente"));
                                dto.setPessoa(rs.getInt("pessoa"));
                                dto.setEmpresa(rs.getString("empresa"));
                                dto.setFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                                lista.add(dto);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO notificarRecursoEmpresa(ServletRequest request) throws Exception {
        Connection con = null;
        ZillyonWebFacade zillyonWebFacade = null;
        try {
            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }

            String recurso = request.getParameter("r");
            if (UteisValidacao.emptyString(recurso)) {
                throw new Exception("Recurso não informado");
            }

            Integer empresa = obterEmpresa(request, false);
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informado");
            }

            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("u"));
            String username = request.getParameter("username");
            if (UteisValidacao.emptyString(username) && UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuario não informado");
            }

            RecursoSistema recursoEnum = RecursoSistema.fromDescricao(recurso);
            if (recursoEnum == null) {
                throw new Exception("RecursoSistema não identificado");
            }

            con = obterConexao(request);
            zillyonWebFacade = new ZillyonWebFacade(con);
            zillyonWebFacade.notificarRecursoSistema(key, recursoEnum, usuario, empresa, username);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO bloquearCatraca(ServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        ClienteMensagem clienteMensagemDAO;
        Log logDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            clienteMensagemDAO = new ClienteMensagem(con);
            logDAO = new Log(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));
            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("u"));
            String msgCatraca = request.getParameter("m");
            String username = request.getParameter("username");

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Cliente não encontrado");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            ClienteMensagemVO clienteMensagemVOExiste = clienteMensagemDAO.consultarPorCodigoTipoMensagemECliente(clienteVO.getCodigo(), TiposMensagensEnum.CATRACA.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyNumber(clienteMensagemVOExiste.getCodigo())) {
                excluirClienteMensagem(clienteMensagemVOExiste, clienteVO, usuarioVO, logDAO, clienteMensagemDAO);
            }

            ClienteMensagemVO clienteMensagemVO = new ClienteMensagemVO();
            clienteMensagemVO.setTipomensagem(TiposMensagensEnum.CATRACA);
            clienteMensagemVO.setUsuario(usuarioVO);
            clienteMensagemVO.setCliente(clienteVO);
            clienteMensagemVO.setBloqueio(true);
            clienteMensagemVO.setDataBloqueio(Calendario.hoje());
            if (UteisValidacao.emptyString(msgCatraca)) {
                msgCatraca = "Por favor, dirija-se à recepção.";
            }
            clienteMensagemVO.setMensagem(msgCatraca);
            clienteMensagemDAO.incluir(clienteMensagemVO);

            try {
                clienteMensagemVO.setObjetoVOAntesAlteracao(new ClienteMensagemVO());
                clienteMensagemVO.setNovoObj(true);
//                registraLog(clienteMensagemVO, usuarioVO, clienteMensagemVO.getCodigo().toString(), "MENSAGEMCATRACA", clienteMensagemVO.getCliente().getPessoa().getCodigo(), true);
            } catch (Exception e) {
//                registraLog("MENSAGEMCATRACA", clienteMensagemVO.getCliente().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE MENSAGEM CATRACA", usuarioVO.getNome(), usuarioVO.getUserOamd());
                e.printStackTrace();
            }

            notificarOuvintes(clienteMensagemVO, clienteVO, clienteVO.getEmpresa().getCodigo(), request.getParameter("key"), con);
            return EnvelopeRespostaDTO.of("Cliente bloqueado na catraca.");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            clienteMensagemDAO = null;
            logDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO desbloquearCatraca(ServletRequest request) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        ClienteMensagem clienteMensagemDAO;
        Log logDAO;
        Connection con = null;
        try {
            con = obterConexao(request);
            clienteDAO = new Cliente(con);
            usuarioDAO = new Usuario(con);
            clienteMensagemDAO = new ClienteMensagem(con);
            logDAO = new Log(con);

            Integer pessoa = UteisValidacao.converterInteiro(request.getParameter("p"));
            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("u"));
            String username = request.getParameter("username");

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Cliente não encontrado");
            }

            ClienteMensagemVO clienteMensagemVO = clienteMensagemDAO.consultarPorCodigoTipoMensagemECliente(clienteVO.getCodigo(), TiposMensagensEnum.CATRACA.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(clienteMensagemVO.getCodigo())) {
                throw new Exception("Cliente mensagem não encontrada");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            excluirClienteMensagem(clienteMensagemVO, clienteVO, usuarioVO, logDAO, clienteMensagemDAO);

            clienteMensagemVO.setDataBloqueio(null);
            clienteMensagemVO.setBloqueio(false);
            notificarOuvintes(clienteMensagemVO, clienteVO, clienteVO.getEmpresa().getCodigo(), request.getParameter("key"), con);
            return EnvelopeRespostaDTO.of("Cliente desbloqueado na catraca.");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            clienteDAO = null;
            usuarioDAO = null;
            clienteMensagemDAO = null;
            logDAO = null;
            finalizarConexao(con);
        }
    }

    private void excluirClienteMensagem(ClienteMensagemVO clienteMensagemVO, ClienteVO clienteVO, UsuarioVO usuarioVO, Log logDAO, ClienteMensagem clienteMensagemDAO) throws Exception {
        clienteMensagemDAO.excluir(clienteMensagemVO);
        try {
//            logDAO.registrarLogExclusaoObjetoVO(clienteMensagemVO, clienteMensagemVO.getCodigo(), "MENSAGEMCATRACA", clienteVO.getPessoa().getCodigo());
        } catch (Exception e) {
//            logDAO.registrarLogErroObjetoVO("MENSAGEMCATRACA", clienteMensagemVO.getCliente().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE MENSAGEM CATRACA", usuarioVO.getNome(), usuarioVO.getUserOamd());
            e.printStackTrace();
        }
    }

    private void notificarOuvintes(ClienteMensagemVO clienteMensagemVO, ClienteVO clienteVO,
                                   Integer empresa, String key, Connection con) {
        try {
            String msg = "";
            if (clienteMensagemVO.getBloqueio() || (clienteMensagemVO.getDataBloqueio() != null && Calendario.igual(clienteMensagemVO.getDataBloqueio(), Calendario.hoje()))) {
                msg = "bloquear agora(" + clienteVO.getCodAcesso() + ")";
            } else {
                msg = "desbloquear agora(" + clienteVO.getCodAcesso() + ")";
            }

            String url = PropsService.getPropertyValue("urlNotificacaoAcesso");
            String timeZone = null;
            Empresa empresaDAO = null;
            try {
                empresaDAO = new Empresa(con);
                timeZone = empresaDAO.obterTimeZoneDefault(empresa);
            } catch (Exception ex) {
                Logger.getLogger(PactoPayDashServlet.class.getName()).log(Level.SEVERE, null, ex);
            } finally {
                empresaDAO = null;
            }
            SuperControle.notificarOuvintes(msg, url, key, timeZone);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void registraLog(String nomeEntidade, UsuarioVO usuarioVO, Integer chavePrimaria,
                             String operacao, String campo, String alteracao, Connection con) {
        Log logDAO;
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade(nomeEntidade.toUpperCase());
            log.setNomeEntidadeDescricao(nomeEntidade.toUpperCase());
            log.setDescricao(nomeEntidade.toUpperCase());
            log.setChavePrimaria(chavePrimaria.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNome().toUpperCase());
            log.setOperacao(operacao);
            log.setNomeCampo(campo);
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(alteracao);
            logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, "Erro ao gerarLog " + e.getMessage());
        } finally {
            logDAO = null;
        }
    }

    private EnvelopeRespostaDTO consultarConveniosCobranca(ServletRequest request) throws Exception {
        Connection con = null;
        ConvenioCobranca convenioCobrancaDAO = null;
        try {
            con = obterConexao(request);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            Integer tipoAutorizacao = UteisValidacao.converterInteiro(request.getParameter("tipo"));
            if (UteisValidacao.emptyNumber(tipoAutorizacao)) {
                throw new Exception("Tipo Autorização não informado");
            }

            TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum = TipoAutorizacaoCobrancaEnum.valueOf(tipoAutorizacao);
            if (tipoAutorizacaoCobrancaEnum.equals(TipoAutorizacaoCobrancaEnum.NENHUM)) {
                throw new Exception("Tipo Autorização não informado");
            }

            Integer empresa = obterEmpresa(request, false, true);
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            List<ConvenioCobrancaVO> lista = convenioCobrancaDAO.consultarPorTiposESituacao(tipoAutorizacaoCobrancaEnum.getTiposConvenio(), empresa, SituacaoConvenioCobranca.ATIVO,
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);

            List<ConvenioCobrancaDTO> listaRetorno = new ArrayList<>();
            for (ConvenioCobrancaVO obj : lista) {
                listaRetorno.add(new ConvenioCobrancaDTO(obj));
            }

            return EnvelopeRespostaDTO.of(listaRetorno);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO consultarConveniosCobrancaGeral(ServletRequest request) throws Exception {
        Connection con = null;
        ConvenioCobranca convenioCobrancaDAO = null;
        try {
            con = obterConexao(request);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            Integer empresa = obterEmpresa(request, false, true);
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            List<ConvenioCobrancaVO> lista = convenioCobrancaDAO.consultarPorTiposESituacao(null, empresa, SituacaoConvenioCobranca.ATIVO,
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);

            Map<Integer, ConvenioCobrancaVO> mapa = new HashMap<>();
            List<ConvenioCobrancaDTO> listaRetorno = new ArrayList<>();
            for (ConvenioCobrancaVO obj : lista) {
                if (!mapa.containsKey(obj.getCodigo())) {
                    listaRetorno.add(new ConvenioCobrancaDTO(obj));
                    mapa.put(obj.getCodigo(), obj);
                }
            }

            Ordenacao.ordenarLista(listaRetorno, "descricao");
            return EnvelopeRespostaDTO.of(listaRetorno);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterTipoAutorizacao() {
        try {
            JSONArray jsonArray = new JSONArray();
            for (TipoAutorizacaoCobrancaEnum tipo : TipoAutorizacaoCobrancaEnum.values()) {
                if (tipo.equals(TipoAutorizacaoCobrancaEnum.NENHUM) ||
                        tipo.equals(TipoAutorizacaoCobrancaEnum.PIX)) {
                    continue;
                }
                JSONObject json = new JSONObject();
                json.put("codigo", tipo.getId());
                json.put("descricao", tipo.getDescricao());
                jsonArray.put(json);
            }
            return EnvelopeRespostaDTO.of(jsonArray);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO obterTipoProdutos() {
        try {
            JSONArray jsonArray = new JSONArray();
            for (SelectItem item : TipoProduto.getTiposProdutosParaAutorizacaoCobranca(false)) {
                JSONObject json = new JSONObject();
                json.put("codigo", item.getValue());
                json.put("descricao", item.getLabel());
                jsonArray.put(json);
            }
            return EnvelopeRespostaDTO.of(jsonArray);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO obterTipoParcelasCobrar() {
        try {
            JSONArray jsonArray = new JSONArray();
            for (TipoObjetosCobrarEnum tipo : TipoObjetosCobrarEnum.values()) {
                if (tipo.equals(TipoObjetosCobrarEnum.NENHUM)) {
                    continue;
                }
                JSONObject json = new JSONObject();
                json.put("codigo", tipo.getId());
                json.put("descricao", tipo.getDescricao());
                jsonArray.put(json);
            }
            return EnvelopeRespostaDTO.of(jsonArray);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public EnvelopeRespostaDTO infoRetornos(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            FiltroPactoPayDTO filtroDTO = new FiltroPactoPayDTO(request);

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("codigoretorno, \n");
            sql.append("tipoconvenio, \n");
            sql.append("count(*) as qtd \n");
            sql.append("from ( \n");
            sql.append("select  \n");
            sql.append("t.codigoretorno, \n");
            sql.append("t.dataprocessamento as data, \n");
            sql.append("t.empresa, \n");
            sql.append("t.conveniocobranca, \n");
            sql.append("cc.tipoconvenio \n");
            sql.append("from transacao t \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = t.conveniocobranca \n");
            sql.append("where t.transacaoverificarcartao = false \n");
            sql.append("and t.dataprocessamento >= '").append(Uteis.getDataFormatoBD(filtroDTO.getInicioDate())).append(" 00:00:00.000' \n");
            sql.append("and t.dataprocessamento <= '").append(Uteis.getDataFormatoBD(filtroDTO.getFimDate())).append(" 23:59:59.000' \n");
            sql.append("and t.situacao in (1,3) \n");
            sql.append("and t.movpagamento is null \n");
            sql.append("union \n");
            sql.append("select \n");
            sql.append("split_part(split_part(split_part(ri.props, 'StatusVenda=', 2), ',', 1), '}', 1) AS codigoRetorno, \n");
            sql.append("r.dataregistro as data, \n");
            sql.append("r.empresa, \n");
            sql.append("r.conveniocobranca, \n");
            sql.append("cc.tipoconvenio \n");
            sql.append("from remessaitem ri \n");
            sql.append("inner join remessa r on r.codigo = ri.remessa \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = r.conveniocobranca \n");
            sql.append("where r.tipo in (2,8,11) \n");
            sql.append("and r.dataregistro >= '").append(Uteis.getDataFormatoBD(filtroDTO.getInicioDate())).append(" 00:00:00.000' \n");
            sql.append("and r.dataregistro <= '").append(Uteis.getDataFormatoBD(filtroDTO.getFimDate())).append(" 23:59:59.000' \n");
            sql.append("and ri.movpagamento is null \n");
            sql.append("and ri.props ilike '%StatusVenda%' \n");
            sql.append(") as sql \n");
            sql.append("where coalesce(sql.codigoretorno, '') != '' \n");
            if (filtroDTO.getEmpresas().size() > 0) {
                sql.append("and sql.empresa in (").append(filtroDTO.getEmpresasString()).append(") \n");
            }
            if (filtroDTO.getConvenios().size() > 0) {
                sql.append("and sql.conveniocobranca in (").append(filtroDTO.getConveniosString()).append(") \n");
            }
            sql.append("group by 1,2 \n");
            sql.append("order by 3 desc \n");
            List<CodigoRetornoDTO> lista = new ArrayList<>();
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        try {
                            String codigoRetorno = rs.getString("codigoretorno");
                            CodigoRetornoDTO dto = new CodigoRetornoDTO();
                            dto.setCodigo(codigoRetorno);
                            dto.setQtd(rs.getInt("qtd"));
                            TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipoconvenio"));
                            String msgAdqui = CartaoTentativaVO.obterDescricaoRetornoAdquirente(tipoConvenioCobrancaEnum, codigoRetorno, null, null);

                            if (UteisValidacao.emptyString(msgAdqui) &&
                                    tipoConvenioCobrancaEnum.getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                                    !tipoConvenioCobrancaEnum.getTipoTransacao().equals(TipoTransacaoEnum.NENHUMA)) {
                                msgAdqui = transacaoDAO.obterCodigoRetornoDescricao(codigoRetorno, tipoConvenioCobrancaEnum.getTipoTransacao());
                                if (UteisValidacao.emptyString(msgAdqui)) {
                                    try {
                                        PovoarMovParcelaTentativaConvenio.preencherCodigoRetornoDescricaoTransacao(codigoRetorno, tipoConvenioCobrancaEnum.getTipoTransacao(), null, null, 2, con);
                                        msgAdqui = transacaoDAO.obterCodigoRetornoDescricao(codigoRetorno, tipoConvenioCobrancaEnum.getTipoTransacao());
                                    } catch (Exception ex) {
                                        ex.printStackTrace();
                                    }
                                }
                            }
                            dto.setDescricao(msgAdqui);
                            lista.add(dto);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }
            return EnvelopeRespostaDTO.of(lista);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            transacaoDAO = null;
        }
    }

    public EnvelopeRespostaDTO cancelarCobranca(ServletRequest request) throws Exception {
        Connection con = null;
        Transacao transacaoDAO = null;
        Usuario usuarioDAO = null;
        try {
            con = obterConexao(request);
            transacaoDAO = new Transacao(con);
            usuarioDAO = new Usuario(con);

            String key = request.getParameter("key");

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuario não informado");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(username, usuario, con, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            if (jsonBody.has("transacao")) {

                Integer totalCancelado = 0;
                JSONArray lista = jsonBody.getJSONArray("transacao");
                JSONArray detalhes = new JSONArray();
                for (int e = 0; e < lista.length(); e++) {
                    JSONObject transacaoJson = new JSONObject();
                    try {
                        Integer transacao = lista.getInt(e);
                        TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
                        if (!transacaoVO.isPermiteCancelar()) {
                            throw new Exception("Transação não permite cancelar");
                        }

                        String retorno = transacaoDAO.cancelarTransacao(transacaoVO, true, usuarioVO, key);
                        transacaoJson.put("resultado", retorno);
                        totalCancelado++;
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        transacaoJson.put("erro", ex.getMessage());
                    } finally {
                        detalhes.put(transacaoJson);
                    }
                }

                JSONObject json = new JSONObject();
                if (UteisValidacao.emptyNumber(totalCancelado)) {
                    json.put("resultado", "Nenhuma transação cancelada");
                } else {
                    json.put("resultado", totalCancelado > 1 ? "Foram canceladas " + totalCancelado + " transações" : "Foi cancelado 1 transação");
                }
                json.put("detalhes", detalhes);
                return EnvelopeRespostaDTO.of(json.toString());
//            } else if (!UteisValidacao.emptyString(remessaItem)) {
////                return EnvelopeRespostaDTO.of("Criada remessa de cancelamento");
//                throw new Exception("Cancelamento não disponível");
            } else {
                throw new Exception("Nenhuma cobranca informada");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    public EnvelopeRespostaDTO eficienciaCobranca(ServletRequest request) throws Exception {
        BiMSService biMSService;
        try {
            biMSService = new BiMSServiceImpl();

            String key = request.getParameter("key");
            FiltroPactoPayDTO filtroPayDTO = new FiltroPactoPayDTO(request);

            FiltroDTO filtroBIDTO = biMSService.obterBI(key, BIEnum.DCC, getFiltroBiConvenioDTO(filtroPayDTO));
            JSONObject dados = new JSONObject(filtroBIDTO.getJsonDados());

            List<BIInadimplenciaTO> listaDadosInadimplencia = new ArrayList<>();
            JSONArray dadosInadimplencia = dados.getJSONArray("dadosInadimplencia");
            for (int e = 0; e < dadosInadimplencia.length(); e++) {
                listaDadosInadimplencia.add(new BIInadimplenciaTO(dadosInadimplencia.getJSONObject(e)));
            }

            return EnvelopeRespostaDTO.of(listaDadosInadimplencia);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private FiltroDTO getFiltroBiConvenioDTO(FiltroPactoPayDTO filtroPayDTO) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.DCC.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", false);
        filtros.put("dataBase", Calendario.getDataComHoraZerada(filtroPayDTO.getFimDate()).getTime());
        filtros.put("somenteParcelasMes", true);
        filtros.put("somenteParcelasForaMes", false);
        filtros.put("incluirContratosCancelados", false);
        filtros.put("empresas", filtroPayDTO.getEmpresas());

        List<Integer> colaboradores = new ArrayList<>();
        filtros.put("colaboradores", colaboradores);

        filtros.put("convenios", filtroPayDTO.getConvenios());
        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    private EnvelopeRespostaDTO consultarDashCartao(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayDashCartao pactoPayDashCartaoDAO;
        try {
            con = obterConexao(request);
            pactoPayDashCartaoDAO = new PactoPayDashCartao(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroPactoPayDTO = new FiltroPactoPayDTO(request);
            List<HistoricoDTO> lista = pactoPayDashCartaoDAO.consultarHistorico(filtroPactoPayDTO, paginadorDTO);
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pactoPayDashCartaoDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO consultarDashCartaoLista(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayDashCartao pactoPayDashCartaoDAO;
        try {
            con = obterConexao(request);
            pactoPayDashCartaoDAO = new PactoPayDashCartao(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroPactoPayDTO = new FiltroPactoPayDTO(request);

            boolean informouPeriodo = filtroPactoPayDTO != null && filtroPactoPayDTO.getInicioDate() != null && filtroPactoPayDTO.getFimDate() != null;

            String dia = request.getParameter("dia");
            boolean informouDia = !UteisValidacao.emptyString(dia);

            if (!informouDia && !informouPeriodo) {
                throw new Exception("Não foi informado dia nem período!");
            }

            Date diaDate = null;
            if (informouDia) {
                diaDate = Calendario.getDate("yyyyMMdd", dia);
            }

            String remessa = request.getParameter("remessa");
            TipoConsultaHistoricoCartaoEnum tipoConsultaHistoricoCartaoEnum =
                    (remessa != null && remessa.equalsIgnoreCase("true")) ? TipoConsultaHistoricoCartaoEnum.REMESSA : TipoConsultaHistoricoCartaoEnum.TRANSACAO;

            List<CobrancaDetalheDTO> lista = pactoPayDashCartaoDAO.historicoLista(diaDate, tipoConsultaHistoricoCartaoEnum, filtroPactoPayDTO, paginadorDTO);
            return EnvelopeRespostaDTO.of(lista, paginadorDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pactoPayDashCartaoDAO = null;
            finalizarConexao(con);
        }
    }

    private Integer obterEmpresa(ServletRequest request, boolean validar) throws Exception {
        return obterEmpresa(request, validar, false);
    }

    private Integer obterEmpresa(ServletRequest request, boolean validar, boolean usarCodEmpresa) throws Exception {
        Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
        if (UteisValidacao.emptyNumber(empresa)) {
            empresa = UteisValidacao.converterInteiro(request.getParameter("e"));
        }
        Integer codEmpresa = UteisValidacao.converterInteiro(request.getParameter("codEmpresa"));
        if (usarCodEmpresa && !UteisValidacao.emptyNumber(codEmpresa)) {
            empresa = codEmpresa;
        }
        if (validar && UteisValidacao.emptyNumber(empresa)) {
            throw new Exception("Empresa não informado");
        }
        return empresa;
    }

    private UsuarioVO obterUsuarioVO(String username, Integer codigoUsuario, Connection con, int nivelMontarDados) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);
            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyString(username)) {
                usuarioVO = usuarioDAO.consultarPorUsername(username, nivelMontarDados);
            }
            if (!UteisValidacao.emptyNumber(codigoUsuario) &&
                    (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo()))) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codigoUsuario, nivelMontarDados);
            }
            return usuarioVO;
        } finally {
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO obterCobrancaDetalhe(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayDashCartao pactoPayDashCartaoDAO;
        try {
            con = obterConexao(request);
            pactoPayDashCartaoDAO = new PactoPayDashCartao(con);
            PaginadorDTO paginadorDTO = new PaginadorDTO(request);
            FiltroPactoPayDTO filtroPactoPayDTO = new FiltroPactoPayDTO(request);
            paginadorDTO.setSize(1L);


            Integer codigoTransacao = UteisValidacao.converterInteiro(request.getParameter("transacao"));
            if (UteisValidacao.emptyNumber(codigoTransacao)) {
                throw new Exception("Codigo transação não informada");
            }
            filtroPactoPayDTO.setCodigoTransacao(codigoTransacao);

            List<CobrancaDetalheDTO> lista = pactoPayDashCartaoDAO.historicoLista(null, TipoConsultaHistoricoCartaoEnum.TRANSACAO, filtroPactoPayDTO, paginadorDTO);
            if (UteisValidacao.emptyList(lista)) {
                throw new Exception("Cobrança não encontrada");
            }
            return EnvelopeRespostaDTO.of(lista.get(0));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pactoPayDashCartaoDAO = null;
            finalizarConexao(con);
        }
    }

    private Date getMesReferenciaConsultaCreditoPacto(EmpresaVO empresaVO) {
        //Tratativa, se o tipo de cobrança for pós pago e a cobrança do mês já tiver sido feita, então consultar os créditos do mês corrente.
        Date mesReferencia = null;

        if(empresaVO != null && (empresaVO.getDtUltimaCobrancaPacto() != null && Calendario.getMes(empresaVO.getDtUltimaCobrancaPacto()) == Calendario.getMes(new Date())) &&
                empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
            mesReferencia = new Date();
        }

        return mesReferencia;

    }
}
