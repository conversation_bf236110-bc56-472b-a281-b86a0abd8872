package controle.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.asaas.AsaasEmpresaService;
import controle.basico.TokenOperacaoControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoReenvioMovParcelaEmpresaVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.ContaCorrenteEmpresaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;
import negocio.comuns.utilitarias.reflexao.PovoadorPropriedades;
import negocio.comuns.utilitarias.reflexao.Propriedade;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.oamd.RedeEmpresaVO;
import negocio.oamd.dto.BackupClienteDTO;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpStatus;
import org.jboleto.JBoleto;
import org.json.JSONArray;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.boleto.asaas.DadosComerciaisContaAsaasDTO;
import servicos.impl.boleto.asaas.SituacaoCadastralContaAsaasDTO;
import servicos.impl.boleto.asaas.TaxasContaAsaasDTO;
import servicos.impl.boleto.itau.ItauService;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.dcc.rede.TipoArquivoRedeEnum;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;
import servicos.impl.oamd.OAMDService;
import servicos.impl.pCertPacto.PCertService;
import servicos.impl.pagarMe.PagarMeService;
import servicos.impl.gatewaypagamento.RecebedorDTO;
import servicos.impl.pagolivre.BankAccountPagoLivreDto;
import servicos.impl.pagolivre.MerchantPagoLivreDto;
import servicos.impl.pagolivre.PagoLivreConciliacaoService;
import servicos.impl.pagolivre.PagoLivreService;
import servicos.impl.pix.PixWebhookService;
import servicos.impl.redepay.ERedeStatusConciliacaoEnum;
import servicos.impl.stone.StoneOnlineServiceConciliation;
import servicos.impl.stoneV5.StoneOnlineV5Service;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.integracao.pjbank.recebimento.CredencialPJBankDTO;
import servicos.pix.PixRequisicaoDto;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas convenioCobrancaForm.jsp convenioCobrancaCons.jsp) com as
 * funcionalidades da classe
 * <code>ConvenioCobranca</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see ConvenioCobrancaVO
 */
public class ConvenioCobrancaControle extends SuperControle {

    private ConvenioCobrancaVO convenioCobrancaVO;
    private PixWebhookVO pixWebhookVO;
    protected List listaSelectItemEmpresa;
    private List<SelectItem> listaSelectItemContaEmpresa;
    private List<SelectItem> listaSelectItemSituacaoConvenioCobranca;
    private List<SelectItem> listaSelectItemCurrencyConvenio;
    private List<ContaCorrenteVO> listaContaCorrenteEmpresa;
    private List listaSelectItemTipoRemessa;
    private List<SelectItem> listaSelectItemTipoConvenio = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemConvenios = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemIdentificadorClienteEmpresa = new ArrayList<SelectItem>();
    private List<BancoVO> bancosVOs = new ArrayList<BancoVO>();
    private List<SelectItem> bancos = new ArrayList<SelectItem>();
    private Boolean somenteExibirTipoConvenio = false;
    private boolean podeAdicionarConfiguracaoEmpresa = false;
    private ConvenioCobrancaEmpresaVO convenioCobrancaEmpresa = new ConvenioCobrancaEmpresaVO();
    private Boolean utilizaConfiguracaoSesc;
    private ConvenioCobrancaVO convenioCobrancaVOClone;
    private String msgAlert;
    private Boolean acessoAjusteConvenio;
    private String codigoConvenioAlterado;
    private String descricaoConvenioNovo;
    private List<ConvenioCobrancaVO> listaConveniosCompleta;
    private List<SelectItem> listaConvenioAlterado = new ArrayList<>();
    private String exemplo;
    private List<String> logsExtrato = new ArrayList<>();
    private String extratoEletronicoCielo;
    private List<OperadoraCartaoVO> listaOperadoraCriarAutomatico;
    private String onComplete;
    private ConvenioCobrancaRateioVO convenioCobrancaRateioVO;
    private ConvenioCobrancaRateioItemVO convenioCobrancaRateioItemVO;
    private List<SelectItem> listaSelectItemRecebedores;
    private List<SelectItem> listaSelectItemPlano;
    private List<SelectItem> listaSelectItemProduto;
    private List<PlanoVO> listaPlano;
    private List<ProdutoVO> listaProduto;
    private CredencialPJBankDTO credencialPJBankDTO;
    private String codeAutorizacaoCielo;
    private int qtdMaximaLinhasApresentarLogExtrato = 5000;
    private String situacaoFiltro;
    private Integer situacaoFiltroTipo;
    private List<MovParcelaVO> movParcelaVOS = new ArrayList<>();
    private Double totalMovParcela = 0.0;

    private boolean fezUploadArquivo1 = false;
    private boolean fezUploadArquivo2 = false;
    private List<ConvenioCobrancaArquivoVO> listaConvenioCobrancaArquivoVO;
    private ConvenioCobrancaArquivoVO convenioCobrancaArquivo1;
    private ConvenioCobrancaArquivoVO convenioCobrancaArquivo2;
    private String senhaArquivo = "";
    private String nomeArquivo1 = "";
    private String nomeArquivo2 = "";
    private boolean exibirCampoSenhaArquivo = false;
    private boolean possuiArquivo1 = false;
    private boolean possuiArquivo2 = false;

    private String tipoConsultaPix;
    private String infoPixPesquisar;
    private JSONObject jsonConsultaPixResposta;
    private boolean possuiRespostaConsultaPix = false;
    private Object jsonWebhookPixAtivadoResposta;
    private boolean possuiWebhookPixAtivado = false;
    private String pixWebhookString;
    private boolean gerouConvenioCieloSandbox = false;
    private SituacaoCadastralContaAsaasDTO situacaoCadastralContaAsaasDTO;
    private DadosComerciaisContaAsaasDTO dadosComerciaisContaAsaasDTO;
    private TaxasContaAsaasDTO taxasContaAsaasDTO;
    private boolean gerarBoletoHibrido = false;
    private String abrirFecharModalWebhookAtivado = "";
    private String abrirFecharModalLogPixWebhook = "";
    private String abrirFecharModalTokenOperacao;
    private String labelsCamposExibirNoModalTokenOperacao;
    private int qtdCamposSensiveisAlterados = 0;
    private static Map<String, RedeEmpresaVO> redeEmpresaVosJaConsultados = new HashMap<>();

    /**
     * Interface
     * <code>ConvenioCobrancaInterfaceFacade</code> responsável pela
     * interconexão da camada de controle com a camada de negócio. Criando uma
     * independência da camada de controle com relação a tenologia de
     * persistência dos dados (DesignPatter: Façade).
     */
    public ConvenioCobrancaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void inicializarUsuarioLogado() {
        try {
            UsuarioVO usuario = getUsuarioLogado();
            convenioCobrancaVO.setUsuarioVO(usuario);
        } catch (Exception exception) {
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>ConvenioCobranca</code> para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        limparMsg();
        this.acessoAjusteConvenio = false;
        ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS);
        utilizaConfiguracaoSesc = configuracaoSistemaVO.getSesc();
        setConvenioCobrancaVO(new ConvenioCobrancaVO());
        getConvenioCobrancaVO().setAmbiente(AmbienteEnum.PRODUCAO);
        getConvenioCobrancaVO().setPermiteVisualizarCredenciaisOriginais(isUsuarioPodeVisualizarCredenciaisOriginaisConvenio());
        inicializarUsuarioLogado();
        inicializarEmpresaLogada();
        inicializarListasSelectItemTodosComboBox();
        validarPermissaoConfiguracaoEmpresa();
        setSomenteExibirTipoConvenio(false);
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        convenioCobrancaVOClone = (ConvenioCobrancaVO)convenioCobrancaVO.getClone(true);
        carregarDadosRateio();
        prepararCamposUploadArquivo(true);
        setGerouConvenioCieloSandbox(false);
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>ConvenioCobranca</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            limparMsg();
            ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
            utilizaConfiguracaoSesc = configuracaoSistemaVO.getSesc();
            ConvenioCobrancaVO obj = getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            Uteis.logarDebug("**UsuarioPodeVisualizarCredenciaisOriginaisConvenio** - Editando convênio" );
            obj.setPermiteVisualizarCredenciaisOriginais(isUsuarioPodeVisualizarCredenciaisOriginaisConvenio());
            Uteis.logarDebug("**UsuarioPodeVisualizarCredenciaisOriginaisConvenio** - Editando convênio - permite: " + obj.isPermiteVisualizarCredenciaisOriginais());

            //no Asaas, se possui pelo menos uma chave pix cadastrada ativa, já pode afirmar que o recurso de boleto híbrido está ativo
            if (obj.isBoletoAsaas()) {
                verificarSeExisteChavePixCadastradaAsaas(obj);
            }

            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setConvenioCobrancaVO(obj);
            convenioCobrancaVO.registrarObjetoVOAntesDaAlteracao();
            setSomenteExibirTipoConvenio(!obj.getTipo().equals(TipoConvenioCobrancaEnum.NENHUM));
            inicializarUsuarioLogado();
            inicializarEmpresaLogada();
            inicializarListasSelectItemTodosComboBox();
            validarPermissaoConfiguracaoEmpresa();
            carregarConvenios(obj);
            carregarDadosRateio();
            prepararCamposUploadArquivo(false);
            carregarDadosArquivo();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            try {
                convenioCobrancaVOClone = (ConvenioCobrancaVO) convenioCobrancaVO.getClone(false);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InstantiationException e) {
                e.printStackTrace();
            }
        }
        return "editar";
    }

    private void verificarSeExisteChavePixCadastradaAsaas(ConvenioCobrancaVO obj) {
        setGerarBoletoHibrido(false);
        AsaasEmpresaService asaasEmpresaService;
        try {
            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), obj.getCodigoAutenticacao01(), obj.getAmbiente());
            JSONObject resposta = asaasEmpresaService.listarChavesPix(); //consultar Chaves Pix Ativas Cadastradas
            if (resposta.has("data")) {
                JSONArray jsonArray = resposta.getJSONArray("data");
                if (jsonArray.length() > 0) {
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    if (!UteisValidacao.emptyString(jsonObject.optString("status")) && jsonObject.optString("status").equals("ACTIVE")) {
                        setGerarBoletoHibrido(true);
                    }
                } else {
                    setGerarBoletoHibrido(false);
                }
            }
        } catch (Exception ex) {
            setGerarBoletoHibrido(false);
        } finally {
            asaasEmpresaService = null;
        }
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>ConvenioCobrancaVO</code>. Esta inicialização é necessária por
     * exigência da tecnologia JSF, que não trabalha com valores nulos para
     * estes atributos.
     */
    public void inicializarAtributosRelacionados(ConvenioCobrancaVO obj) {
        if (obj.getEmpresa() == null) {
            obj.setEmpresa(new EmpresaVO());
        }
        if (obj.getContaEmpresa() == null) {
            obj.setContaEmpresa(new ContaCorrenteVO());
        }
        if (obj.getTipoRemessa() == null) {
            obj.setTipoRemessa(new TipoRemessaVO());
        }
    }

    public void envioExtratoEletronicoCielo() throws Exception {
        try {
            limparMsg();
            String clientId = "f827c30a-ee67-38f0-b557-3ac429b46c06";
            String url = "https://minhaconta2.cielo.com.br/oauth";

            if (UteisValidacao.emptyString(convenioCobrancaVO.getCieloClientId())){
                mensagensTela(false, "msg_extrato_cielo_erro", "O campo Email cadastrado na Cielo é obrigatório.");
            } else if (!UteisValidacao.validaEmail(convenioCobrancaVO.getCieloClientId())) {
                mensagensTela(false, "msg_extrato_cielo_erro", "O Email é inválido para o credenciamento.");
            } else {
                FacesContext context = FacesContext.getCurrentInstance();
                HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
                String urlRedirect = request.getRequestURL().toString();

                StringBuilder urlCompleta = new StringBuilder(url)
//                .append("?response_type=code")
                        .append("?mode=redirect")
                        .append("&client_id=").append(clientId)
                        .append("&redirect_uri=").append(urlRedirect)
                        .append("&state=retornoextratocielo")
                        .append("&scope=profile_read,transaction_read");

                FacesContext.getCurrentInstance().getExternalContext().redirect(urlCompleta.toString());
            }
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public void retornoExtratoEletronicoCielo() {
        try {
            FacesContext context = FacesContext.getCurrentInstance();
            HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();

            if (request.getParameter("state") != null && request.getParameter("state").equals("retornoextratocielo")) {
                String erro = request.getParameter("error");

                if (erro != null) {
                    mensagensTela(false, "msg_extrato_cielo_erro", "");
                } else {
                    this.setCodeAutorizacaoCielo(request.getParameter("code"));
                    if (!UteisValidacao.emptyString(this.getCodeAutorizacaoCielo())) {
                        ConvenioCobranca convenioCobranca = new ConvenioCobranca();
                        convenioCobranca.credenciamentoExtratoDiarioCielo(this.convenioCobrancaVO, this.getCodeAutorizacaoCielo());
                        setSucesso(true);
                        setErro(false);
                        setMensagem("Credenciamento enviado com sucesso, consulte o status!");
                    }
                }
            }
        } catch (Exception e) {
            mensagensTela(false, "msg_extrato_cielo_erro", e.toString().replace("java.lang.Exception:", ""));
        }
    }

    private void tratarGravarAbaAjusteConvenio() throws Exception {
        if (!UteisValidacao.emptyString(this.codigoConvenioAlterado)) {
            if (this.codigoConvenioAlterado.equals(convenioCobrancaVO.getCodigo().toString())) {
                throw new Exception("Na aba 'Ajuste de convenio', o campo 'Convenio a ser alterado' nao pode ser o mesmo do convênio.");
            }
            //descobrir o tipo
            if (!this.codigoConvenioAlterado.equals("TODOS")) {
                ConvenioCobrancaVO convenioASerAlterado = getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(Integer.parseInt(this.codigoConvenioAlterado), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (convenioASerAlterado != null && convenioASerAlterado.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PINBANK)) {
                    throw new Exception("Você colocou o convênio destino para PinBank. Não é possível realizar migrações assim, por questões" +
                            " técnicas da própria PinBank. Para que as suas autorizações sejam migradas para a PinBank é necessário excluir todas as autorizações de cobrança e cadastrar uma a uma manualmente." +
                            "Você pode entrar em contato com a pacto para excluirmos as autorizações de uma vez só caso deseje.");
                }
            }
        }

        // Valida se tem permissao de acesso (perfil acesso) e se o campo Convenio a ser alterado contem valor
        if (this.acessoAjusteConvenio && !UteisValidacao.emptyString(this.codigoConvenioAlterado)) {
            List<OperadorasExternasAprovaFacilEnum> listOperadoras = OperadorasExternasAprovaFacilEnum.getListOperadoras();

            if (this.codigoConvenioAlterado.equals("TODOS")) {
                for (ConvenioCobrancaVO convenioVO : getListaConveniosCompleta()) {
                    for (ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO : convenioVO.getConfiguracoesEmpresa()) {
                        if (convenioCobrancaEmpresaVO.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                            if (!convenioVO.getTipo().isPix() && convenioCobrancaVO.getTipo().getTipoAutorizacao().equals(convenioVO.getTipo().getTipoAutorizacao())) {
                                getFacade().getConvenioCobranca().ajustarConvenio(getEmpresaLogado().getCodigo(), convenioVO.getCodigo(), convenioVO.getTipo().getTipoAutorizacao(), convenioCobrancaVO.getCodigo(), listOperadoras);
                            } else if (convenioVO.getTipo().isPix() && convenioCobrancaVO.getTipo().isPix()) {
                                getFacade().getConvenioCobranca().ajustarConvenio(getEmpresaLogado().getCodigo(), convenioVO.getCodigo(), convenioVO.getTipo().getTipoAutorizacao(), convenioCobrancaVO.getCodigo(), listOperadoras);
                            }
                        }
                    }
                }
                incluirLogAjusteConvenio("TODOS", convenioCobrancaVO.getCodigo().toString());
            } else {
                incluirLogAjusteConvenio(getCodigoConvenioAlterado(), convenioCobrancaVO.getCodigo().toString());
                ConvenioCobrancaVO convenioVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(Integer.valueOf(getCodigoConvenioAlterado()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getFacade().getConvenioCobranca().ajustarConvenio(getEmpresaLogado().getCodigo(), convenioVO.getCodigo(), convenioVO.getTipo().getTipoAutorizacao(), convenioCobrancaVO.getCodigo(), listOperadoras);
            }
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>ConvenioCobranca</code>. Caso o objeto seja novo (ainda não gravado
     * no BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    private void gravar(boolean origemModalTokenOperacao) throws Exception {
        limparMsg();
        setAbrirFecharModalTokenOperacao("");

        setarNumeroContrato();
        validarRemessasPendentes();
        validarUtilizadoRetentativa();
        gravarRecursoSistemaSalvarConvenio(convenioCobrancaVO);
        boolean novoObj = convenioCobrancaVO.isNovoObj();

        if (this.convenioCobrancaVO.isNovoObj() && this.convenioCobrancaVO.getTipo() != null && this.convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS)) {
            setGerarBoletoHibrido(false);
        }

        if (this.convenioCobrancaVO.getContaEmpresa() != null &&
                !UteisValidacao.emptyNumber(this.convenioCobrancaVO.getContaEmpresa().getCodigo())) {
            this.convenioCobrancaVO.setContaEmpresa(getFacade().getContaCorrente().consultarPorChavePrimaria(this.convenioCobrancaVO.getContaEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        }

        //convênio pix pjbank sempre será tipo de pix para gerar somente o qr code
        if (this.convenioCobrancaVO.isPixPjBank()) {
            this.convenioCobrancaVO.setTipoBoletoPJBank(TipoBoletoPJBankEnum.PIX);
        }

        validarDadosGravarComUploadArquivo();

        List<ConvenioCobrancaEmpresaVO> listaAnterior = new ArrayList<>();
        if (convenioCobrancaVO.isNovoObj()) {

            //CRIANDO NOVO

            processarCredenciaisConvenioNovo();

            //Novo convênio Stone
            if (isHttps()) { //validar somente em ambiente produtivo
                try {
                    if (convenioCobrancaVO.isStone() || convenioCobrancaVO.isStoneV5()) {
                        boolean isUsuarioOAMD = !UteisValidacao.emptyString(getUsuarioLogado().getUserOamd().trim()) && !getUsuarioLogado().getUserOamd().equalsIgnoreCase("undefined");
                        validarSeUsuarioTemPermissaoCadastrarNovoConvenioStone(isUsuarioOAMD);
                    }
                } catch (Exception ex) {
                    montarAviso(ex.getMessage());
                    return;
                }
            }

            //Stone v5 sempre trabalha com verificação zero dolar, no modelo gateway e no PSP. Só não exibe isso na tela
            if (convenioCobrancaVO.isStoneV5()) {
                convenioCobrancaVO.setVerificacaoZeroDollar(true);
            }

            getFacade().getConvenioCobranca().incluir(convenioCobrancaVO);
            notificarRecursoSistemaCobranca();
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                notificarRecursoEmpresa(RecursoSistema.CONVENIO_COBRANCA_GETNET_ONLINE);
            }
            convenioCobrancaVO.setPermiteVisualizarCredenciaisOriginais(isUsuarioPodeVisualizarCredenciaisOriginaisConvenio());
            convenioCobrancaVOClone = (ConvenioCobrancaVO) convenioCobrancaVO.getClone(true);
            incluirLogInclusao();
        } else {
            //EDITANDO

            processarCredenciaisConvenioExistente();

            //Stone v5 sempre trabalha com verificação zero dolar, no modelo gateway e no PSP. Só não exibe isso na tela
            if (convenioCobrancaVO.isStoneV5()) {
                convenioCobrancaVO.setVerificacaoZeroDollar(true);
            }

            boolean ignorarModalTokenOperacao = false;
            boolean alterouCampoSensivel = alterouCampoSensivel();

            if (!isAmbienteDesenvolvimento()) {
                boolean isUsuarioOAMD = !UteisValidacao.emptyString(getUsuarioLogado().getUserOamd().trim()) && !getUsuarioLogado().getUserOamd().equalsIgnoreCase("undefined");
                if (isUsuarioOAMD) {
                    ConvenioCobrancaVO convenioCobrancaVOAntesAlteracao = (ConvenioCobrancaVO) convenioCobrancaVO.getObjetoVOAntesAlteracao();
                    boolean estaAlterandoAmbienteProducaoParaHomologacao = (!convenioCobrancaVOAntesAlteracao.getAmbiente().equals(convenioCobrancaVO.getAmbiente()) &&
                            convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO));
                    boolean isUsuarioOAMDPodeAlterarConvenio = isUsuarioOAMDPodeAlterarConvenio();
                    if (estaAlterandoAmbienteProducaoParaHomologacao && !isUsuarioOAMDPodeAlterarConvenio) {
                        montarAviso("Seu usuário do OAMD não tem permissão para alterar convênios de cobrança e por isso você não pode alterar um convênio de Produção para Homologação pois os convênios de homologação não validam alterações em campos sensíveis.");
                        return;
                    }
                }


                //Alteração de códigos de autenticação Stone E Facilite
                try {
                    validarSeUsuarioAlterouCodigoAutenticacaoStoneOuFacilite(isUsuarioOAMD);
                } catch (Exception ex) {
                    montarAviso(ex.getMessage());
                    return;
                }

                if (isUsuarioOAMD && alterouCampoSensivel) {
                    if (isUsuarioOAMDPodeAlterarConvenio()) {
                        if (isUsuarioOAMDPodeIgnorarTokenOperacao() || convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO)) {
                            //homologação não precisa pedir token
                            ignorarModalTokenOperacao = true;
                        }
                    } else {
                        montarAviso("Seu usuário do OAMD não tem permissão para alterar campos sensíveis em convênios de cobrança");
                        return;
                    }
                }
            }


            boolean solicitarToken = !isAmbienteDesenvolvimento()
                    && !origemModalTokenOperacao
                    && alterouCampoSensivel
                    && !ignorarModalTokenOperacao
                    && !isRedePratique(getKey());

            Uteis.logarDebug("Gravando convênio --> isAmbienteDesenvolvimento | " + isAmbienteDesenvolvimento());
            Uteis.logarDebug("Gravando convênio --> origemModalTokenOperacao | " + origemModalTokenOperacao);
            Uteis.logarDebug("Gravando convênio --> alterouCampoSensivel | " + alterouCampoSensivel);
            Uteis.logarDebug("Gravando convênio --> isRedePratique | " + isRedePratique(getKey()));

            //###Validar se é para solicitar Token para prosseguir o gravar###
            if (solicitarToken) {
                //Validar dados antes de pedir o token (código de autenticação)
                ConvenioCobrancaVO.validarDados(convenioCobrancaVO);
                TokenOperacaoControle control = getControlador(TokenOperacaoControle.class);
                setAbrirFecharModalTokenOperacao("Richfaces.showModalPanel('modalTokenOperacao');document.getElementById('formModalTokenOperacao:inputToken1').focus();");
                control.init("",
                        this, "validarTokenEGravarConvenio", "", "",
                        "", getLabelsCamposExibirNoModalTokenOperacao(), getQtdCamposSensiveisAlterados(), "",
                        RecursoSistema.CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_GEROU);
                //enviar notificação push depois que abrir o modal:
                enviaNotificacaoPushTokenPactoApp();
                return;
            } else {
                convenioCobrancaVOClone = (ConvenioCobrancaVO) convenioCobrancaVO.getClone(true);
                getFacade().getConvenioCobranca().alterar(convenioCobrancaVO);
                incluirLogAlteracao();
            }

            listaAnterior = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobranca(convenioCobrancaVO.getCodigo());
            notificarRecursoSistemaCobranca();
        }

        if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            try {
                Integer numContratoAux = Integer.parseInt(convenioCobrancaVO.getNumeroContrato());
                if (numContratoAux == 0) {
                    convenioCobrancaVO.setNumeroContrato("");
                }
                convenioCobrancaVO.setNumeroContrato(numContratoAux.toString().trim());
            } catch (Exception e) {
            }
        }

        //configuração SPLIT rateio
        getFacade().getConvenioCobrancaRateio().gravar(convenioCobrancaVO, getUsuarioLogado());

        if (convenioCobrancaVOClone != null) {
            gravarLogConvenioEmpresa(convenioCobrancaVO, listaAnterior, getUsuarioLogado());
        }

        tratarGravarAbaAjusteConvenio();

        //Upload de Arquivos (Certificados)
        verificarUploadsArquivos();

        if (this.getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PIX) && !this.getConvenioCobrancaVO().isPixPjBank()) {
            if (this.getConvenioCobrancaVO().isPixInter()) {
                //formatar chave pix cnpj
                if (this.getConvenioCobrancaVO().getPixChave().length() == 18) { // CNPJ mascarado
                    String CNPJSemMascara = Uteis.formatarCpfCnpj(this.getConvenioCobrancaVO().getPixChave(), true);
                    this.getConvenioCobrancaVO().setPixChave(CNPJSemMascara);
                }
            }
            adicionarModuloPix();
            //configurar webhook automaticamente quando criar o novo convênio
            if (novoObj) {
                configurarWebhookPix();
            }
        }
        montarSucessoGrowl("Dados gravados com sucesso!");
    }

    private boolean isRedePratique(String key) {
        //Rede pratique não é pra pedir token para nenhuma operação
        OAMDService oamdService;
        RedeEmpresaVO rede;
        try {
            oamdService = new OAMDService();

            //Ver se já existe no mapa de já consultados anteriormente
            rede = redeEmpresaVosJaConsultados.getOrDefault(key, null);

            if (rede == null) {
                rede = oamdService.consultarRedeEmpresa(key);
            }

            if (rede != null) {
                redeEmpresaVosJaConsultados.put(key, rede);
                //CHAVEREDE DA PRATIQUE e POWERGYM SC (também da pratique)
                if (rede.getChaverede().equals("341b908afd7637c1d5b09f248d3498f1") || rede.getChaverede().equals("515b3b832de8674a8de9713418abe6ac")) {
                    return true;
                }
            }
        } catch (Exception ex) {
            return false;
        } finally {
            oamdService = null;
        }
        return false;
    }

    public void processarCredenciaisConvenioExistente() {
        // o sistema precisa veririficar se está vazio separadamente do nulo igual está abixo. Existe diferenças lá na tela, não mexer nessas validações.
        // o usuário pode apagar o valor do campo e tentar gravar por exemplo, neste cenário deve deixar preencher o vazio pra vair na validação de dados obrigatorios lá na frente
        if (getConvenioCobrancaVO().codigoAutenticacao01Exibir == "" || (getConvenioCobrancaVO().codigoAutenticacao01Exibir != null && !getConvenioCobrancaVO().codigoAutenticacao01Exibir.contains("**") &&
                !getConvenioCobrancaVO().codigoAutenticacao01Exibir.equals(getConvenioCobrancaVO().getCodigoAutenticacao01()))) {
            getConvenioCobrancaVO().setCodigoAutenticacao01(getConvenioCobrancaVO().codigoAutenticacao01Exibir);
        }
        if (getConvenioCobrancaVO().codigoAutenticacao02Exibir == "" || (getConvenioCobrancaVO().codigoAutenticacao02Exibir != null && !getConvenioCobrancaVO().codigoAutenticacao02Exibir.contains("**") &&
                !getConvenioCobrancaVO().codigoAutenticacao02Exibir.equals(getConvenioCobrancaVO().getCodigoAutenticacao02()))) {
            getConvenioCobrancaVO().setCodigoAutenticacao02(getConvenioCobrancaVO().codigoAutenticacao02Exibir);
        }
        if (getConvenioCobrancaVO().codigoAutenticacao03Exibir == "" || (getConvenioCobrancaVO().codigoAutenticacao03Exibir != null && !getConvenioCobrancaVO().codigoAutenticacao03Exibir.contains("**") &&
                !getConvenioCobrancaVO().codigoAutenticacao03Exibir.equals(getConvenioCobrancaVO().getCodigoAutenticacao03()))) {
            getConvenioCobrancaVO().setCodigoAutenticacao03(getConvenioCobrancaVO().codigoAutenticacao03Exibir);
        }
        if (getConvenioCobrancaVO().codigoAutenticacao04Exibir == "" || (getConvenioCobrancaVO().codigoAutenticacao04Exibir != null && !getConvenioCobrancaVO().codigoAutenticacao04Exibir.contains("**") &&
                !getConvenioCobrancaVO().codigoAutenticacao04Exibir.equals(getConvenioCobrancaVO().getCodigoAutenticacao04()))) {
            getConvenioCobrancaVO().setCodigoAutenticacao04(getConvenioCobrancaVO().codigoAutenticacao04Exibir);
        }
        if (getConvenioCobrancaVO().codigoAutenticacao05Exibir == "" || (getConvenioCobrancaVO().codigoAutenticacao05Exibir != null && !getConvenioCobrancaVO().codigoAutenticacao05Exibir.contains("**") &&
                !getConvenioCobrancaVO().codigoAutenticacao05Exibir.equals(getConvenioCobrancaVO().getCodigoAutenticacao05()))) {
            getConvenioCobrancaVO().setCodigoAutenticacao05(getConvenioCobrancaVO().codigoAutenticacao05Exibir);
        }
        if (getConvenioCobrancaVO().codigoAutenticacao06Exibir == "" || (getConvenioCobrancaVO().codigoAutenticacao06Exibir != null && !getConvenioCobrancaVO().codigoAutenticacao06Exibir.contains("**") &&
                !getConvenioCobrancaVO().codigoAutenticacao06Exibir.equals(getConvenioCobrancaVO().getCodigoAutenticacao06()))) {
            getConvenioCobrancaVO().setCodigoAutenticacao06(getConvenioCobrancaVO().codigoAutenticacao06Exibir);
        }
        if (getConvenioCobrancaVO().codigoAutenticacao07Exibir == "" || (getConvenioCobrancaVO().codigoAutenticacao07Exibir != null && !getConvenioCobrancaVO().codigoAutenticacao07Exibir.contains("**") &&
                !getConvenioCobrancaVO().codigoAutenticacao07Exibir.equals(getConvenioCobrancaVO().getCodigoAutenticacao07()))) {
            getConvenioCobrancaVO().setCodigoAutenticacao07(getConvenioCobrancaVO().codigoAutenticacao07Exibir);
        }
    }

    public void processarCredenciaisConvenioNovo() throws Exception {
        if (!UteisValidacao.emptyString(getConvenioCobrancaVO().codigoAutenticacao01Exibir)) {
            getConvenioCobrancaVO().setCodigoAutenticacao01(getConvenioCobrancaVO().codigoAutenticacao01Exibir);
        }
        if (!UteisValidacao.emptyString(getConvenioCobrancaVO().codigoAutenticacao02Exibir)) {
            getConvenioCobrancaVO().setCodigoAutenticacao02(getConvenioCobrancaVO().codigoAutenticacao02Exibir);
        }
        if (!UteisValidacao.emptyString(getConvenioCobrancaVO().codigoAutenticacao03Exibir)) {
            getConvenioCobrancaVO().setCodigoAutenticacao03(getConvenioCobrancaVO().codigoAutenticacao03Exibir);
        }
        if (!UteisValidacao.emptyString(getConvenioCobrancaVO().codigoAutenticacao04Exibir)) {
            getConvenioCobrancaVO().setCodigoAutenticacao04(getConvenioCobrancaVO().codigoAutenticacao04Exibir);
        }
        if (!UteisValidacao.emptyString(getConvenioCobrancaVO().codigoAutenticacao05Exibir)) {
            getConvenioCobrancaVO().setCodigoAutenticacao05(getConvenioCobrancaVO().codigoAutenticacao05Exibir);
        }
        if (!UteisValidacao.emptyString(getConvenioCobrancaVO().codigoAutenticacao06Exibir)) {
            getConvenioCobrancaVO().setCodigoAutenticacao06(getConvenioCobrancaVO().codigoAutenticacao06Exibir);
        }
        if (!UteisValidacao.emptyString(getConvenioCobrancaVO().codigoAutenticacao07Exibir)) {
            getConvenioCobrancaVO().setCodigoAutenticacao07(getConvenioCobrancaVO().codigoAutenticacao07Exibir);
        }
        validarIntegridadeCredenciais();
    }

    private void validarIntegridadeCredenciais() throws Exception {
        if ((!UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao01()) && getConvenioCobrancaVO().getCodigoAutenticacao01().contains("**")) ||
                (!UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao02()) && getConvenioCobrancaVO().getCodigoAutenticacao02().contains("**")) ||
                (!UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao03()) && getConvenioCobrancaVO().getCodigoAutenticacao03().contains("**")) ||
                (!UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao04()) && getConvenioCobrancaVO().getCodigoAutenticacao04().contains("**")) ||
                (!UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao05()) && getConvenioCobrancaVO().getCodigoAutenticacao05().contains("**")) ||
                (!UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao06()) && getConvenioCobrancaVO().getCodigoAutenticacao06().contains("**")) ||
                (!UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao07()) && getConvenioCobrancaVO().getCodigoAutenticacao07().contains("**"))) {
            throw new Exception("Foi identificado credenciais mascaradas antes de gravar o convênio. Informe a credencial sem a máscara e tente novamente");
        }
    }

    private void verificarUploadsArquivos() throws Exception {
        if (getConvenioCobrancaVO().isPixInter()) {
            if (isFezUploadArquivo1() && isFezUploadArquivo2() && !possuiArquivo1) {
                incluirConvenioCobrancaArquivoBanco();
                setPossuiArquivo1(true);
                setNomeArquivo1(convenioCobrancaArquivo1.getNomeArquivoOriginal());
            }
        }
        if (isFezUploadArquivo1() && !getConvenioCobrancaVO().isPixInter()) {
            incluirConvenioCobrancaArquivo1();
            setPossuiArquivo1(true);
            setNomeArquivo1(convenioCobrancaArquivo1.getNomeArquivoOriginal());
        }
        if (isFezUploadArquivo2() && !getConvenioCobrancaVO().isPixInter()) {
            incluirConvenioCobrancaArquivo2();
            setPossuiArquivo2(true);
            setNomeArquivo2(convenioCobrancaArquivo2.getNomeArquivoOriginal());
        }
    }

    private void incluirConvenioCobrancaArquivoBanco() throws Exception {
        Connection con;
        PCertService pCertService;
        try {
            con = Conexao.getFromSession();
            pCertService = new PCertService(con);

            String certificate_Base64 = new Base64().encodeAsString(getConvenioCobrancaArquivo1().getArquivoPlenoString().getBytes()).replaceAll("\n","");
            String private_key_Base64 = new Base64().encodeAsString(getConvenioCobrancaArquivo2().getArquivoPlenoString().getBytes()).replaceAll("\n","");

            byte[] certPFX = pCertService.obterPFXAtravesDoPublicoEPrivado(certificate_Base64, private_key_Base64);

            //incluir certificado pfx
            ConvenioCobrancaArquivoVO obj = new ConvenioCobrancaArquivoVO();
            obj.setConvenioCobranca(getConvenioCobrancaVO().getCodigo());
            if (this.getConvenioCobrancaVO().isPixInter()) {
                obj.setNomeArquivoOriginal("Cert_Banco_Inter.pfx");
            } else {
                obj.setNomeArquivoOriginal("Cert_Banco_Pix.pfx");
            }
            obj.setArquivo(certPFX);
            obj.setDataUpload(Calendario.hoje());
            obj.setUsuario(getUsuarioLogado().getCodigo());
            obj.setConvenioCobrancaVO(getConvenioCobrancaVO());
            getFacade().getConvenioCobrancaArquivo().incluir(obj);
            setConvenioCobrancaArquivo1(obj);
        } catch (Exception ex) {
            throw ex;
        } finally {
            con = null;
            pCertService = null;
        }
    }

    private void validarDadosGravarComUploadArquivo() throws ConsistirException {
        if (!isPossuiArquivo1()) {
            if (getConvenioCobrancaVO().isPixBradesco()) {
                //preencheu a senha e não fez upload do arquivo
                if (!UteisValidacao.emptyString(getSenhaArquivo()) && !isFezUploadArquivo1()) {
                    //não fez upload do arquivo, mas preencheu a senha
                    throw new ConsistirException("Você preencheu a senha do certificado, mas não fez o upload do arquivo. Quando adicionar o arquivo, não se esqueça de clicar em \"Enviar\"");
                }
                //fez upload do arquivo e não preencheu a senha
                if (UteisValidacao.emptyString(getSenhaArquivo()) && isFezUploadArquivo1()) {
                    throw new ConsistirException("Você fez o upload do arquivo certificado, mas não preencheu a senha do arquivo.");
                }
            }
            if (getConvenioCobrancaVO().isPixInter()) {
                if (convenioCobrancaArquivo1 == null || UteisValidacao.emptyString(convenioCobrancaArquivo1.getNomeArquivoOriginal())) {
                    limparCamposUploadArquivo1();
                    limparCamposUploadArquivo2();
                    throw new ConsistirException("Você não fez o upload do arquivo Certificado digital A1 público (.crt). Quando adicionar o arquivo, não se esqueça de clicar em \"Enviar\"");
                }
                if (convenioCobrancaArquivo2 == null || UteisValidacao.emptyString(convenioCobrancaArquivo2.getNomeArquivoOriginal())) {
                    limparCamposUploadArquivo1();
                    limparCamposUploadArquivo2();
                    throw new ConsistirException("Você não fez o upload do arquivo Chave Privada (.key). Quando adicionar o arquivo, não se esqueça de clicar em \"Enviar\"");
                }
            }
        }

    }

    public void validarSeUsuarioTemPermissaoCadastrarNovoConvenioStone(boolean isUsuarioOAMD) throws Exception {

            //usuário comum do sistema não pode criar novo convênio Stone
            if (!isUsuarioOAMD) {
                    throw new Exception("Somente a Pacto possui permissão para criar novos convênios STONE");
            } else {
                //Usuários OAMD
                Uteis.logarDebug("NOVO CONVÊNIO STONE | IDENTIFICAR USUARIO OAMD CONVENIO COBRANCA | CHAVE " + getKey() + " Usuário: " + getUsuarioLogado().getUserOamd() + " --> É um usuário OAMD, vou verificar se tem permissão alterar convênios Stone");
                if (!isUsuarioOAMDPodeAlterarCodigoAutenticacaoStone()) {
                    throw new Exception("Seu usuário do OAMD não tem permissão para criar um novo convênio de Cobrança STONE, procure seu superior.");
                }
            }
    }

    public void validarSeUsuarioAlterouCodigoAutenticacaoStoneOuFacilite(boolean isUsuarioOAMD) throws Exception {

        ConvenioCobrancaVO convenioCobrancaVOAntesAlteracao = (ConvenioCobrancaVO) convenioCobrancaVO.getObjetoVOAntesAlteracao();

        boolean alterouCodigoAutenticacaoStone = convenioCobrancaVO.isStone() &&
                !convenioCobrancaVOAntesAlteracao.getCodigoAutenticacao01().equals(convenioCobrancaVO.getCodigoAutenticacao01()) ||
                !convenioCobrancaVOAntesAlteracao.getCodigoAutenticacao02().equals(convenioCobrancaVO.getCodigoAutenticacao02());

        boolean alterouCodigoAutenticacaoFacilite = convenioCobrancaVO.isFacilitePay() &&
                !convenioCobrancaVOAntesAlteracao.getCodigoAutenticacao01().equals(convenioCobrancaVO.getCodigoAutenticacao01());

        if (alterouCodigoAutenticacaoStone || alterouCodigoAutenticacaoFacilite) {
            //usuário comum do sistema não pode alterar
            if (!isUsuarioOAMD) {
                if (convenioCobrancaVO.isStone()) {
                    throw new Exception("Os campos: " + convenioCobrancaVO.getLabelCodigoAutenticacao01().replace(":","") + " e " +
                            convenioCobrancaVO.getLabelCodigoAutenticacao02().replace(":","") + " da STONE só podem ser alterados pela Pacto");
                } else if (convenioCobrancaVO.isFacilitePay()) {
                    throw new Exception("O campo: " + convenioCobrancaVO.getLabelCodigoAutenticacao01().replace(":","") + " da FYPAY só pode ser alterado pela Pacto");
                }
            } else {
                //Usuários OAMD
                if (convenioCobrancaVO.isStone() && !isUsuarioOAMDPodeAlterarCodigoAutenticacaoStone()) {
                    throw new Exception("Seu usuário do OAMD não tem permissão para alterar os campos: " + convenioCobrancaVO.getLabelCodigoAutenticacao01().replace(":","") + " e " +
                            convenioCobrancaVO.getLabelCodigoAutenticacao02().replace(":","") + " da STONE, procure seu superior.");
                } else if (convenioCobrancaVO.isFacilitePay() && !isUsuarioOAMDPodeAlterarCodigoAutenticacaoFacilite()) {
                    throw new Exception("Seu usuário do OAMD não tem permissão para alterar o campo: " + convenioCobrancaVO.getLabelCodigoAutenticacao01().replace(":","") + " da FYPAY, procure seu superior.");
                }
            }
        }
    }

    private boolean isUsuarioOAMDPodeAlterarConvenio() throws Exception {
        return getUsuarioLogado().isUsuarioLogadoPodeAlterarConvenio();
    }

    public boolean isUsuarioOAMDPodeIgnorarTokenOperacao() throws Exception {
        return getUsuarioLogado().isIgnorarTokenOperacao();
    }

    private boolean isUsuarioOAMDPodeAlterarCodigoAutenticacaoStone() throws Exception {
            return getUsuarioLogado().isUsuarioLogadoPodeAlterarCodigoAutenticacaoStone();
    }

    private boolean isUsuarioOAMDPodeAlterarCodigoAutenticacaoFacilite() throws Exception {
        return getUsuarioLogado().isUsuarioLogadoPodeAlterarCodigoAutenticacaoFacilitePay();
    }

    private boolean isUsuarioPodeVisualizarCredenciaisOriginaisConvenio() throws Exception {
        if (isAmbienteDesenvolvimento()) {
            Uteis.logarDebug("**UsuarioPodeVisualizarCredenciaisOriginaisConvenio** - AMBIENTE DEV. O usuário pode visualizar as credenciais originais do convênio");
            return true;
        } else {
            Uteis.logarDebug("**UsuarioPodeVisualizarCredenciaisOriginaisConvenio** - usuariooamd: " + getUsuarioLogado().getUserOamd());
            boolean isUsuarioOAMD = !UteisValidacao.emptyString(getUsuarioLogado().getUserOamd().trim()) && !getUsuarioLogado().getUserOamd().equalsIgnoreCase("undefined");
            Uteis.logarDebug("**UsuarioPodeVisualizarCredenciaisOriginaisConvenio** - isUsuarioOAMD: " + isUsuarioOAMD);
            if (!isUsuarioOAMD) { //cliente final
                return false;
            } else {
                boolean isUsuarioLogadoPodeVisualizarCredenciaisOriginaisConvenio = getUsuarioLogado().isUsuarioLogadoPodeVisualizarCredenciaisOriginaisConvenio();
                Uteis.logarDebug("**UsuarioPodeVisualizarCredenciaisOriginaisConvenio** - isUsuarioLogadoPodeVisualizarCredenciaisOriginaisConvenio:" + isUsuarioLogadoPodeVisualizarCredenciaisOriginaisConvenio);
                return isUsuarioLogadoPodeVisualizarCredenciaisOriginaisConvenio;
            }
        }
    }

    private void configurarWebhookPix() {
        PixWebhookService pixWebhookService;
        Connection con;
        try {
            con = Conexao.getFromSession();
            pixWebhookService = new PixWebhookService(con);
            boolean sucesso = pixWebhookService.configurarUrlCallback(convenioCobrancaVO, getKey());
            if (sucesso) {
                String msgSucesso = "Webhook configurado com sucesso automaticamente ao criar convênio: Chave | " + getKey() + "| Convenio |  " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao();
                Uteis.logarDebug(msgSucesso);
            } else {
                String erro = "Não foi possível configurar Webhook automaticamente ao criar convênio: Chave | " + getKey() + "| Convenio |  " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao();
                Uteis.logarDebug(erro);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            String erro = "Não foi possível configurar Webhook automaticamente ao criar convênio: Chave | " + getKey() + "| Convenio |  " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao() + " | ERRO: " + ex.getMessage();
            Uteis.logarDebug(erro);
        } finally {
            pixWebhookService = null;
            con = null;
        }
    }

    public void validarTokenEGravarConvenio() {
        try {
            setMsgAlert("Richfaces.hideModalPanel('modalDesbloqueioCobrancasAutomaticas');");
            limparMsg();

            TokenOperacaoControle controlToken = (TokenOperacaoControle) JSFUtilities.getFromSession(TokenOperacaoControle.class.getSimpleName());

            try {
                controlToken.validarToken(RecursoSistema.CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_SUCESSO, RecursoSistema.CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_ERRO);
            } catch (Exception ex) {
                controlToken.montarAviso("Ops!", ex.getMessage());
                return;
            }

            //chegou até aqui então o token é valido, já pode continuar com a alteração do convênio
            gravar(true);

            //colocar Token como "Utilizado" para não permitir utilizar o mesmo token mais;
            controlToken.inutilizarToken();
            controlToken.fecharModal();
            controlToken.montarSucessoGrowl("Dados gravados com sucesso!");

        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public void acaoBloquearDesbloquearCobrancas() throws Exception {
        setMsgAlert("");
        ConvenioCobrancaVO convenioCobrancaVOAntesAlteracao = (ConvenioCobrancaVO) convenioCobrancaVO.getObjetoVOAntesAlteracao();
        boolean desbloqueouCobrancasAutomaticas = convenioCobrancaVOAntesAlteracao.isBloquearCobrancaAutomatica() == true && convenioCobrancaVO.isBloquearCobrancaAutomatica() == false;
        if (desbloqueouCobrancasAutomaticas) {
            preencherDadosDesbloqueioCobrancas();
        }
    }

    public void validarTokenEGravarDesbloqueioCobrancas() {
        try {
            limparMsg();

            TokenOperacaoControle controlToken = (TokenOperacaoControle) JSFUtilities.getFromSession(TokenOperacaoControle.class.getSimpleName());

            try {
                controlToken.validarToken(RecursoSistema.CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_SUCESSO, RecursoSistema.CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_ERRO);
            } catch (Exception ex) {
                controlToken.montarAviso("Ops!", ex.getMessage());
                return;
            }

            //chegou até aqui então o token é valido, já pode continuar fazer o update no desbloqueio de cobranças
             getFacade().getConvenioCobranca().alterarBloquearCobrancasAutomaticas(convenioCobrancaVO.getCodigo(), false);

            //colocar Token como "Utilizado" para não permitir utilizar o mesmo token mais;
            controlToken.inutilizarToken();
            controlToken.fecharModal();
            controlToken.montarSucessoGrowl("Dados gravados com sucesso!");
            convenioCobrancaVO.setBloquearCobrancaAutomatica(false);
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public boolean alterouCampoSensivel() {
        //Varíaveis controlador
        setLabelsCamposExibirNoModalTokenOperacao("");
        setQtdCamposSensiveisAlterados(0);

        //Varíaveis método
        StringBuilder textoCamposExibirNoModal = new StringBuilder();
        boolean alterouCampoSensivel = false;
        int qtdCampoSensiveisAlterados = 0;
        ConvenioCobrancaVO convenioCobrancaVOAntesAlteracao = (ConvenioCobrancaVO) convenioCobrancaVO.getObjetoVOAntesAlteracao();

        if (!convenioCobrancaVOAntesAlteracao.getCodigoAutenticacao01().equals(convenioCobrancaVO.getCodigoAutenticacao01())) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"").append(convenioCobrancaVO.getLabelCodigoAutenticacao01().replace(":","")).append("\"").append(", ");
        }

        if (!convenioCobrancaVOAntesAlteracao.getCodigoAutenticacao02().equals(convenioCobrancaVO.getCodigoAutenticacao02())) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"").append(convenioCobrancaVO.getLabelCodigoAutenticacao02().replace(":","")).append("\"").append(", ");
        }

        if (!convenioCobrancaVOAntesAlteracao.getCodigoAutenticacao03().equals(convenioCobrancaVO.getCodigoAutenticacao03())) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"").append(convenioCobrancaVO.getLabelCodigoAutenticacao03().replace(":","")).append("\"").append(", ");
        }

        if (!convenioCobrancaVOAntesAlteracao.getCodigoAutenticacao04().equals(convenioCobrancaVO.getCodigoAutenticacao04())) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"").append(convenioCobrancaVO.getLabelCodigoAutenticacao04().replace(":","")).append("\"").append(", ");
        }

        //desbloqueou ou bloqueou cobrança automática
        if (convenioCobrancaVOAntesAlteracao.isBloquearCobrancaAutomatica() != convenioCobrancaVO.isBloquearCobrancaAutomatica()) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"").append("Bloquear cobranças automáticas").append("\"").append(", ");
        }

        //alterou situação (ativo/inativo)
        if (!convenioCobrancaVOAntesAlteracao.getSituacao().equals(convenioCobrancaVO.getSituacao())) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"").append("Situação").append("\"").append(", ");
        }

        if (alterouCampoSensivel) {
            setQtdCamposSensiveisAlterados(qtdCampoSensiveisAlterados);
            //remover a última vírgula
            textoCamposExibirNoModal = new StringBuilder(textoCamposExibirNoModal.toString().substring(0, textoCamposExibirNoModal.toString().lastIndexOf(", ")));
            setLabelsCamposExibirNoModalTokenOperacao(textoCamposExibirNoModal.toString());
        }
        return alterouCampoSensivel;
    }

    public void enviaNotificacaoPushTokenPactoApp() {
        try {
            ServicoNotificacaoPush.enviaNotificacaoPushTokenPactoApp(getKey(), getEmpresaLogado().getCodigo(), getUsuarioLogado().getCodigo());
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível enviar notificação push do token para Pacto App: " + ex.getMessage());
        }
    }

    private void gravarLogConvenioEmpresa(ConvenioCobrancaVO obj, List<ConvenioCobrancaEmpresaVO> listaAnterior, UsuarioVO usuarioVO) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("CONVENIOCOBRANCA");
            log.setNomeEntidadeDescricao("CONVENIOCOBRANCA");
            log.setDescricao("CONVENIOCOBRANCAEMPRESA");
            log.setChavePrimaria(obj.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("EMPRESAS");
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setValorCampoAnterior(obterArrayLog(listaAnterior).toString());
            log.setValorCampoAlterado(obterArrayLog(obj.getConfiguracoesEmpresa()).toString());

            getFacade().getLog().incluirSemCommit(log);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private JSONArray obterArrayLog(List<ConvenioCobrancaEmpresaVO> lista) {
        JSONArray array = new JSONArray();
        for (ConvenioCobrancaEmpresaVO obj : lista) {
            JSONObject json = new JSONObject();
            json.put("empresa", "Cod. "+ obj.getEmpresa().getCodigo() + " - " + obj.getEmpresa().getNome());
            array.put(json);
        }
        return array;
    }

    private void gravarRecursoSistemaSalvarConvenio(ConvenioCobrancaVO convenioCobrancaVO) {
        switch (convenioCobrancaVO.getTipo()) {
            case DCC_STONE_ONLINE:
                notificarRecursoEmpresa(RecursoSistema.CRIAR_CONVENIO_STONE_ONLINE);
                break;
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>ConvenioCobrancaVO</code> Após a exclusão ela automaticamente
     * aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getConvenioCobranca().excluir(convenioCobrancaVO);
            incluirLogExclusao();
            setConvenioCobrancaVO(new ConvenioCobrancaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"conveniocobranca\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"conveniocobranca\" violates foreign key")){
                setMensagemDetalhada("Este convênio de cobrança não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>TipoRemessa</code>.
     */
    public void montarListaSelectItemTipoRemessa(String prm) throws Exception {
        List resultadoConsulta = consultarTipoRemessaPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            TipoRemessaVO obj = (TipoRemessaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemTipoRemessa(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>TipoRemessa</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>TipoRemessa</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemTipoRemessa() {
        try {
            montarListaSelectItemTipoRemessa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarTipoRemessaPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getTipoRemessa().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>ContaEmpresa</code>.
     */
    public void montarListaSelectItemContaEmpresa(String prm) throws Exception {
        List<Integer> codsJaInCluidos = new ArrayList<Integer>();
        listaContaCorrenteEmpresa = new ArrayList<ContaCorrenteVO>();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        for (ConvenioCobrancaEmpresaVO conv : getConvenioCobrancaVO().getConfiguracoesEmpresa()) {
            if (!UteisValidacao.emptyNumber(conv.getEmpresa().getCodigo())) {
                EmpresaVO obj = getFacade().getEmpresa().consultarPorChavePrimaria(conv.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                for (ContaCorrenteEmpresaVO ccEmpresaVO : obj.getContaCorrenteEmpresaVOs()) {
                    if (codsJaInCluidos.contains(ccEmpresaVO.getContaCorrente().getCodigo())) {
                        continue;
                    }
                    codsJaInCluidos.add(ccEmpresaVO.getContaCorrente().getCodigo());
                    objs.add(new SelectItem(ccEmpresaVO.getContaCorrente().getCodigo(), ccEmpresaVO.getDescricaoContaCorrente()));
                    listaContaCorrenteEmpresa.add(ccEmpresaVO.getContaCorrente());
                }
            }
        }
        setListaSelectItemContaEmpresa(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>ContaEmpresa</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>Empresa</code>. Esta rotina não recebe parâmetros para filtragem de
     * dados, isto é importante para a inicialização dos dados da tela para o
     * acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemContaEmpresa() {
        try {
            montarListaSelectItemContaEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Realiza a repovoação da propriedade userSFTP para as propriedades padrões.
     */
    public void setarUsuarioSFTP(){
        try{
            PovoadorPropriedades.repovoarSubstituta(Propriedade.CONVENIO_USUARIO_SFTP, getConvenioCobrancaVO().getTipo().getPropriedadesPadrao(), getConvenioCobrancaVO().getUserSFTP(), getConvenioCobrancaVO());
        }catch (Exception e){

        }
    }

    /**
     * Realiza a repovoação da propriedade userSFTP para as propriedades padrões.
     */
    public void setarNumeroContrato(){
        try{
            if (this.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
                this.getConvenioCobrancaVO().setNumeroContrato(this.getConvenioCobrancaVO().getNumeroContrato().replaceAll(" ", ""));
            }
            PovoadorPropriedades.repovoarSubstituta(Propriedade.CONVENIO_NUMERO_CONTRADO, getConvenioCobrancaVO().getTipo().getPropriedadesPadrao(), getConvenioCobrancaVO().getNumeroContrato(), getConvenioCobrancaVO());
        }catch (Exception e){}
    }


    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaSelectItemEmpresa(objs);
    }

    public void montarListaSelectItemConvenios() throws Exception {
        listaSelectItemConvenios = new ArrayList<SelectItem>();
        listaSelectItemConvenios.add(new SelectItem(0, ""));
        List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Iterator<ConvenioCobrancaVO> i = convenios.iterator();
        convenios = Ordenacao.ordenarLista(convenios, "descricao");
        while (i.hasNext()) {
            ConvenioCobrancaVO obj = i.next();
            listaSelectItemConvenios.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Empresa</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>Empresa</code>. Esta rotina não recebe parâmetros para filtragem de
     * dados, isto é importante para a inicialização dos dados da tela para o
     * acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm, true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemEmpresa();
        montarListaSelectItemContaEmpresa();
        montarListaSelectItemTipoRemessa();
        montarListaSelectItemTipoIdentificadorClienteEmpresa();
        montarListaSelectItemSituacaoConvenioCobranca();
        montarListaCurrencyConvenio();
        carregarBancos();
    }

    /**
     * Monta a lista de {@link SelectItem} da {@link SituacaoConvenioCobranca}
     */
    private void montarListaSelectItemSituacaoConvenioCobranca() {
        this.listaSelectItemSituacaoConvenioCobranca = new ArrayList<SelectItem>();
        for(SituacaoConvenioCobranca situacao : SituacaoConvenioCobranca.values()){
            this.listaSelectItemSituacaoConvenioCobranca.add(new SelectItem(situacao, situacao.getDescricao()));
        }
    }

    private void montarListaCurrencyConvenio() {
        this.listaSelectItemCurrencyConvenio = new ArrayList<SelectItem>();
        for(CurrencyConvenioEnum situacao : CurrencyConvenioEnum.values()){
            this.listaSelectItemCurrencyConvenio.add(new SelectItem(situacao, situacao.getDescricao()));
        }
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomeEmpresa", "Empresa"));
        itens.add(new SelectItem("multa", "Multa"));
        itens.add(new SelectItem("juros", "Juros"));
        itens.add(new SelectItem("extensaoArquivoRemessa", "Extensão do Arquivo de Remessa"));
        itens.add(new SelectItem("extensaoArquivoRetorno", "Extensão do Arquivo de Retorno"));
        itens.add(new SelectItem("diretorioGravaRemessa", "Diretório Grava Remessa"));
        itens.add(new SelectItem("diretorioLerRetorno", "Diretório Ler Retorno"));
        itens.add(new SelectItem("mensagem", "Mensagem"));
        itens.add(new SelectItem("sequencialDoArquivo", "Sequencial Do Arquivo"));
        itens.add(new SelectItem("razaoSocialEmpresa", "Conta Empresa"));
        itens.add(new SelectItem("numeroContrato", "Número do Contrato"));
        itens.add(new SelectItem("carteira", "Carteira"));
        itens.add(new SelectItem("variacao", "Variação"));
        itens.add(new SelectItem("descricaoTipoRemessa", "Tipo de Remessa"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        try {
            convenioCobrancaVOClone = (ConvenioCobrancaVO) convenioCobrancaVO.getClone(false);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    private void carregarBancos() {
        try {
            bancos.clear();
            bancosVOs = getFacade().getBanco().consultarTodos(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Ordenacao.ordenarLista(bancosVOs, "codigoBanco");
            bancos.add(new SelectItem(0, ""));
            for (BancoVO o : bancosVOs) {
                bancos.add(new SelectItem(o.getCodigo(), o.getCodigoBanco() + " - " + o.getNome()));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<SelectItem> getListaSelectItemTipoBoletoPJBank() {
        List<SelectItem> lista = new ArrayList<>();
        for(TipoBoletoPJBankEnum tipo : TipoBoletoPJBankEnum.values()) {
            lista.add(new SelectItem(tipo, tipo.getDescricao()));
        }
        return lista;
    }
    public List getListaSelectItemTipoRemessa() {
        return (listaSelectItemTipoRemessa);
    }

    public void setListaSelectItemTipoRemessa(List listaSelectItemTipoRemessa) {
        this.listaSelectItemTipoRemessa = listaSelectItemTipoRemessa;
    }

    public List<SelectItem> getListaSelectItemContaEmpresa() {
        return listaSelectItemContaEmpresa;
    }

    public void setListaSelectItemContaEmpresa(List<SelectItem> listaSelectItemContaEmpresa) {
        this.listaSelectItemContaEmpresa = listaSelectItemContaEmpresa;
    }

    public List getListaSelectItemEmpresa() {
        return (listaSelectItemEmpresa);
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO.getPixExpiracao() ==  null) {
            convenioCobrancaVO.setPixExpiracao(259200);
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public List<SelectItem> getListaSelectItemTipoConvenio() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipo.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY_MS)) {
                //não apresentar até implementação completa
                //Luiz Felipe 29-09-2023
                continue;
            }
            if (tipo.equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) || tipo.equals(TipoConvenioCobrancaEnum.PIX_ASAAS)) {
                //não apresentar até Pacto finalizar parceria com Asaas (solicitado remoção pelo Leonardo)
                //Estulano 07-11-2023
                continue;
            }
            if (this.getConvenioCobrancaVO() != null &&
                    UteisValidacao.emptyNumber(this.getConvenioCobrancaVO().getCodigo()) &&
                    tipo.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                //removido bin para novos cadastros
                continue;
            }
            if (!tipo.equals(TipoConvenioCobrancaEnum.NENHUM)) {
                lista.add(new SelectItem(tipo, tipo.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(TipoConvenioCobrancaEnum.NENHUM, TipoConvenioCobrancaEnum.NENHUM.getDescricao()));
        return lista;
    }

    public List<SelectItem> getListaSelectItemConvenios() {
        return listaSelectItemConvenios;
    }

    public void setListaSelectItemConvenios(List<SelectItem> listaSelectItemConvenios) {
        this.listaSelectItemConvenios = listaSelectItemConvenios;
    }

    public List<BancoVO> getBancoVOs() {
        if (bancosVOs == null) {
            bancosVOs = new ArrayList<>();
        }
        return bancosVOs;
    }

    public void setBancoVOs(List<BancoVO> bancosVOs) {
        this.bancosVOs = bancosVOs;
    }

    public List<SelectItem> getBancos() {
        return bancos;
    }

    public void setBancos(List<SelectItem> bancos) {
        this.bancos = bancos;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getConvenioCobranca().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro,null );

    }
    public void uploadNossaChave(UploadEvent upload){
        upload(upload, true);
    }

    public void uploadChaveGETNET(UploadEvent upload){
        upload(upload, false);
    }

    public void uploadChaveBIN(UploadEvent upload) {
        UploadItem item = upload.getUploadItem();
        File arqChave = item.getFile();
        try {
            getConvenioCobrancaVO().setChaveBIN(new Scanner(arqChave).useDelimiter("\\Z").next());
            getConvenioCobrancaVO().setNomeChaveBIN(item.getFileName());
            setMensagemID("chave_bin_carregada");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void upload(UploadEvent upload, boolean nossaChave) {
        UploadItem item = upload.getUploadItem();
        File arqChave = item.getFile();
        try {
            String arquivo = new Scanner(arqChave).useDelimiter("\\Z").next();
            if(nossaChave){
                getConvenioCobrancaVO().setNomeNossaChave(item.getFileName());
                if (getConvenioCobrancaVO().isItauRegistroOnline() || getConvenioCobrancaVO().isPixItau()) {
                    getConvenioCobrancaVO().setNossaChave(Uteis.encriptar(arquivo, PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline)));
                    setMensagemID("nossa_chave_itau_carregada");
                } else {
                    getConvenioCobrancaVO().setNossaChave(arquivo);
                    setMensagemID("nossa_chave_carregada");
                }
            }else{
                getConvenioCobrancaVO().setNomeChaveGETNET(item.getFileName());
                if (getConvenioCobrancaVO().isItauRegistroOnline() || getConvenioCobrancaVO().isPixItau()) {
                    getConvenioCobrancaVO().setChaveGETNET(Uteis.encriptar(arquivo, PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline)));
                    setMensagemID("chave_itau_carregada");
                } else {
                    getConvenioCobrancaVO().setChaveGETNET(arquivo);
                    setMensagemID("chave_getnet_carregada");
                }
            }
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void uploadArquivo1(UploadEvent upload) throws Exception {
        boolean erroAoLerArquivo = true;
        setNomeArquivo1("");
        UploadItem item = upload.getUploadItem();
        String nomeArquivo = item.getFileName();

        File file = item.getFile();
        try {
            BufferedImage outImage = ImageIO.read(file);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(file.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }

            ConvenioCobrancaArquivoVO convenioCobrancaArquivoVO = new ConvenioCobrancaArquivoVO();
            convenioCobrancaArquivoVO.setNomeArquivoOriginal(nomeArquivo);
            convenioCobrancaArquivoVO.setArquivo(arrayOutputStream.toByteArray()); //arquivo em byte
            convenioCobrancaArquivoVO.setDataUpload(Calendario.hoje());
            convenioCobrancaArquivoVO.setUsuario(getUsuarioLogado().getCodigo());
            convenioCobrancaArquivoVO.setConvenioCobranca(getConvenioCobrancaVO().getCodigo());

            if (getConvenioCobrancaVO().isPixInter()) {
                convenioCobrancaArquivoVO.setArquivoPlenoString(FileUtils.readFileToString(file, StandardCharsets.UTF_8)); //arquivo em texto Pleno String
            }

            setConvenioCobrancaArquivo1(convenioCobrancaArquivoVO);

            arrayOutputStream.close();
            fi.close();
            setMensagemID("Upload do certificado realizado com sucesso!");
            setSucesso(true);
            setErro(false);
            setFezUploadArquivo1(true);
        } catch (Exception e) {
            tratarErroUploadCertificado(e, erroAoLerArquivo, "uploadCertificadoDigital");
        }
    }

    public void uploadArquivo2(UploadEvent upload) throws Exception {
        boolean erroAoLerArquivo = true;
        UploadItem item = upload.getUploadItem();
        String nomeArquivo = item.getFileName();

        File file = item.getFile();
        try {
            BufferedImage outImage = ImageIO.read(file);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(file.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }

            ConvenioCobrancaArquivoVO convenioCobrancaArquivoVO = new ConvenioCobrancaArquivoVO();
            convenioCobrancaArquivoVO.setNomeArquivoOriginal(nomeArquivo);
            convenioCobrancaArquivoVO.setArquivo(arrayOutputStream.toByteArray());
            convenioCobrancaArquivoVO.setDataUpload(Calendario.hoje());
            convenioCobrancaArquivoVO.setUsuario(getUsuarioLogado().getCodigo());
            convenioCobrancaArquivoVO.setConvenioCobranca(getConvenioCobrancaVO().getCodigo());

            if (getConvenioCobrancaVO().isPixInter()) {
                convenioCobrancaArquivoVO.setArquivoPlenoString(FileUtils.readFileToString(file, StandardCharsets.UTF_8)); //arquivo em texto Pleno String
            }

            setConvenioCobrancaArquivo2(convenioCobrancaArquivoVO);

            arrayOutputStream.close();
            fi.close();
            setMensagemID("Upload do certificado realizado com sucesso!");
            setSucesso(true);
            setErro(false);
            setFezUploadArquivo2(true);
        } catch (Exception e) {
            tratarErroUploadCertificado(e, erroAoLerArquivo, "uploadCertificadoDigital");
        }
    }

    private void tratarErroUploadCertificado(Exception e, boolean erroAoLerArquivo, String nomeMetodo) throws Exception {
        setSucesso(false);
        setErro(true);
        if (erroAoLerArquivo) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CARREGAR CERTIFICADO DIGITAL. método:" + nomeMetodo + ". Erro: " + e.getMessage());
        } else {
            montarErro(e);
            throw e;
        }
    }

    public void excluirArquivo1() {
        try {
            getFacade().getConvenioCobrancaArquivo().excluirPorChavePrimaria(getConvenioCobrancaArquivo1().getCodigo());
            setConvenioCobrancaArquivo1(new ConvenioCobrancaArquivoVO());
            setFezUploadArquivo1(false);
            setSenhaArquivo("");
            setNomeArquivo1("");
            if (getConvenioCobrancaVO().isPixBradesco()) {
                setExibirCampoSenhaArquivo(true);
            } else {
                setExibirCampoSenhaArquivo(false);
            }
            setPossuiArquivo1(false);
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Certificado excluído com sucesso!");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void excluirArquivo2() {
        try {
            getFacade().getConvenioCobrancaArquivo().excluirPorChavePrimaria(getConvenioCobrancaArquivo2().getCodigo());
            setConvenioCobrancaArquivo2(new ConvenioCobrancaArquivoVO());
            setFezUploadArquivo2(false);
            setSenhaArquivo("");
            setNomeArquivo2("");
            setExibirCampoSenhaArquivo(false);
            setPossuiArquivo2(false);
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Certificado excluído com sucesso!");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }


    public void montarListaSelectItemTipoIdentificadorClienteEmpresa() {
        List<SelectItem> listaItens = new ArrayList<SelectItem>();
        for (IdentificadorClienteEmpresaEnum identificador : IdentificadorClienteEmpresaEnum.values()) {
            listaItens.add(new SelectItem(identificador.getCodigo(), identificador.getDescricao()));
        }
        setListaSelectItemIdentificadorClienteEmpresa(listaItens);
    }

    public List<SelectItem> getListaSelectItemIdentificadorClienteEmpresa() {
        return listaSelectItemIdentificadorClienteEmpresa;
    }

    public void setListaSelectItemIdentificadorClienteEmpresa(List<SelectItem> listaSelectItemIdentificadorClienteEmpresa) {
        this.listaSelectItemIdentificadorClienteEmpresa = listaSelectItemIdentificadorClienteEmpresa;
    }

    public void preencherBanco() throws Exception {
        if (getConvenioCobrancaVO().getBanco() != null && getConvenioCobrancaVO().getBanco().getCodigo() > 0) {
            BancoVO bancoVO = getFacade().getBanco().consultarPorChavePrimaria(getConvenioCobrancaVO().getBanco().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getConvenioCobrancaVO().setBanco(bancoVO);
            //banco Sicredi
            if (getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(748)) {
                getConvenioCobrancaVO().setExtensaoArquivoRemessa(".CRM");
            }
        } else {
            getConvenioCobrancaVO().setBanco(new BancoVO());
        }
        preencherContaCorrente();
    }

    private void preencherContaCorrente() {
        getConvenioCobrancaVO().setContaEmpresa(new ContaCorrenteVO());
        montarListaSelectItemContaEmpresa();
    }

    public void prepararTela() {
        try {
            limparMsg();
            setOnComplete("");

            if (getConvenioCobrancaVO().getCodigo().equals(0)) {
                ConvenioCobrancaVO aux = getConvenioCobrancaVO();
                setConvenioCobrancaVO(new ConvenioCobrancaVO());
                getConvenioCobrancaVO().setTipo(aux.getTipo());
                getConvenioCobrancaVO().setAmbiente(AmbienteEnum.PRODUCAO);
                inicializarUsuarioLogado();
                inicializarEmpresaLogada();

                if (getConvenioCobrancaVO().isStoneConnect()) {
                    if (getEmpresaLogado() == null || UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                        throw new Exception("Essa operação não pode ser executada por admin");
                    }
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!empresaVO.isFacilitePayStoneConnect()) {
                        this.getConvenioCobrancaVO().setTipo(TipoConvenioCobrancaEnum.NENHUM);
                        this.setOnComplete("Richfaces.showModalPanel('modalPropagandaFaciliteStoneConnect');");
                        return;
                    }
                }
            }
            if (getConvenioCobrancaVO().isBoleto()) {
                getConvenioCobrancaVO().setMulta(0.01);
                getConvenioCobrancaVO().setJuros(0.01);
            }
            if (getConvenioCobrancaVO().getTipo().getCodigoBanco() != 0) {
                selecionarBanco(getConvenioCobrancaVO().getTipo().getCodigoBanco());
            }
            if (getConvenioCobrancaVO().getTipo().getPropriedadesPadrao().length > 0) {
                try {
                    PovoadorPropriedades.povoar(getConvenioCobrancaVO().getTipo().getPropriedadesPadrao(), getConvenioCobrancaVO());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            setOnComplete("window.location.reload();");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void adicionarTagDesconto() throws Exception {
        if(getConvenioCobrancaVO().getInstrucoesBoleto() != null && getConvenioCobrancaVO().getDescontoBoleto() == 0.0 && !getConvenioCobrancaVO().getInstrucoesBoleto().contains("<desconto>")){
            getConvenioCobrancaVO().setInstrucoesBoleto(getConvenioCobrancaVO().getInstrucoesBoleto() + "\n<desconto>SE PAGO ATÉ O DIA TAG_DIA_DESCONTO, O VALOR SERÁ DE " + getEmpresaLogado().getMoeda() + " TAG_DESCONTO.</desconto>");
        }else if(getConvenioCobrancaVO().getInstrucoesBoleto() != null && getConvenioCobrancaVO().getDescontoBoleto() >= 0){
            getConvenioCobrancaVO().setInstrucoesBoleto(getConvenioCobrancaVO().getInstrucoesBoleto() + "\n<descontoBoleto>ATÉ O DIA TAG_DIA_DESCONTO_BOLETO, O VALOR SERÁ DE " + getEmpresaLogado().getMoeda() + " TAG_DESCONTO_BOLETO. \n Após vencimento sujeito a Protesto. </descontoBoleto>");
        }
    }

    /**
     * Realiza a seleção de um determinado banco.
     */
    private void selecionarBanco(int codigoBanco) {
        BancoVO bancoItau = new BancoVO();
        for(BancoVO banco : getBancoVOs()){
            if(banco.getCodigoBanco().equals(codigoBanco)){
                bancoItau = banco;
                break;
            }
        }
        getConvenioCobrancaVO().setBanco(bancoItau);
        preencherContaCorrente();
    }

    public boolean isApresentarCampoJuros() {
        return !getConvenioCobrancaVO().isBoleto();
    }

    public boolean isApresentarCampoMulta() {
        return !getConvenioCobrancaVO().isBoleto();
    }

    public boolean isApresentarCampoDiretorioGravaRemessa() {
        return !getConvenioCobrancaVO().isBoleto();
    }

    public boolean isApresentarCampoDiretorioLerRetorno() {
        return !getConvenioCobrancaVO().isBoleto();
    }

    public boolean isApresentarBloquearCobrancas() {
        return  !getConvenioCobrancaVO().isNovoObj() &&
                !getConvenioCobrancaVO().getTipo().isPix() &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                !getConvenioCobrancaVO().isSomenteExtrato() &&
                !getConvenioCobrancaVO().isBoleto() &&
                !getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS);
    }

    public boolean isApresentarCampoCarteira() {
//        ${!ConvenioCobrancaControle.convenioCobrancaVO.tipo.transacaoOnline && !ConvenioCobrancaControle.convenioCobrancaVO.tipo.pix &&
//        !ConvenioCobrancaControle.convenioCobrancaVO.tipoNenhum && ConvenioCobrancaControle.convenioCobrancaVO.tipo.codigoBanco != 301}
        return !getConvenioCobrancaVO().getTipo().isTransacaoOnline() &&
                !getConvenioCobrancaVO().getTipo().isPix() &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.NENHUM) &&
                !getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS);
    }

    public boolean isApresentarCampoCarteiraBoleto() {
        return getConvenioCobrancaVO().getTipo().getTipoAutorizacao() != null &&
                getConvenioCobrancaVO().getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS);
    }

    public boolean isApresentarCampoVariacao() {
        return !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                (!getConvenioCobrancaVO().isBoleto() ||
                getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(JBoleto.SANTANDER) ||
                getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(JBoleto.BANCO_DO_BRASIL) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE));
    }

    public boolean isApresentarCampoSitesNoBoleto() {
        return getConvenioCobrancaVO().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_CLUBE);
    }

    public void incluirLogAjusteConvenio(String campoAnterior, String campoAlterado) throws Exception {
        LogInterfaceFacade logFacade = getFacade().getLog();

        LogVO log = new LogVO();
        log.setNomeCampo("De convênio");
        log.setChavePrimaria(convenioCobrancaVO.getCodigo().toString());
        log.setNomeEntidade("CONVENIOCOBRANCA");
        log.setPessoa(convenioCobrancaVO.getCodigo());
        log.setValorCampoAnterior(campoAnterior);
        log.setValorCampoAlterado(campoAlterado);
        log.setOperacao("ALTERAÇÃO");
        log.setResponsavelAlteracao(this.getUsuarioLogado().getNome());
        log.setUserOAMD(this.getUsuarioLogado().getUserOamd());
        logFacade.incluirSemCommit(log);
    }

    public void incluirLogInclusao() throws Exception {
        try {
            convenioCobrancaVO.setObjetoVOAntesAlteracao(new ConvenioCobrancaVO());
            convenioCobrancaVO.setNovoObj(true);
            registrarLogObjetoVO(convenioCobrancaVO, convenioCobrancaVO.getCodigo(), "CONVENIOCOBRANCA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONVENIOCOBRANCA", convenioCobrancaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONVENIOCOBRANCA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        convenioCobrancaVO.setNovoObj(new Boolean(false));
        convenioCobrancaVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            convenioCobrancaVO.setObjetoVOAntesAlteracao(new ConvenioCobrancaVO());
            convenioCobrancaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(convenioCobrancaVO, convenioCobrancaVO.getCodigo(), "CONVENIOCOBRANCA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONVENIOCOBRANCA", convenioCobrancaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CONVENIOCOBRANCA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(convenioCobrancaVO, convenioCobrancaVO.getCodigo(), "CONVENIOCOBRANCA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONVENIOCOBRANCA", convenioCobrancaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CONVENIOCOBRANCA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        convenioCobrancaVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = convenioCobrancaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), convenioCobrancaVO.getCodigo(), 0);
    }

    public void realizarConsultaLogWebhookPix() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = getPixWebhookVO().getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), convenioCobrancaVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       convenioCobrancaVO = new ConvenioCobrancaVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public List<SelectItem> getListaSelectItemSituacaoConvenioCobranca() {
        return listaSelectItemSituacaoConvenioCobranca;
    }

    public void setListaSelectItemSituacaoConvenioCobranca(List<SelectItem> listaSelectItemSituacaoConvenioCobranca) {
        this.listaSelectItemSituacaoConvenioCobranca = listaSelectItemSituacaoConvenioCobranca;
    }

    public Boolean getSomenteExibirTipoConvenio() {
        return somenteExibirTipoConvenio;
    }

    public void setSomenteExibirTipoConvenio(Boolean somenteExibirTipoConvenio) {
        this.somenteExibirTipoConvenio = somenteExibirTipoConvenio;
    }

    private void inicializarEmpresaLogada() {
        try {
            if (!UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                boolean existe = false;
                for (ConvenioCobrancaEmpresaVO conv : getConvenioCobrancaVO().getConfiguracoesEmpresa()) {
                    if (conv.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                        existe = true;
                    }
                }
                if (!existe && getConvenioCobrancaVO().isNovoObj()) {
                    getConvenioCobrancaVO().getConfiguracoesEmpresa().add(new ConvenioCobrancaEmpresaVO(getConvenioCobrancaVO(), getEmpresaLogado()));
                }

            }
        }catch(Exception ignored){
        }
    }

    public boolean isPodeAdicionarConfiguracaoEmpresa() {
        return podeAdicionarConfiguracaoEmpresa;
    }

    public void setPodeAdicionarConfiguracaoEmpresa(boolean podeAdicionarConfiguracaoEmpresa) {
        this.podeAdicionarConfiguracaoEmpresa = podeAdicionarConfiguracaoEmpresa;
    }

    public boolean isApresentarCNPJ() {
        return (getUtilizaConfiguracaoSesc() != null && getUtilizaConfiguracaoSesc() && !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE);
    }

    public Boolean getUtilizaConfiguracaoSesc() {
        return utilizaConfiguracaoSesc;
    }

    public void setUtilizaConfiguracaoSesc(Boolean utilizaConfiguracaoSesc) {
        this.utilizaConfiguracaoSesc = utilizaConfiguracaoSesc;
    }

    public void validarPermissaoConfiguracaoEmpresa() {
        try {

            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    podeAdicionarConfiguracaoEmpresa = true;
                    return;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }

            if (getFacade().getEmpresa().countEmpresas(true) <= 1) {
                podeAdicionarConfiguracaoEmpresa = false;
                return;
            }

            Iterator<UsuarioPerfilAcessoVO> i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "ConvenioCobrancaEmpresa",
                            "5.14 - Configuração de empresa para convenio de cobrança");
                    podeAdicionarConfiguracaoEmpresa = true;
                    return;
                }
            }
        } catch (Exception e) {
            podeAdicionarConfiguracaoEmpresa = false;
        }
    }    public void removerCfg() {
        ConvenioCobrancaEmpresaVO obj = (ConvenioCobrancaEmpresaVO) context().getExternalContext().getRequestMap().get("cfg");
        convenioCobrancaVO.getConfiguracoesEmpresa().remove(obj);
    }

    public void adicionarCfg() {
        try {
            if(convenioCobrancaEmpresa == null ||
                    convenioCobrancaEmpresa.getEmpresa() == null ||
                    convenioCobrancaEmpresa.getEmpresa().getCodigo() == 0 ||
                    convenioCobrancaEmpresa.getEmpresa().getCodigo() == null
            ){
                throw new Exception("Você precisa selecionar uma empresa.");
            }

            for (Object obj : getListaSelectItemEmpresa()) {
                SelectItem si = (SelectItem) obj;
                if (convenioCobrancaEmpresa.getEmpresa().getCodigo().equals(si.getValue())) {
                    convenioCobrancaEmpresa.getEmpresa().setNome(si.getLabel());
                }
            }
            for (ConvenioCobrancaEmpresaVO cfg : getConvenioCobrancaVO().getConfiguracoesEmpresa()) {
                if (!UteisValidacao.emptyNumber(convenioCobrancaEmpresa.getEmpresa().getCodigo()) && cfg.getEmpresa().getCodigo().equals(convenioCobrancaEmpresa.getEmpresa().getCodigo())) {
                    throw new Exception("Já existe configuração para esta empresa.");
                }
            }
            convenioCobrancaVO.getConfiguracoesEmpresa().add(convenioCobrancaEmpresa.clone());
            convenioCobrancaEmpresa = new ConvenioCobrancaEmpresaVO();
            setMensagemID("msg_adicionados_dados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void adicionarTodasEmpresas() {
        try {
            List<ConvenioCobrancaEmpresaVO> novas = new ArrayList<ConvenioCobrancaEmpresaVO>();
            for (Object obj : getListaSelectItemEmpresa()) {
                SelectItem si = (SelectItem) obj;

                ConvenioCobrancaEmpresaVO add = new ConvenioCobrancaEmpresaVO(getConvenioCobrancaVO(), new EmpresaVO((Integer) si.getValue(), si.getLabel()));
                if (UteisValidacao.emptyNumber(add.getEmpresa().getCodigo())) {
                    continue;
                }
                novas.add(add.clone());
            }

            convenioCobrancaVO.setConfiguracoesEmpresa(novas);
            convenioCobrancaEmpresa = new ConvenioCobrancaEmpresaVO();
            setMensagemID("msg_adicionados_dados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public ConvenioCobrancaEmpresaVO getConvenioCobrancaEmpresa() {
        if (convenioCobrancaEmpresa == null) {
            convenioCobrancaEmpresa = new ConvenioCobrancaEmpresaVO();
        }
        return convenioCobrancaEmpresa;
    }

    public void setConvenioCobrancaEmpresa(ConvenioCobrancaEmpresaVO convenioCobrancaEmpresa) {
        this.convenioCobrancaEmpresa = convenioCobrancaEmpresa;
    }

    public boolean isExibirDiasAntecipacaoCobrancaDCO() {
        return (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO));
    }

    public boolean isApresentarCampoNrDiasProtesto() {
        return getConvenioCobrancaVO().isBoleto() && (getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(JBoleto.CAIXA_ECONOMICA) || getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(JBoleto.SANTANDER));
    }

    public boolean isApresentarCampoNrDiasBaixaAutomatica() {
        return getConvenioCobrancaVO().isBoleto() && getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(JBoleto.CAIXA_ECONOMICA);
    }

    public boolean isBoleto() {
        return getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO);
    }

    public boolean isConvenioCartaoCredito() {
        return getConvenioCobrancaVO().getTipo().getTipoAutorizacao() != null &&
                getConvenioCobrancaVO().getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
    }

    public boolean isApresentarDiretorioExtrato() {
        return getConvenioCobrancaVO() != null &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ASAAS) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) &&
                !getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                !getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD) &&
                !getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                !getConvenioCobrancaVO().isUtilizaExtrato();
    }

    public void notificarRecursoSistemaCobranca() {
        try {
            if(getConvenioCobrancaVO().isProcessarRemessasAutomatico()) {
                if (!convenioCobrancaVO.equals(convenioCobrancaVOClone)) {
                    for (ConvenioCobrancaEmpresaVO obj : getConvenioCobrancaVO().getConfiguracoesEmpresa()) {
                        getFacade().getZWFacade().notificarRecursoSistema(getKey(),
                                RecursoSistema.CONVENIO_COBRANCA_DCO_AUTOMATICO_MARCOU, getUsuario().getCodigo(),
                                obj.getEmpresa().getCodigo());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isExibirQuantidadeLimiteItensRemessa() {
        try {
            return getConvenioCobrancaVO().getTipo() != null &&
                    getConvenioCobrancaVO().getTipo().getTipoCobranca() != null &&
                    getUsuarioLogado().getUsuarioPactoSolucoes() &&
                    (getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) ||
                            getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC));
        } catch (Exception ex) {
            return false;
        }
    }
    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public Boolean getAcessoAjusteConvenio() {
        return acessoAjusteConvenio;
    }

    public void setAcessoAjusteConvenio(Boolean acessoAjusteConvenio) {
        this.acessoAjusteConvenio = acessoAjusteConvenio;
    }

    public String getCodigoConvenioAlterado() {
        if (codigoConvenioAlterado == null) {
            codigoConvenioAlterado = "";
        }
        return codigoConvenioAlterado;
    }

    public void setCodigoConvenioAlterado(String codigoConvenioAlterado) {
        this.codigoConvenioAlterado = codigoConvenioAlterado;
    }

    public String getDescricaoConvenioNovo() {
        return descricaoConvenioNovo;
    }

    public void setDescricaoConvenioNovo(String descricaoConvenioNovo) {
        this.descricaoConvenioNovo = descricaoConvenioNovo;
    }

    public List<ConvenioCobrancaVO> getListaConveniosCompleta() {
        if (listaConveniosCompleta == null) {
            listaConveniosCompleta = new ArrayList<>();
        }
        return listaConveniosCompleta;
    }

    public void setListaConveniosCompleta(List<ConvenioCobrancaVO> listaConveniosCompleta) {
        this.listaConveniosCompleta = listaConveniosCompleta;
    }

    public List<SelectItem> getListaConvenioAlterado() {
        return listaConvenioAlterado;
    }

    public void setListaConvenioAlterado(List<SelectItem> listaConvenioAlterado) {
        this.listaConvenioAlterado = listaConvenioAlterado;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Convênio de Cobrança",
                "Deseja excluir o Convênio de Cobrança?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public List<SelectItem> getListaSelectItemNomenclaturaArquivo() {
        return NomenclaturaArquivoEnum.getListaSelectItem();
    }

    /**
     * Valida se usuario tem permissao no perfil de acesso
     * @return Se usuario tem perfil de acesso
     */
    private Boolean validaPerfilAcesso() {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            UsuarioVO usuarioLogado = loginControle.getUsuario();

            if (usuarioLogado.getUsuarioPerfilAcessoVOs().isEmpty()) {
                return false;
            } else {
                Iterator i = usuarioLogado.getUsuarioPerfilAcessoVOs().iterator();
                while (i.hasNext()) {
                    UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                    if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                        usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                                usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                                usuarioLogado, "AlterarConvenioCobranca", "Alterar convenio de cobrança");
                    }
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void carregarConvenios(ConvenioCobrancaVO convenioCobranca) throws Exception {
        this.codigoConvenioAlterado = null;
        this.descricaoConvenioNovo = null;
        if (convenioCobranca.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PINPAD)) {
            this.acessoAjusteConvenio = false;
        } else {
            this.acessoAjusteConvenio = validaPerfilAcesso();
        }

        if (this.acessoAjusteConvenio) {
            this.listaConvenioAlterado = new ArrayList<>();
            this.descricaoConvenioNovo = convenioCobranca.getCodigo() + " - " + convenioCobranca.getDescricao();

            this.listaConvenioAlterado.add(new SelectItem("", ""));
            this.listaConvenioAlterado.add(new SelectItem("TODOS", "TODOS"));
            this.listaConveniosCompleta = getFacade().getConvenioCobranca().consultarTodosGeral(true, null, Uteis.NIVELMONTARDADOS_TODOS);

            Ordenacao.ordenarLista(this.listaConveniosCompleta, "descricao");
            for (ConvenioCobrancaVO convenioVO : this.listaConveniosCompleta) {
                if (convenioVO.getTipo().getTipoAutorizacao() != null &&
                        !convenioVO.getTipo().getTipoAutorizacao().equals(this.getConvenioCobrancaVO().getTipo().getTipoAutorizacao())) {
                    continue;
                }
                if (convenioVO.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo()) && !convenioVO.isTipoPix()) {
                    convenioVO.setCodigoGenerico(convenioVO.getCodigo().toString());

                    getListaConvenioAlterado().add(new SelectItem(convenioVO.getCodigoGenerico(), convenioVO.getDescricao()));
                }
            }
        }
    }

    public List<SelectItem> getListaSelectItemVanExtrato() {
        return VanExtratoEnum.getListaSelectItem();
    }

    public void verificarExemplo() {
        try {
            limparMsg();
            setMsgAlert("");
            setExemplo("");

            ConvenioCobrancaVO convenioVO = (ConvenioCobrancaVO) getConvenioCobrancaVO().getClone(true);
            convenioVO.setEmpresa(getEmpresaLogado());

            StringBuilder exem = new StringBuilder();
            exem.append("Nomenclatura arquivo: <br/>").append(convenioVO.getNomenclaturaExtrato_Preenchido(getKey(), Calendario.hoje(), TipoArquivoRedeEnum.EEVD.getId())).append("<br/><br/>");
            exem.append("Diretório remoto: <br/>").append(convenioVO.getDiretorioRemotoExtrato_Preparado(Calendario.hoje())).append("<br/><br/>");
            exem.append("Diretório local: <br/>").append(convenioVO.getDiretorioLocalExtrato_Preparado(Calendario.hoje())).append("<br/><br/>");
            setExemplo(exem.toString());
            setMsgAlert("Richfaces.showModalPanel('modalExemploConvenio')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void verificarLogExtrato() {
        try {
            limparMsg();
            setMsgAlert("");
            setLogsExtrato(new ArrayList<>());

            if(!getConvenioCobrancaVO().getDiretorioLocalLogExtrato().contains("IDENTIFICADOR-EMPRESA")) {
                List<String> logs = getFacade().getConvenioCobranca().lerLogProcessamentoExtratos(getConvenioCobrancaVO(), getQtdMaximaLinhasApresentarLogExtrato());
                for(String log : logs){
                    if (log.startsWith("[DEBUG]") && !log.contains("Current Props") && !log.contains("SETTING PROPS FOR")) {
                        log.replace("host=sftp.pactosolucoes.com.br", "host=*****")
                                .replace("porta=2222", "porta=*****")
                                .replace("user=root", "user=*****")
                                .replace("pwd=I21b12r68qA4Abv", "pwd=*****");
                        getLogsExtrato().add(log);
                    }
                }
                setMsgAlert("Richfaces.showModalPanel('modalLogsExtratoConvenio')");
            }else{
                montarErro("Necessario informar identificador da empresa no campo diretorio local de logs de extrato.");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String getExemplo() {
        if (exemplo == null) {
            exemplo = "";
        }
        return exemplo;
    }
    public void setExemplo(String exemplo) {
        this.exemplo = exemplo;
    }
    public String limparMsgString() {
            limparMsg();
            return "";
    }

    public String getExtratoEletronicoCielo() {
        retornoExtratoEletronicoCielo();

        if (extratoEletronicoCielo == null) {
            extratoEletronicoCielo = "";
        }

        return extratoEletronicoCielo;
    }

    public void setExtratoEletronicoCielo(String extratoEletronicoCielo) {
        this.extratoEletronicoCielo = extratoEletronicoCielo;
    }

    public void modalAtualizarCompRecebimentosBoleto() {
        try {
            limparMsg();
            setMsgAlert("");
            setSucesso(true);
            setMensagem("Processo Finalizado! " + getFacade().getMovPagamento().acaoAtualizarDiasCompensacaoBoleto(getConvenioCobrancaVO().getDiasParaCompensacao(),getConvenioCobrancaVO().getCodigo(),false)
                    + " boletos foram atualizados com sucesso.") ;
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
            control.init("Atualizar datas de compensação dos boletos",
                    "<center><b>Atenção!</b></center><br>" +
                            "<p align='justify'> O sistema irá identificar a data de lançamento de cada recebimento de boleto e irá acrescentar os dias informados na configuração anterior para a data de compensação" +
                            " de todos os recebimentos de boleto, independente da data de compensação já existente." +
                            " Esta operação não poderá ser desfeita.<br><br>" +
                            "Deseja atualizar a data de compensação de todos os boletos passados já lançados no sistema neste convênio de cobrança específico?</p>",
                    this, "atualizarDiasCompensacaoBoleto", getMensagemNotificar(), "", limparMsgString(), "form");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void atualizarDiasCompensacaoBoleto(){
        try {
            limparMsg();
            gravar(false);
            getFacade().getMovPagamento().acaoAtualizarDiasCompensacaoBoleto(getConvenioCobrancaVO().getDiasParaCompensacao(),getConvenioCobrancaVO().getCodigo(),true);
        } catch (Exception e) {
            Uteis.logar(e, ConvenioCobrancaControle.class);
            montarErro(e);
        }
    }

    public List<SelectItem> getListaSelectItemAmbiente() {
        return AmbienteEnum.obterListSelectItem();
    }

    public List<SelectItem> getListaSelectItemTipoCredencialStone() {
        if (getConvenioCobrancaVO().isUsaSplitPagamentoStoneV5()) {
            return TipoCredencialStoneEnum.obterListSelectItemParaUsoSplit();
        } else {
            return TipoCredencialStoneEnum.obterListSelectItem();
        }
    }

    public String getUrlWebhook() {
        try {
            return getConvenioCobrancaVO().getTipo().getWebhookURL(getKey());
        } catch (Exception ex) {
            return "";
        }
    }

    public String getUrlWebhookTitle() {
        try {
            if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
                return "Essa URL deve ser colocada lá nas configurações de webhook do portal da própria Mundipagg.<br/>" +
                        "É através deste link que eles poderão nos avisar qualquer alteração que for realizada em uma transação.<br/>" +
                        "Sendo assim, crie um novo webhook lá no portal deles, cole este link lá e marque o evento <b>PEDIDO</b>.<br/><br/>" +
                        "<b>Clique para copiar para a área de transferência</b>";
            } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                return "É através desta URL que a Pagar.me irá nos avisar qualquer alteração que for realizada em uma transação.<br/>" +
                        "Na Pagar.me não é necessário que você faça nenhuma configuração com essa URL, pois isso ocorre automaticamente.";
            } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                return "Essa URL deve ser colocada lá nas configurações de webhook do portal da própria Vindi.<br/>" +
                        "É através deste link que eles poderão nos avisar qualquer alteração que for realizada em uma transação.<br/>" +
                        "Sendo assim, crie um novo webhook lá no portal deles, cole este link lá e marque os eventos relacionados à <b>COBRANÇA</b> e <b>FATURA</b>.<br/><br/>" +
                        "<b>Clique para copiar para a área de transferência</b>";
            } else {
                return "URL do Webhook do ZillyonWeb<br/><br/><b>Clique para copiar para a área de transferência</b>";
            }
        } catch (Exception ex) {
            return "";
        }
    }

    public boolean isApresentarCriarOperadorasAutomatico() {
        return getConvenioCobrancaVO() != null &&
                getConvenioCobrancaVO().getTipo() != null &&
                getConvenioCobrancaVO().getTipo().getTipoCobranca() != null &&
                getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE);
    }

    /*A situação da Banderira ser um Cadastro, está gerando problemas, pois fica gerando vários cadastros para mesma Bandeira, atrapalhando integrações com outros sistemas.
    O ideal é ter apenas um para cada Bandeira. Então, geramos no Banco de Importação um cadastro para cada Operadora (Bandeira).
    Agora, bloqueamos as opções de Cadastrar ou Editar novas.
    Se tiver algum impacto, precisa resolver via Ajuste BD e arrumar também no banco de Importação para os clientes futuros.*/
//    public void abrirModalCriarOperadorasAutomatico() {
//        try {
//            limparMsg();
//            setOnComplete("");
//            setListaOperadoraCriarAutomatico(new ArrayList<>());
//
//            if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.NENHUM)) {
//                throw new Exception("Tipo do convênio não informado.");
//            }
//
//            setListaOperadoraCriarAutomatico(getFacade().getOperadoraCartao().consultarOperadorasTipoConvenio(getConvenioCobrancaVO().getTipo(), true));
//
//            setOnComplete("Richfaces.showModalPanel('modalCriarOperadora');");
//        } catch (Exception ex) {
//            montarErro(ex);
//        }
//    }

    public void gravarOperadorasCartaoCreditoOnline() throws Exception{
                confirmarCriarOperadorasAutomatico();
    }

    public void confirmarCriarOperadorasAutomatico() throws Exception {
        try {
            limparMsg();
            setOnComplete("");

            setListaOperadoraCriarAutomatico(getFacade().getOperadoraCartao().consultarOperadorasTipoConvenio(getConvenioCobrancaVO().getTipo(), true));

            if (!UteisValidacao.emptyList(getListaOperadoraCriarAutomatico())) {
                List<OperadoraCartaoVO> listaOperadorasGravar = new ArrayList<>();
                for (OperadoraCartaoVO operadora : getListaOperadoraCriarAutomatico()) {
                    //Se já existe uma Operadora configurada, usa a que existe
                    if (!UteisValidacao.emptyNumber(operadora.getCodigo())) {
                        listaOperadorasGravar.add(operadora);
                        continue;
                    }

                    //Se não tem uma Operadora configurada
                    OperadoraCartaoVO operadoraCartaoVO = null;
                    OperadorasExternasAprovaFacilEnum operadoraEnum = null;

                    //Vê qual o tipo, para consultar pela descrição padrão
                    switch (getConvenioCobrancaVO().getTipo()) {
                        case DCC_CAIXA_ONLINE:
                            operadoraEnum = operadora.getCodigoIntegracaoDCCCaixaOnline();
                            break;
                        case DCC_CIELO_ONLINE:
                            operadoraEnum = operadora.getCodigoIntegracaoCielo();
                            break;
                        case DCC_STONE_ONLINE:
                            operadoraEnum = operadora.getCodigoIntegracaoStoneOnline();
                            break;
                        case DCC_STONE_ONLINE_V5:
                            operadoraEnum = operadora.getCodigoIntegracaoStoneOnlineV5();
                            break;
                        case DCC_MUNDIPAGG:
                            operadoraEnum = operadora.getCodigoIntegracaoMundiPagg();
                            break;
                        case DCC_PAGAR_ME:
                            operadoraEnum = operadora.getCodigoIntegracaoPagarMe();
                            break;
                        case DCC_GETNET_ONLINE:
                            operadoraEnum = operadora.getCodigoIntegracaoGetNet();
                            break;
                        case DCC_STRIPE:
                            operadoraEnum = operadora.getCodigoIntegracaoStripe();
                            break;
                        case DCC_PAGOLIVRE:
                            operadoraEnum = operadora.getCodigoIntegracaoPagoLivre();
                            break;
                        case DCC_FACILITEPAY:
                            operadoraEnum = operadora.getCodigoIntegracaoFacilitePay();
                            break;
                        case DCC_FACILITEPAY_MS:
                            operadoraEnum = operadora.getCodigoIntegracaoFacilitePay();
                            break;
                        case DCC_CEOPAG:
                            operadoraEnum = operadora.getCodigoIntegracaoCeopag();
                            break;
                        case DCC_PINBANK:
                            operadoraEnum = operadora.getCodigoIntegracaoPinBank();
                            break;
                        case DCC_ONE_PAYMENT:
                            operadoraEnum = operadora.getCodigoIntegracaoOnePayment();
                            break;
                        case DCC_VINDI:
                            operadoraEnum = operadora.getCodigoIntegracaoVindi();
                            break;
                        case DCC_FITNESS_CARD:
                            operadoraEnum = operadora.getCodigoIntegracaoFitnessCard();
                            break;
                        case DCC_PAGBANK:
                            operadoraEnum = operadora.getCodigoIntegracaoPagBank();
                            break;
                        case DCC_E_REDE:
                            operadoraEnum = operadora.getCodigoIntegracaoERede();
                            break;
                        case DCC_MAXIPAGO:
                            operadoraEnum = operadora.getCodigoIntegracaoMaxiPago();
                            break;
                    }

                    if (operadoraEnum == null) {
                        throw new Exception("Operadora não encontrada. Entre em contato com a Pacto");
                    }

                    //Verifica se existe uma Operadora com a Descrição Padrão, sem configuração
                    operadoraCartaoVO = getFacade().getOperadoraCartao().consultarOperadoraPadraoPorDescricao(operadoraEnum.getDescricaoOperadoraCartaoPadrao(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (operadoraCartaoVO != null && !UteisValidacao.emptyNumber(operadoraCartaoVO.getCodigo())) {
                        //Se tem uma Operadora com a Descrição Padrão, que só falta configurar, configura aqui
                        operadoraCartaoVO.setAtivo(true);
                        switch (getConvenioCobrancaVO().getTipo()) {
                            case DCC_CAIXA_ONLINE:
                                operadoraCartaoVO.setCodigoIntegracaoDCCCaixaOnline(operadoraEnum);
                                break;
                            case DCC_CIELO_ONLINE:
                                operadoraCartaoVO.setCodigoIntegracaoCielo(operadoraEnum);
                                break;
                            case DCC_STONE_ONLINE:
                                operadoraCartaoVO.setCodigoIntegracaoStoneOnline(operadoraEnum);
                                break;
                            case DCC_MUNDIPAGG:
                                operadoraCartaoVO.setCodigoIntegracaoMundiPagg(operadoraEnum);
                                break;
                            case DCC_PAGAR_ME:
                                operadoraCartaoVO.setCodigoIntegracaoPagarMe(operadoraEnum);
                                break;
                            case DCC_GETNET_ONLINE:
                                operadoraCartaoVO.setCodigoIntegracaoGetNet(operadoraEnum);
                                break;
                            case DCC_STRIPE:
                                operadoraCartaoVO.setCodigoIntegracaoStripe(operadoraEnum);
                                break;
                            case DCC_PAGOLIVRE:
                                operadoraCartaoVO.setCodigoIntegracaoPagoLivre(operadoraEnum);
                                break;
                            case DCC_FACILITEPAY:
                                operadoraCartaoVO.setCodigoIntegracaoFacilitePay(operadoraEnum);
                                break;
                            case DCC_FACILITEPAY_MS:
                                operadoraCartaoVO.setCodigoIntegracaoFacilitePay(operadoraEnum);
                                break;
                            case DCC_CEOPAG:
                                operadoraCartaoVO.setCodigoIntegracaoCeopag(operadoraEnum);
                                break;
                            case DCC_PINBANK:
                                operadoraCartaoVO.setCodigoIntegracaoPinBank(operadoraEnum);
                                break;
                            case DCC_ONE_PAYMENT:
                                operadoraCartaoVO.setCodigoIntegracaoOnePayment(operadoraEnum);
                                break;
                            case DCC_VINDI:
                                operadoraCartaoVO.setCodigoIntegracaoVindi(operadoraEnum);
                                break;
                            case DCC_FITNESS_CARD:
                                operadoraCartaoVO.setCodigoIntegracaoFitnessCard(operadoraEnum);
                                break;
                            case DCC_PAGBANK:
                                operadoraCartaoVO.setCodigoIntegracaoPagBank(operadoraEnum);
                                break;
                            case DCC_E_REDE:
                                operadoraCartaoVO.setCodigoIntegracaoERede(operadoraEnum);
                                break;
                            case DCC_MAXIPAGO:
                                operadoraCartaoVO.setCodigoIntegracaoMaxiPago(operadoraEnum);
                                break;
                        }
                        listaOperadorasGravar.add(operadoraCartaoVO);
                    } else {
                        //Se não tem uma Operadora com a Descrição Padrão, aqui ajusta a descrição e usa o objeto que foi criado inicialmente
                        operadora.setDescricao(operadoraEnum.getDescricaoOperadoraCartaoPadrao());
                        listaOperadorasGravar.add(operadora);
                    }
                }
                if (!UteisValidacao.emptyList(listaOperadorasGravar)) {
                    setListaOperadoraCriarAutomatico(listaOperadorasGravar);
                }
            }

            getFacade().getOperadoraCartao().criarOperadorasAutomatico(getListaOperadoraCriarAutomatico());
            montarSucessoGrowl("Operadoras configuradas.");
        } catch (Exception ex) {
            throw new Exception(ex.getMessage());
        }
    }

    public List<OperadoraCartaoVO> getListaOperadoraCriarAutomatico() {
        if (listaOperadoraCriarAutomatico == null) {
            listaOperadoraCriarAutomatico = new ArrayList<>();
        }
        return listaOperadoraCriarAutomatico;
    }

    public void setListaOperadoraCriarAutomatico(List<OperadoraCartaoVO> listaOperadoraCriarAutomatico) {
        this.listaOperadoraCriarAutomatico = listaOperadoraCriarAutomatico;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public ConvenioCobrancaRateioVO getConvenioCobrancaRateioVO() {
        if (convenioCobrancaRateioVO == null) {
            convenioCobrancaRateioVO = new ConvenioCobrancaRateioVO();
        }
        return convenioCobrancaRateioVO;
    }

    public void setConvenioCobrancaRateioVO(ConvenioCobrancaRateioVO convenioCobrancaRateioVO) {
        this.convenioCobrancaRateioVO = convenioCobrancaRateioVO;
    }

    public ConvenioCobrancaRateioItemVO getConvenioCobrancaRateioItemVO() {
        if (convenioCobrancaRateioItemVO == null) {
            convenioCobrancaRateioItemVO = new ConvenioCobrancaRateioItemVO();
        }
        return convenioCobrancaRateioItemVO;
    }

    public void setConvenioCobrancaRateioItemVO(ConvenioCobrancaRateioItemVO convenioCobrancaRateioItemVO) {
        this.convenioCobrancaRateioItemVO = convenioCobrancaRateioItemVO;
    }

    public void carregarDadosRateio() {
        try {
            setConvenioCobrancaRateioVO(new ConvenioCobrancaRateioVO());
            getConvenioCobrancaVO().setListaConvenioCobrancaRateioVO(getFacade().getConvenioCobrancaRateio().consultarPorConvenioCobranca(getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void novoConvenioCobrancaRateio() {
        try {
            getConvenioCobrancaRateioItemVO().setRecebedorPrincipal(false);
            limparMsg();
            setOnComplete("");
            setListaPlano(new ArrayList<>());
            setListaSelectItemPlano(new ArrayList<>());
            setListaSelectItemProduto(new ArrayList<>());
            setListaSelectItemRecebedores(new ArrayList<>());
            setConvenioCobrancaRateioVO(new ConvenioCobrancaRateioVO());
            if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                carregarListaRecebedoresPagarMeV1();
            } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                carregarListaRecebedoresStonePagarMeV5();
            } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                    getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)){
                carregarListaMerchants();
            }
            setOnComplete("Richfaces.showModalPanel('modalRateio');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void editarConvenioCobrancaRateio() {
        try {
            limparMsg();
            setOnComplete("");

            ConvenioCobrancaRateioVO convenioCobrancaRateioVO = (ConvenioCobrancaRateioVO) request().getAttribute("rateio");
            if (convenioCobrancaRateioVO == null) {
                throw new Exception("Erro ao obter rateio");
            }
            setConvenioCobrancaRateioVO(convenioCobrancaRateioVO);

            if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                carregarListaRecebedoresPagarMeV1();
            } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                carregarListaRecebedoresStonePagarMeV5();
            } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                    getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)){
                carregarListaMerchants();
            }
            carregarProdutos();
            carregarPlanos();
            setOnComplete("Notifier.cleanAll();Richfaces.showModalPanel('modalRateio');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gravarConvenioCobrancaRateio() {
        try {
            limparMsg();
            setOnComplete("");

            getConvenioCobrancaRateioVO().setConvenioCobrancaVO(getConvenioCobrancaVO());
            //validar dados
            ConvenioCobrancaRateioVO.validarDados(getConvenioCobrancaRateioVO(), getListaPlano(), getListaProduto());

            //validar padrão
            if (getConvenioCobrancaRateioVO().isPadrao()) {
                for (ConvenioCobrancaRateioVO obj : getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO()) {
                    if (obj.hashCode() != getConvenioCobrancaRateioVO().hashCode() && obj.isPadrao()) {
                        throw new Exception("O rateio \"" + obj.getDescricao() +"\" já está definido como padrão.");
                    }
                }
            }

            if (!UteisValidacao.emptyNumber(getConvenioCobrancaRateioVO().getPlanoVO().getCodigo())) {
                getConvenioCobrancaRateioVO().setPlanoVO(getFacade().getPlano().consultarPorChavePrimaria(getConvenioCobrancaRateioVO().getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            if (!UteisValidacao.emptyNumber(getConvenioCobrancaRateioVO().getProdutoVO().getCodigo())) {
                getConvenioCobrancaRateioVO().setProdutoVO(getFacade().getProduto().consultarPorChavePrimaria(getConvenioCobrancaRateioVO().getProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            List<ConvenioCobrancaRateioVO> lista = new ArrayList<>();
            for (ConvenioCobrancaRateioVO obj : getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO()) {
                if (obj.hashCode() != getConvenioCobrancaRateioVO().hashCode()) {
                    lista.add(obj);
                }
            }
            getConvenioCobrancaVO().setListaConvenioCobrancaRateioVO(lista);
            getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO().add(getConvenioCobrancaRateioVO());

            setOnComplete("Richfaces.hideModalPanel('modalRateio');");
            montarAviso("Atenção!!", "Agora clique no botão Gravar!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirConvenioCobrancaRateio() {
        try {
            limparMsg();
            setOnComplete("");

            ConvenioCobrancaRateioVO excluir = (ConvenioCobrancaRateioVO) request().getAttribute("rateio");
            if (excluir == null) {
                throw new Exception("Erro ao obter rateio");
            }

            List<ConvenioCobrancaRateioVO> lista = new ArrayList<>();
            for (ConvenioCobrancaRateioVO rateioVO : getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO()) {
                if (rateioVO.hashCode() != excluir.hashCode()) {
                    lista.add(rateioVO);
                }
            }
            getConvenioCobrancaVO().setListaConvenioCobrancaRateioVO(lista);

        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public List<SelectItem> getListaSelectItemRecebedores() {
        if (listaSelectItemRecebedores == null) {
            listaSelectItemRecebedores = new ArrayList<>();
        }
        return listaSelectItemRecebedores;
    }

    public void setListaSelectItemRecebedores(List<SelectItem> listaSelectItemRecebedores) {
        this.listaSelectItemRecebedores = listaSelectItemRecebedores;
    }

    public void editarConvenioCobrancaRateioItem() {
        try {
            limparMsg();
            setOnComplete("");

            ConvenioCobrancaRateioItemVO editar = (ConvenioCobrancaRateioItemVO) request().getAttribute("item");
            if (editar == null) {
                throw new Exception("Erro ao obter item");
            }

            setConvenioCobrancaRateioItemVO(editar);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gravarConvenioCobrancaRateioItem() {
        try {
            limparMsg();
            setOnComplete("");

            ConvenioCobrancaRateioItemVO.validarDados(getConvenioCobrancaRateioItemVO());

            //obter o nome do recebedor
            for (SelectItem item : getListaSelectItemRecebedores()) {
                if (item.getValue().equals(getConvenioCobrancaRateioItemVO().getIdRecebedor())) {
                    getConvenioCobrancaRateioItemVO().setNomeRecebedor(item.getLabel());
                    break;
                }
            }

            //validar se ele já existe na lista
            for (ConvenioCobrancaRateioItemVO item : getConvenioCobrancaRateioVO().getItens()) {
                if (item.hashCode() != getConvenioCobrancaRateioItemVO().hashCode() &&
                        item.getIdRecebedor().equalsIgnoreCase(getConvenioCobrancaRateioItemVO().getIdRecebedor())) {
                    throw new Exception("Não foi possível adicionar o recebedor \"" + getConvenioCobrancaRateioItemVO().getNomeRecebedor() + "\" pois ele já se encontra na lista.");
                }
            }

            boolean jaPossuiRecebedorPrincipal = false;
            for (ConvenioCobrancaRateioItemVO item : getConvenioCobrancaRateioVO().getItens()) {
                if ((item.getCodigo() != getConvenioCobrancaRateioItemVO().getCodigo()) && item.isRecebedorPrincipal()) {
                    jaPossuiRecebedorPrincipal = true;
                }
            }
            if (getConvenioCobrancaRateioItemVO().isRecebedorPrincipal() && jaPossuiRecebedorPrincipal) {
                throw new ConsistirException("Já existe um recebedor principal. Você precisa desmarcar o recebedor principal atual e depois marcar este.");
            }

            List<ConvenioCobrancaRateioItemVO> lista = new ArrayList<>();
            for (ConvenioCobrancaRateioItemVO obj : getConvenioCobrancaRateioVO().getItens()) {
                if (obj.hashCode() != getConvenioCobrancaRateioItemVO().hashCode()) {
                    lista.add(obj);
                }
            }

            getConvenioCobrancaRateioVO().setItens(lista);
            getConvenioCobrancaRateioVO().getItens().add(getConvenioCobrancaRateioItemVO());

            setConvenioCobrancaRateioItemVO(new ConvenioCobrancaRateioItemVO());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirConvenioCobrancaRateioItem() {
        try {
            limparMsg();
            setOnComplete("");

            ConvenioCobrancaRateioItemVO excluir = (ConvenioCobrancaRateioItemVO) request().getAttribute("item");
            if (excluir == null) {
                throw new Exception("Erro ao obter rateio");
            }

            List<ConvenioCobrancaRateioItemVO> lista = new ArrayList<>();
            for (ConvenioCobrancaRateioItemVO itemVO : getConvenioCobrancaRateioVO().getItens()) {
                if (itemVO.hashCode() != excluir.hashCode()) {
                    lista.add(itemVO);
                }
            }

            getConvenioCobrancaRateioVO().setItens(lista);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void carregarListaRecebedoresPagarMeV1() throws Exception {
        try {
            limparMsg();
            setOnComplete("");
            setListaSelectItemRecebedores(new ArrayList<>());

            PagarMeService pagarMeService = new PagarMeService(getFacade().getContrato().getCon(), getEmpresaLogado().getCodigo(), getConvenioCobrancaVO().getCodigo());
            List<RecebedorDTO> recebedores = pagarMeService.obterRecebedores();

            if (UteisValidacao.emptyList(recebedores)) {
                throw new Exception("Nenhum recebedor encontrado");
            }

            setListaSelectItemRecebedores(new ArrayList<>());
            for (RecebedorDTO dto : recebedores) {
                getListaSelectItemRecebedores().add(new SelectItem(dto.getId(), dto.getName().toUpperCase()));
            }
            Ordenacao.ordenarLista(getListaSelectItemRecebedores(), "label");

            carregarProdutos();
        } catch (Exception ex) {
            ex.printStackTrace();
           throw ex;
        }
    }

    private void carregarListaRecebedoresStonePagarMeV5() throws Exception {
        try {
            limparMsg();
            setOnComplete("");
            setListaSelectItemRecebedores(new ArrayList<>());

            StoneOnlineV5Service stoneOnlineV5Service = new StoneOnlineV5Service(getFacade().getContrato().getCon(), getConvenioCobrancaVO());
            List<RecebedorDTO> recebedores = stoneOnlineV5Service.obterRecebedoresCadastradosNoPortal();

            if (UteisValidacao.emptyList(recebedores)) {
                throw new Exception("Nenhum recebedor encontrado");
            }

            setListaSelectItemRecebedores(new ArrayList<>());
            for (RecebedorDTO dto : recebedores) {
                getListaSelectItemRecebedores().add(new SelectItem(dto.getId(), dto.getName().toUpperCase()));
            }
            Ordenacao.ordenarLista(getListaSelectItemRecebedores(), "label");

            carregarProdutos();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private void carregarListaMerchants() throws Exception {
        try {
            limparMsg();
            setOnComplete("");
            setListaSelectItemRecebedores(new ArrayList<>());
            Set<String> adicionados = new HashSet<>();

            PagoLivreService pagoLivreService = new PagoLivreService(Conexao.getFromSession(),
                    getConvenioCobrancaVO().getConfiguracoesEmpresa().get(0).getEmpresa().getCodigo(),
                    this.getConvenioCobrancaVO().getCodigo(), this.getConvenioCobrancaVO().getTipo().getTipoTransacao());
            RespostaHttpDTO respostaHttpDTO = pagoLivreService.consultarMerchant();
            if (respostaHttpDTO.getHttpStatus() != 200){
                throw new Exception("Não foi possível consultar o merchant:" + respostaHttpDTO.getResponse());
            }
            JSONObject jsonObject = new JSONObject(respostaHttpDTO.getResponse());

            //montar conta bancária para exibir no primeiro item da lista
            BankAccountPagoLivreDto bankAccountPagoLivreDto = new BankAccountPagoLivreDto(jsonObject.getJSONObject("bankAccount"));

            //adicionar primeiro item da lista manualemnte (a empresa atual)
            getListaSelectItemRecebedores().add(new SelectItem(jsonObject.optString("merchantId"), "*" + jsonObject.optString("name").toUpperCase() + " | "
                    + agenciaExibir(bankAccountPagoLivreDto) + " | " + contaExibir(bankAccountPagoLivreDto)+ "*"));

            adicionados.add(jsonObject.optString("merchantId"));

            String parentId = jsonObject.optString("parentMerchantId");
            if (!UteisValidacao.emptyString(parentId)) {

                //Aqui ele consulta todos os merchants relacionados para montar a lista...
                List<MerchantPagoLivreDto> merchants = pagoLivreService.obterMerchantsByParentId(parentId);
                if (!UteisValidacao.emptyList(merchants)) {
                    for (MerchantPagoLivreDto merchantItem : merchants) {
                        if (!adicionados.contains(merchantItem.getMerchantId())) {
                            getListaSelectItemRecebedores().add(new SelectItem(merchantItem.getMerchantId(), merchantItem.getName().toUpperCase() + " | " +
                                    agenciaExibir(merchantItem.getBankAccount()) + " | " + contaExibir(merchantItem.getBankAccount())));
                            adicionados.add(merchantItem.getMerchantId());
                        }
                    }
                }
            }

            Ordenacao.ordenarLista(getListaSelectItemRecebedores(), "label");

            carregarProdutos();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String agenciaExibir(BankAccountPagoLivreDto obj){
        return "Ag.: " + obj.getAgencyNumber() + (UteisValidacao.emptyString(obj.getAgencyDigit()) ? "" : "-") + obj.getAgencyDigit();
    }

    public String contaExibir(BankAccountPagoLivreDto obj){
        return "Conta: " + obj.getAccountNumber() + (UteisValidacao.emptyString(obj.getAccountDigit()) ? "" : "-") + obj.getAccountDigit();
    }

    public void carregarPlanos() {
        try {
            limparMsg();
            setOnComplete("");
            setListaSelectItemPlano(new ArrayList<>());

            setListaPlano(getFacade().getPlano().consultarPorCodigoEmpresa(getConvenioCobrancaRateioVO().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            for (PlanoVO obj : getListaPlano()) {
                getListaSelectItemPlano().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            Ordenacao.ordenarLista(getListaSelectItemPlano(), "label");
            getListaSelectItemPlano().add(0, new SelectItem(0, ""));

        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void carregarProdutos() throws Exception {
        try {
            limparMsg();
            setOnComplete("");
            setListaSelectItemProduto(new ArrayList<>());

            setListaProduto(getFacade().getProduto().consultarPorDescricaoAtivo("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            for (ProdutoVO obj : getListaProduto()) {
                getListaSelectItemProduto().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            Ordenacao.ordenarLista(getListaSelectItemProduto(), "label");
            getListaSelectItemProduto().add(0, new SelectItem(0, ""));

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<SelectItem> getListaSelectItemPlano() {
        if (listaSelectItemPlano == null) {
            listaSelectItemPlano = new ArrayList<>();
        }
        return listaSelectItemPlano;
    }

    public void setListaSelectItemPlano(List<SelectItem> listaSelectItemPlano) {
        this.listaSelectItemPlano = listaSelectItemPlano;
    }

    public List<SelectItem> getListaSelectItemProduto() {
        if (listaSelectItemProduto == null) {
            listaSelectItemProduto = new ArrayList<>();
        }
        return listaSelectItemProduto;
    }

    public void setListaSelectItemProduto(List<SelectItem> listaSelectItemProduto) {
        this.listaSelectItemProduto = listaSelectItemProduto;
    }

    public List<PlanoVO> getListaPlano() {
        if (listaPlano == null) {
            listaPlano = new ArrayList<>();
        }
        return listaPlano;
    }

    public void setListaPlano(List<PlanoVO> listaPlano) {
        this.listaPlano = listaPlano;
    }

    public List<ProdutoVO> getListaProduto() {
        if (listaProduto == null) {
            listaProduto = new ArrayList<>();
        }
        return listaProduto;
    }

    public void setListaProduto(List<ProdutoVO> listaProduto) {
        this.listaProduto = listaProduto;
    }

    public List<SelectItem> getListaSelectItemEmpresaRateio() {
        List<SelectItem> lista = new ArrayList<>();
        for(ConvenioCobrancaEmpresaVO cobrancaEmpresaVO : getConvenioCobrancaVO().getConfiguracoesEmpresa()) {
            lista.add(new SelectItem(cobrancaEmpresaVO.getEmpresa().getCodigo(), cobrancaEmpresaVO.getEmpresa().getNome()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemTipoProduto() {
        return TipoProduto.getTiposProdutosParaAutorizacaoCobranca(true);
    }

    public boolean isExisteRateioPlano() {
        for (ConvenioCobrancaRateioVO rateioVO : getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO()) {
            if (!UteisValidacao.emptyNumber(rateioVO.getPlanoVO().getCodigo())) {
                return true;
            }
        }
        return false;
    }

    public boolean isExisteRateioProduto() {
        for (ConvenioCobrancaRateioVO rateioVO : getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO()) {
            if (!UteisValidacao.emptyNumber(rateioVO.getProdutoVO().getCodigo())) {
                return true;
            }
        }
        return false;
    }

    public boolean isExisteRateioTipoProduto() {
        for (ConvenioCobrancaRateioVO rateioVO : getConvenioCobrancaVO().getListaConvenioCobrancaRateioVO()) {
            if (!UteisValidacao.emptyString(rateioVO.getTipoProduto())) {
                return true;
            }
        }
        return false;
    }

    public CredencialPJBankDTO getCredencialPJBankDTO() {
        if (credencialPJBankDTO == null) {
            credencialPJBankDTO = new CredencialPJBankDTO();
        }
        return credencialPJBankDTO;
    }

    public void setCredencialPJBankDTO(CredencialPJBankDTO credencialPJBankDTO) {
        this.credencialPJBankDTO = credencialPJBankDTO;
    }

    public void consultarCredencialPJBank() {
        try {
            limparMsg();
            setOnComplete("");
            setCredencialPJBankDTO(null);

            BoletosManager boletosManager = new BoletosManager(getConvenioCobrancaVO().getCredencialPJBank(), getConvenioCobrancaVO().getChavePJBank(), getConvenioCobrancaVO());
            String credencial = boletosManager.getInfoCredencial();
            JSONObject json = new JSONObject(credencial);
            if (json.optString("status").equalsIgnoreCase("400") && json.has("msg")) {
                throw new Exception(json.getString("msg"));
            }
            setCredencialPJBankDTO(new CredencialPJBankDTO(json));
            setOnComplete("Richfaces.showModalPanel('modalCredencialPJBank');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void consultarTaxasContaAsaas() {
        AsaasEmpresaService asaasEmpresaService;
        try {
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCodigoAutenticacao01())) {
                throw new Exception("Preencha o campo \"Chave da API\" e tente novamente");
            }
            limparMsg();
            setOnComplete("");
            setTaxasContaAsaasDTO(null);

            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), this.convenioCobrancaVO.getCodigoAutenticacao01(), this.convenioCobrancaVO.getAmbiente());
            setTaxasContaAsaasDTO(asaasEmpresaService.consultarTaxasConta());
            setOnComplete("Richfaces.showModalPanel('modalTaxasContaAsaas');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaService = null;
        }
    }

    public void consultarSituacaoCadastralContaAsaas() {
        AsaasEmpresaService asaasEmpresaService;
        try {
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCodigoAutenticacao01())) {
                throw new Exception("Preencha o campo \"Chave da API\" e tente novamente");
            }
            limparMsg();
            setOnComplete("");
            setSituacaoCadastralContaAsaasDTO(null);

            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), this.convenioCobrancaVO.getCodigoAutenticacao01(), this.convenioCobrancaVO.getAmbiente());
            setSituacaoCadastralContaAsaasDTO(asaasEmpresaService.consultarSituacaoCadastralConta());
            setOnComplete("Richfaces.showModalPanel('modalStatusContaAsaas');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaService = null;
        }
    }

    public void consultarDadosComerciaisContaAsaas() {
        AsaasEmpresaService asaasEmpresaService;
        try {
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCodigoAutenticacao01())) {
                throw new Exception("Preencha o campo \"Chave da API\" e tente novamente");
            }
            limparMsg();
            setOnComplete("");
            setDadosComerciaisContaAsaasDTO(null);

            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), this.convenioCobrancaVO.getCodigoAutenticacao01(), this.convenioCobrancaVO.getAmbiente());
            setDadosComerciaisContaAsaasDTO(asaasEmpresaService.consultarDadosComerciaisConta());
            setOnComplete("Richfaces.showModalPanel('modalDadosComerciaisContaAsaas');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaService = null;
        }
    }

    public void cadastrarChavePixContaAsaas() {
        //chave é necessária para gerar boleto híbrido (boleto/pix). Após cadastrar uma nova chave os boletos já terão o qrcode junto.
        AsaasEmpresaService asaasEmpresaService;
        try {
            setGerarBoletoHibrido(false);
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCodigoAutenticacao01())) {
                throw new Exception("Preencha o campo \"Chave da API\" e tente novamente");
            }
            limparMsg();
            setOnComplete("");

            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), this.convenioCobrancaVO.getCodigoAutenticacao01(), this.convenioCobrancaVO.getAmbiente());

            JSONObject retorno = asaasEmpresaService.cadastrarChavePix();
            String chaveCadastrada = retorno.optString("key");

            //sucesso
            if (!UteisValidacao.emptyString(chaveCadastrada)) {
                setGerarBoletoHibrido(true);
                setSucesso(true);
                setErro(false);
                montarSucessoGrowl("Boleto híbrido ativado com sucesso!");
            } else {
                montarErro("Não foi possível habilitar o boleto híbrido! Tente novamente mais tarde.");
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaService = null;
        }
    }

    public void solicitarConcessaoAcessoStone() {
        try {
            limparMsg();
            setOnComplete("");

            if (!getEmpresaLogado().isFacilitePayConciliacaoCartao()) {
                this.setOnComplete("Richfaces.showModalPanel('modalPropagandaFaciliteConciliacaoConv');");
                return;
            }

            if (UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao01().trim())) {
                throw new Exception("Informe o StoneCode.");
            }

            String msg = StoneOnlineServiceConciliation.concessaoAcesso(getConvenioCobrancaVO());
            if (msg.toLowerCase().contains("e-mail")) {
                montarAviso(msg);
            } else {
                montarSucessoGrowl(msg);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void verificarStatusConcessaoAcessoStone() {
        try {
            limparMsg();
            setOnComplete("");

            if (!getEmpresaLogado().isFacilitePayConciliacaoCartao()) {
                this.setOnComplete("Richfaces.showModalPanel('modalPropagandaFaciliteConciliacaoConv');");
                return;
            }

            if (UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao01().trim())) {
                throw new Exception("Informe o StoneCode.");
            }

            String msg = StoneOnlineServiceConciliation.verificarStatusConcessaoAcesso(getConvenioCobrancaVO());
            if (msg.toLowerCase().contains("does not have access")) {
                msg = "A Stone ainda não liberou o acesso na conciliação do Stone Code " + getConvenioCobrancaVO().getCodigoAutenticacao01().trim();
                montarAviso(msg);
            } else if (msg.contains("liberado")) {
                msg = "A Stone liberou o acesso da conciliação do Stone Code " + getConvenioCobrancaVO().getCodigoAutenticacao01().trim();
                ;
                montarSucessoGrowl(msg);
            } else {
                msg = "Não foi possível verificar o Status da liberação da Stone";
                montarAviso(msg);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void solicitarConcessaoAcessoRedeOnline() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyString(getConvenioCobrancaVO().getNumeroContrato().trim())) {
                throw new Exception("Informe o Ponto de Venda.");
            }
            String msg = "";
            if (UteisValidacao.emptyString(getConvenioCobrancaVO().getRequestIdConciliacaoRedeOnline())) {
                msg = getFacade().getERedeServiceConciliacao().integrarConciliacaoOnline(getConvenioCobrancaVO());
            } else if (getConvenioCobrancaVO().getStatusConciliacaoRedeOnline().equals(ERedeStatusConciliacaoEnum.APROVADO)) {
                msg = getConvenioCobrancaVO().getStatusConciliacaoRedeOnline().getDescricao();
            } else {
                getFacade().getERedeServiceConciliacao().statusIntegracaoConciliacaoOnline(getConvenioCobrancaVO());
                msg = getConvenioCobrancaVO().getStatusConciliacaoRedeOnline().getDescricao();
            }
            if (!UteisValidacao.emptyString(msg)) {
                montarSucessoGrowl(msg);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void limparConcessaoAcessoRedeOnline() {
        try {
            limparMsg();
            setOnComplete("");

            String msg = "";
            if (!UteisValidacao.emptyString(getConvenioCobrancaVO().getRequestIdConciliacaoRedeOnline())) {
                msg = getFacade().getERedeServiceConciliacao().limparDadosIntegracaoConciliacaoOnline(getConvenioCobrancaVO(), getUsuarioLogado());
            } else {
                msg = "Os dados de Integração já foram apagados.";
            }
            if (!UteisValidacao.emptyString(msg)) {
                montarSucessoGrowl(msg);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public boolean isApresentarSomenteExtrato() {
        return getConvenioCobrancaVO() != null && (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE));
    }

    public void acaoSomenteExtrato() {
        if (getConvenioCobrancaVO() != null && getConvenioCobrancaVO().isSomenteExtrato()) {
            getConvenioCobrancaVO().setAmbiente(AmbienteEnum.PRODUCAO);

        }
    }

    public String getObservacaoConvenioCobranca() {
        if (getConvenioCobrancaVO() == null) {
            return "";
        }

        if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            StringBuilder obs = new StringBuilder();
            obs.append("Este convênio é do tipo Pagar.me.<br/>");
            obs.append("Para que o pagamento recorrente funcione é necessário que a academia entre <br/>");
            obs.append("em contato com Pagar.me para solicitar a \"remoção da obrigatoriedade de CVV\" <br/>");
            obs.append("e informar que irá utilizar o sistema da Pacto.");
            return obs.toString();
        } else {
            return "";
        }
    }

    private void validarUtilizadoRetentativa() throws Exception {
        if (this.getConvenioCobrancaVO() == null || UteisValidacao.emptyNumber(this.getConvenioCobrancaVO().getCodigo()) ||
                this.getConvenioCobrancaVO().getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
            return;
        }
        boolean utilizado = getFacade().getConfiguracaoReenvioMovParcelaEmpresa().existeConfiguracaoReenvioMovParcelaEmpresa(0, this.getConvenioCobrancaVO().getCodigo());
        if (utilizado) {
            throw new Exception("Para inativar primeiro é necessário remover o convênio da configuração de \"Retentativa Automática Convênio de Cobrança\" nas configurações da empresa na aba \"Cobrança\".");
        }
    }

    private void validarRemessasPendentes() throws Exception {
        if (this.getConvenioCobrancaVO() == null ||
                UteisValidacao.emptyNumber(this.getConvenioCobrancaVO().getCodigo()) ||
                this.getConvenioCobrancaVO().getSituacao().equals(SituacaoConvenioCobranca.ATIVO) ||
                !this.getConvenioCobrancaVO().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC)) {
            return;
        }
        boolean utilizado = getFacade().getRemessa().existeRemessaPendente(this.getConvenioCobrancaVO().getCodigo());
        if (utilizado) {
            throw new Exception("Não é possivel inativar o convênio pois existem remessas aguardando retorno.");
        }
    }

    public String getCodeAutorizacaoCielo() {
        return codeAutorizacaoCielo;
    }

    public void setCodeAutorizacaoCielo(String codeAutorizacaoCielo) {
        this.codeAutorizacaoCielo = codeAutorizacaoCielo;
    }

    public void consultarStatusCredenciamentoCielo() {
        try {
            limparMsg();
            ConvenioCobranca convenioCobranca = new ConvenioCobranca();
            String status = convenioCobranca.consultarStatusCredenciamentoCielo(convenioCobrancaVO);
            if (status.equals("COMPLETED")) {
                setSucesso(true);
                setErro(false);
                setMensagem("Credenciado na Cielo");
            } else {
                montarErro("Credenciamento Pendente. Consulte novamente em alguns segundo, se persistir contate seu consultor Cielo");
            }

        } catch (Exception e) {
            e.printStackTrace();
            montarErro("Erro ao consultar, certifique-se de ter solicitando o credenciamento");
        }
    }

    public boolean isExibirExpiracaoPix(){
        return Uteis.isAmbienteDesenvolvimentoTeste();
    }

    public boolean isExibirPactoPay() {
        try {
            return  (Uteis.isAmbienteDesenvolvimentoTeste() ||
                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("Luiz Felipe") ||
                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("eduardo") ||
                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("estulano") ||
                    getUsuarioLogado().getUserOamd().equalsIgnoreCase("Maurin Noleto")) &&
                    convenioCobrancaVO != null &&
                    convenioCobrancaVO.getTipo() != null &&
                    (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) ||
                    convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                    convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) ||
                    convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE));
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }

    }

    public void gerarPadraoDiretorioLocalLogExtrato() {
        try{
            limparMsg();
            setMsgAlert("");
            setExemplo("");
            String url = ExecuteRequestHttpService.gerarURLConexaoOAMD(getKey());
            BackupClienteDTO dados = ExecuteRequestHttpService.consumirEndPointDadosParaTestOAMD(url);
            if(dados != null) {
                getConvenioCobrancaVO().setDiretorioLocalLogExtrato("/opt/RobotRunner/logs/extrato-" + dados.getNomeBD().replace("bdzillyon", "").toLowerCase() + ".log");
            }else{
                getConvenioCobrancaVO().setDiretorioLocalLogExtrato("/opt/RobotRunner/logs/extrato-{identificadorEmpresa}.log");
            }
        }catch (Exception e){
            this.convenioCobrancaVO.setDiretorioLocalLogExtrato("/opt/RobotRunner/logs/extrato-{identificadorEmpresa}.log");
            montarErro("Não foi possivel obter identificador da empresa no oamd.");
        }
    }

    public int getQtdMaximaLinhasApresentarLogExtrato() {
        return qtdMaximaLinhasApresentarLogExtrato;
    }

    public void setQtdMaximaLinhasApresentarLogExtrato(int qtdMaximaLinhasApresentarLogExtrato) {
        this.qtdMaximaLinhasApresentarLogExtrato = qtdMaximaLinhasApresentarLogExtrato;
    }

    private void adicionarModuloPix() {
        try {
            String path = Uteis.getUrlOAMD() + "/prest/adm/" + getKey() + "/ativarPix";
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, null, MetodoHttpEnum.POST);
            if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                throw new Exception(respostaHttpDTO.getResponse());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro = "TD";
        }
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }
    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("TD", "Todos"));
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("IN", "Inativo"));
        return objs;
    }

    public Integer getSituacaoFiltroTipo() {
        if (situacaoFiltroTipo == null) {
            situacaoFiltroTipo = TipoCobrancaEnum.NENHUM.getId();
        }
        return situacaoFiltroTipo;
    }

    public void setSituacaoFiltroTipo(Integer situacaoFiltroTipo) {
        this.situacaoFiltroTipo = situacaoFiltroTipo;
    }

    public List<SelectItem> getListaSelectItemTipoFiltro() {
        List<SelectItem> objs = new ArrayList<>();
        for (TipoCobrancaEnum tipo : TipoCobrancaEnum.values()) {
            if (!tipo.equals(TipoCobrancaEnum.NENHUM)) {
                objs.add(new SelectItem(tipo.getId(), tipo.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(objs, "label");
        objs.add(0, new SelectItem(TipoCobrancaEnum.NENHUM.getId(), "Todos"));
        return objs;
    }

    private void preencherDadosDesbloqueioCobrancas() throws Exception {
            movParcelaVOS = new ArrayList<>();
            totalMovParcela = 0.0;
            boolean consultarParcelas = false;

            for (ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO : convenioCobrancaVO.getConfiguracoesEmpresa()) {
                EmpresaVO empresa = getFacade().getEmpresa().consultarPorCodigo(convenioCobrancaEmpresaVO.getEmpresa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                    for (ConfiguracaoReenvioMovParcelaEmpresaVO configs : empresa.getConfiguracaoReenvioMovParcelaEmpresaVOS()) {
                        if (convenioCobrancaVO.getCodigo().equals(configs.getConvenioCobrancaVO().getCodigo())) {
                            consultarParcelas = true;
                            break;
                        }
                    }
                } else {
                    consultarParcelas = true;
                }

                if (consultarParcelas) {
                    ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenioCobrancaEmpresaVO.getConvenioCobranca().getCodigo(),
                            convenioCobrancaEmpresaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

                    if (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {
                        movParcelaVOS.addAll(getFacade().getMovParcela().consultarParcelasEmAbertoParaPagamento(convenioCobrancaVO, Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS));
                        for (MovParcelaVO movParcelaVO : movParcelaVOS) {
                            totalMovParcela += movParcelaVO.getValorParcela();
                        }
                        Ordenacao.ordenarLista(movParcelaVOS, "pessoa");
                    } else if (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC) || convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
                        RemessaService remessaService = new RemessaService();
                        movParcelaVOS.addAll(remessaService.consultarParcelasProcessoAutomatico(Calendario.hoje(), convenioCobrancaVO.getEmpresa(), convenioCobrancaVO));
                        Map<Integer, EmpresaVO> mapaEmpresa = getFacade().getEmpresa().obterMapaEmpresas(Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                        Map<Integer, PessoaVO> mapaPessoa = new HashMap<>();
                        for (MovParcelaVO movParcelaVO : movParcelaVOS) {
                            totalMovParcela += movParcelaVO.getValorParcela();
                            movParcelaVO.setEmpresa(mapaEmpresa.get(movParcelaVO.getEmpresa().getCodigo()));

                            PessoaVO pessoaVO = mapaPessoa.get(movParcelaVO.getPessoa().getCodigo());
                            if (pessoaVO == null) {
                                pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(movParcelaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                mapaPessoa.put(pessoaVO.getCodigo(), pessoaVO);
                            }
                            movParcelaVO.setPessoa(pessoaVO);
                        }
                        Ordenacao.ordenarLista(movParcelaVOS, "pessoa");
                    }
                }
            }
            setMsgAlert("Richfaces.showModalPanel('modalDesbloqueioCobrancasAutomaticas');");
    }

    public Integer getMovParcelaVOS_Size() {
        return getMovParcelaVOS().size();
    }

    public List<MovParcelaVO> getMovParcelaVOS() {
        if (movParcelaVOS == null) {
            movParcelaVOS = new ArrayList<>();
        }
        return movParcelaVOS;
    }

    public void setMovParcelaVOS(List<MovParcelaVO> movParcelaVOS) {
        this.movParcelaVOS = movParcelaVOS;
    }

    public void naoDesbloquearCobrancas() {
        convenioCobrancaVO.setBloquearCobrancaAutomatica(true);
    }

    public String getTotalMovParcela() {
        return Uteis.getDoubleFormatado(totalMovParcela);
    }

    public void setTotalMovParcela(Double totalMovParcela) {
        this.totalMovParcela = totalMovParcela;
    }

    public void salvar() {
            try {
                if (getConvenioCobrancaVO().getTipo().isTransacaoOnline()) {
                    gravarOperadorasCartaoCreditoOnline();
                }
                limparMsg();
                setMsgAlert("");
                gravar(false);
            } catch (Exception ex) {
                ex.printStackTrace();
                montarErro(ex);
            }
    }

    public void gravarDesbloqueioCobrancas(){
        try {
            convenioCobrancaVO.setBloquearCobrancaAutomatica(true); //caso o usuário feche o modal, não pode estar desbloqueado. Então inicializar aqui.
            limparMsg();
            setMsgAlert("");
            setLabelsCamposExibirNoModalTokenOperacao("Bloquear cobranças automáticas");
            setQtdCamposSensiveisAlterados(1);

            boolean isUsuarioOAMD = !UteisValidacao.emptyString(getUsuarioLogado().getUserOamd().trim()) && !getUsuarioLogado().getUserOamd().equalsIgnoreCase("undefined");
            boolean solicitarToken = (!isUsuarioOAMD || isUsuarioOAMD && !isUsuarioOAMDPodeAlterarConvenio()) && !isRedePratique(getKey());

            if (solicitarToken) {
                TokenOperacaoControle control = getControlador(TokenOperacaoControle.class);
                setAbrirFecharModalTokenOperacao("Richfaces.showModalPanel('modalTokenOperacao');document.getElementById('formModalTokenOperacao:inputToken1').focus();");
                control.init("",
                        this, "validarTokenEGravarDesbloqueioCobrancas", "", "",
                        "", getLabelsCamposExibirNoModalTokenOperacao(), getQtdCamposSensiveisAlterados(), "form",
                        RecursoSistema.CODIGO_AUTENTICACAO_CONVENIO_COBRANCA_GEROU);
                //enviar notificação push depois que abrir o modal:
                enviaNotificacaoPushTokenPactoApp();
                setMsgAlert("Richfaces.hideModalPanel('modalDesbloqueioCobrancasAutomaticas');");
            } else {
                //chegou até aqui então não precisa pedir token, já pode continuar fazer o update no desbloqueio de cobranças
                getFacade().getConvenioCobranca().alterarBloquearCobrancasAutomaticas(convenioCobrancaVO.getCodigo(), false);

                setMsgAlert("Richfaces.hideModalPanel('modalDesbloqueioCobrancasAutomaticas');");
                convenioCobrancaVO.setBloquearCobrancaAutomatica(false);

                montarSucessoGrowl("Cobranças automáticas desbloqueadas com sucesso!");
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void adicionarTagMatricula() throws Exception {
        getConvenioCobrancaVO().setInstrucoesBoleto(getConvenioCobrancaVO().getInstrucoesBoleto() + "\n<matricula>MATRICULA: TAG_MATRICULA</matricula>");
    }

    public List<SelectItem> getListaSelectItemCurrencyConvenio() {
        return listaSelectItemCurrencyConvenio;
    }

    public void setListaSelectItemCurrencyConvenio(List<SelectItem> listaSelectItemCurrencyConvenio) {
        this.listaSelectItemCurrencyConvenio = listaSelectItemCurrencyConvenio;
    }

    public List<BancoVO> getBancosVOs() {
        return bancosVOs;
    }

    public void setBancosVOs(List<BancoVO> bancosVOs) {
        this.bancosVOs = bancosVOs;
    }

    public List<String> getLogsExtrato() {
        return logsExtrato;
    }

    public void setLogsExtrato(List<String> logsExtrato) {
        this.logsExtrato = logsExtrato;
    }

    public List<SelectItem> getListaLayoutBoletoOnline() {
        List<SelectItem> lista = new ArrayList<>();
        for (ArquivoLayoutRemessaEnum obj : ArquivoLayoutRemessaEnum.values()) {
            if (!obj.equals(ArquivoLayoutRemessaEnum.NENHUM) &&
                    !obj.equals(ArquivoLayoutRemessaEnum.DCC) &&
                    !obj.equals(ArquivoLayoutRemessaEnum.BOLETO_SICREDI) &&
                    !obj.equals(ArquivoLayoutRemessaEnum.BOLETO_SANTANDER) &&
                    !obj.equals(ArquivoLayoutRemessaEnum.BOLETO_CAIXA) &&
                    !obj.equals(ArquivoLayoutRemessaEnum.BOLETO_BRB) &&
                    !obj.equals(ArquivoLayoutRemessaEnum.BOLETO_DAYCOVAL) &&
                    !obj.equals(ArquivoLayoutRemessaEnum.BOLETO_CLUBE)) {
                lista.add(new SelectItem(obj, obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(ArquivoLayoutRemessaEnum.NENHUM, ""));
        return lista;
    }

    public String getTitleExplicacaoRateio() {
        StringBuilder title = new StringBuilder();
        if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME) || getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
            title.append("Aqui será possível cadastrar os rateios desejados referente aos recebimentos");
            title.append("<br/>de cada parcela do sistema que for cobrada através deste convênio de cobrança.");
            title.append("<br/>Primeiramente, você deve cadastrar os recebedores com as suas respectivas contas");
            title.append("<br/>bancárias lá no portal da Pagar.me e só depois deve vir aqui para configurar os");
            title.append("<br/>percentuais de cada recebimento realizado por este convênio de cobrança.");
            return title.toString();
        } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            title.append("Aqui será possível cadastrar os rateios (splits) desejados referente aos recebimentos");
            title.append("<br/>de cada parcela do sistema que for cobrada através deste convênio de cobrança.");
            title.append("<br/>Primeiramente, você deve cadastrar os merchants lá no portal da PagoLivre");
            title.append("<br/>e só depois deve vir aqui para configurar os percentuais de cada recebimento");
            title.append("<br/>realizado por este convênio de cobrança.");
            return title.toString();
        } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            title.append("Aqui será possível cadastrar os rateios (splits) desejados referente aos recebimentos");
            title.append("<br/>de cada parcela do sistema que for cobrada através deste convênio de cobrança.");
            title.append("<br/>Primeiramente, você deve cadastrar os merchants lá no portal da Fypay");
            title.append("<br/>e só depois deve vir aqui para configurar os percentuais de cada recebimento");
            title.append("<br/>realizado por este convênio de cobrança.");
            return title.toString();
        } else {
            title.append("Aqui será possível cadastrar os rateios (splits) desejados referente aos recebimentos");
            title.append("<br/>de cada parcela do sistema que for cobrada através deste convênio de cobrança.");
            return title.toString();
        }
    }

    public String getTitleRecebedoresRateio() {
        StringBuilder title = new StringBuilder();
        if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
            title.append("Exibe como possíveis escolhas os recebedores que estão cadastrados lá no portal da Pagar.me.");
            title.append("<br/>Para cadastrar um novo recebedor, faça isto lá no portal deles e irá aparecer como opção aqui automaticamente.");
            return title.toString();
        } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            title.append("Exibe como possíveis escolhas os merchants que estão vinculados à esta empresa lá no portal da PagoLivre");
            title.append("<br/>Para vincular um novo merchant, faça isto lá no portal da PagoLivre e irá aparecer como opção aqui automaticamente.");
            return title.toString();
        } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            title.append("Exibe como possíveis escolhas os merchants que estão vinculados à esta empresa lá no portal da Fypay");
            title.append("<br/>Para vincular um novo merchant, faça isto lá no portal da Fypay e irá aparecer como opção aqui automaticamente.");
            return title.toString();
        } else {
            title.append("Recebedores disponíveis");
            return title.toString();
        }
    }

    public String getTitleIdRecebedor() {
        if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME) || getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
           return "Id Recebedor:";
        } else if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
            return "Merchant Id:";
        } else {
            return "Id:";
        }
    }

    public String getTitleTipoBoletoPJBank() {
            StringBuilder title = new StringBuilder();
            title.append("<b>Defina o tipo do boleto a ser gerado</b>");
            title.append("<br/><br/><b>Somente Boleto:</b> ");
            title.append("Será gerado apenas o boleto convencional, contendo informações como linha digitável, código de barras e outras informações.");
            title.append("<br/><b>Boleto e QR Code Pix:</b> ");
            title.append("Será gerado o boleto híbrido, contendo informações como linha digitável, código de barras e <b>também</b> o QR Code Pix para pagamento.");
            title.append("<br/><b>Somente QR Code Pix:</b> ");
            title.append("Será gerado somente o QR Code Pix para pagamento, sem nenhuma informação relacionada a boleto.");
            return title.toString();
        }

    public String getTitleExpiracaoPix() {
        StringBuilder title = new StringBuilder();
        if (getConvenioCobrancaVO() != null && getConvenioCobrancaVO().isTipoPix()) {
            if (getConvenioCobrancaVO().isPixPjBank()) {
                title.append("<b>Quantidade em dias, que o pix ficará ativo/disponível para pagamento:</b> </br></br>");
                title.append("Valor padrão sugerido: de 1 a 3 dias </br></br>");
            } else {
                title.append("<b>Tempo em segundos, que o pix ficará ativo/disponível para pagamento:</b> </br></br>");
                title.append("86400 &nbsp; segundos | 1 dia | (valor padrão sugerido) </br>");
                title.append("172800 segundos | 2 dias </br>");
                title.append("259200 segundos | 3 dias | (valor máximo permitido) </br></br>");
            }

            title.append("<b><font color=\"" + "#FF0000" + "\">" + "Atenção: " + "</font></b></br>");
            title.append("O sistema utilizará o tempo colocado aqui apenas como referência, isso porque nós temos 2 horários fixos (às 12:30 e às 22:30 - <b>Horário de Brasília</b>) </br>");
            title.append("em que os pix são consultados para serem finalizados caso tenha dado o seu tempo de expiração.</br>");
            title.append("Isso quer dizer que <b>na prática</b>, o tempo de expiração do pix é sempre um pouco maior do que o informado aqui.</br>");
            title.append("No momento de gerar o pix, o tempo de expiração na prática será a seguinte fórmula: </br>");
            title.append("(Tempo informado aqui + tempo restante para chegar no próximo processo de expiração), onde o próximo processo de expiração pode ser as 12:30 ou às 22:00.</br></br>");
            title.append("<b>É possível saber a data/hora exata em que o pix irá expirar? </b> </br>");
            title.append("SIM, basta acessar a tela do cliente, depois ir na aba FINANCEIRO --> COBRANÇAS --> PIX.</br>");
            title.append("Em cada pix tem a coluna \"Data\" onde ao lado da data tem um ícone informativo, onde ao passar o cursor do mouse, irá exibir a data/hora exata em que o pix irá expirar ou então quando ele expirou caso já tenha expirado.");
        } else if (getConvenioCobrancaVO().isPixPjBank()) {
            String diaAteDispPgtoApresentar = Uteis.getData(Calendario.somarDias(Calendario.hoje(), 3));
            String diaAteDispPgtoExpirar = Uteis.getData(Calendario.somarDias(Calendario.hoje(), 4));
            title.append("<b>Quantidade em dias, que o pix ficará ativo/disponível para pagamento:</b> </br></br>");
            title.append("O dia exato que o pix foi gerado não conta.</br>");
            title.append("Ex: Supondo que você configurou 3 dias, na prática, quer dizer que se você gerar um pix hoje, então ele estará disponível para pagamento" +
                    " até o final do dia </br>" + diaAteDispPgtoApresentar + ", <b>porém</b> será expirado de fato sempre às 12:30 - <b>Horário de Brasília</b> no dia seguinte ao vencimento, neste exemplo seria: " + diaAteDispPgtoExpirar + " às 12:30");
        }
        return title.toString();
    }

    public void prepararCamposUploadArquivo(boolean exibirCampoSenhaArquivo) {

        //arquivo 1
        setConvenioCobrancaArquivo1(new ConvenioCobrancaArquivoVO());
        setFezUploadArquivo1(false);
        setSenhaArquivo("");
        setNomeArquivo1("");
        setExibirCampoSenhaArquivo(exibirCampoSenhaArquivo);
        setPossuiArquivo1(false);

        //arquivo 2
        setConvenioCobrancaArquivo2(new ConvenioCobrancaArquivoVO());
        setFezUploadArquivo2(false);
        setSenhaArquivo("");
        setNomeArquivo2("");
        setExibirCampoSenhaArquivo(exibirCampoSenhaArquivo);
        setPossuiArquivo2(false);
    }

    public void limparCamposUploadArquivo1() {
        setConvenioCobrancaArquivo1(new ConvenioCobrancaArquivoVO());
        setFezUploadArquivo1(false);
        setSenhaArquivo("");
        setNomeArquivo1("");
        if (!getConvenioCobrancaVO().isPixInter()) {
            setExibirCampoSenhaArquivo(true);
        } else {
            setExibirCampoSenhaArquivo(false);
        }
        setPossuiArquivo1(false);
    }

    public void limparCamposUploadArquivo2() {
        setConvenioCobrancaArquivo2(new ConvenioCobrancaArquivoVO());
        setFezUploadArquivo2(false);
        setSenhaArquivo("");
        setNomeArquivo2("");
        if (!getConvenioCobrancaVO().isPixInter()) {
            setExibirCampoSenhaArquivo(true);
        } else {
            setExibirCampoSenhaArquivo(false);
        }
        setPossuiArquivo2(false);
    }

    public void carregarDadosArquivo() {
        try {
            setConvenioCobrancaArquivo1(null);
            setConvenioCobrancaArquivo2(null);
            setNomeArquivo1("");
            setNomeArquivo2("");
            setPossuiArquivo1(false);
            setPossuiArquivo2(false);
            setListaConvenioCobrancaArquivoVO(null);

            if (getConvenioCobrancaVO().isPixInter()) {
                setListaConvenioCobrancaArquivoVO(getFacade().getConvenioCobrancaArquivo().consultarListaPorConvenioCobranca(getConvenioCobrancaVO().getCodigo(), true));
                if (UteisValidacao.emptyList(getListaConvenioCobrancaArquivoVO())) {
                    setPossuiArquivo1(false);
                    setExibirCampoSenhaArquivo(false);
                    return;
                }
                for (ConvenioCobrancaArquivoVO arquivo : getListaConvenioCobrancaArquivoVO()) {
                    arquivo.setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorChavePrimaria(arquivo.getConvenioCobranca(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    arquivo.setSenha(null);
                    if (!UteisValidacao.emptyString(arquivo.getNomeArquivoOriginal()) && arquivo.getNomeArquivoOriginal().contains(".pfx")) {
                        setConvenioCobrancaArquivo1(arquivo);
                        setNomeArquivo1(arquivo.getNomeArquivoOriginal());
                        setPossuiArquivo1(true);
                        setExibirCampoSenhaArquivo(false);
                    }
                }
            } else if (getConvenioCobrancaVO().isPixBradesco()) {
                setListaConvenioCobrancaArquivoVO(getFacade().getConvenioCobrancaArquivo().consultarListaPorConvenioCobranca(getConvenioCobrancaVO().getCodigo(), true));
                if (UteisValidacao.emptyList(getListaConvenioCobrancaArquivoVO())) {
                    setPossuiArquivo1(false);
                    setExibirCampoSenhaArquivo(true);
                    return;
                }
                for (ConvenioCobrancaArquivoVO arquivo : getListaConvenioCobrancaArquivoVO()) {
                    arquivo.setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorChavePrimaria(arquivo.getConvenioCobranca(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (!UteisValidacao.emptyString(arquivo.getNomeArquivoOriginal()) && (arquivo.getNomeArquivoOriginal().contains(".pfx") || arquivo.getNomeArquivoOriginal().contains(".p12"))) {
                        setConvenioCobrancaArquivo1(arquivo);
                        setNomeArquivo1(arquivo.getNomeArquivoOriginal());
                        setPossuiArquivo1(true);
                        setExibirCampoSenhaArquivo(false);
                    }
                }
            } else if (getConvenioCobrancaVO().isDccCaixaOnline()) {
                setListaConvenioCobrancaArquivoVO(getFacade().getConvenioCobrancaArquivo().consultarListaPorConvenioCobranca(getConvenioCobrancaVO().getCodigo(), false));
                if (UteisValidacao.emptyList(getListaConvenioCobrancaArquivoVO())) {
                    setPossuiArquivo1(false);
                    setPossuiArquivo2(false);
                    return;
                }
                for (ConvenioCobrancaArquivoVO item : getListaConvenioCobrancaArquivoVO()) {
                    if (!UteisValidacao.emptyString(item.getNomeArquivoOriginal()) && (item.getNomeArquivoOriginal().endsWith(".pub"))) {
                        setConvenioCobrancaArquivo1(item);
                        setNomeArquivo1(item.getNomeArquivoOriginal());
                        setPossuiArquivo1(true);
                    }
                    if (!UteisValidacao.emptyString(item.getNomeArquivoOriginal()) && (item.getNomeArquivoOriginal().endsWith(".key"))) {
                        setConvenioCobrancaArquivo2(item);
                        setNomeArquivo2(item.getNomeArquivoOriginal());
                        setPossuiArquivo2(true);
                    }
                }
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void acaoAlterarSituacao() {
        if (getConvenioCobrancaVO().getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
            getConvenioCobrancaVO().setApresentarInativoNoPactoPay(false);
        }
    }

    public String getTitleCampoCertificadoDigitalBradesco() {
        if (getConvenioCobrancaArquivo1() != null && getConvenioCobrancaArquivo1().getCodigo() != 0) {
            return "Existe um certificado digital privado configurdo para este convênio de cobrança. </br>" +
                    "Não é possível ver a senha do certificado que foi configurada por questões de segurança. </br>" +
                    "Para realizar a troca, você deve primeiramente remover o atual e depois adicionar um novo." +
                    "</br><b>O certificado privado é uma exigência do próprio Bradesco.</b>";
        }
        return "<b>Atenção: Recomendamos primeiramente configurar o convênio sem nenhum certificado e consequentemente sem nenhuma senha, pois dessa forma será utilizado o certificado e senha padrão da Pacto que já funciona na maioria das vezes. </br>" +
                " Se dessa forma não funcionar, aí sim você deve tentar configurar o seu certificado do Bradesco, mas conforme mencionado, primeiramente priorize configurar sem. </b> </br></br>" +
                "Faça aqui o upload do certificado digital <b>privado</b>. Será aceito somente no formato <b>.pfx</b> ou <b>.p12</b>." +
                "</br>Após adicionar o arquivo, não esqueça de clicar no botão \"Enviar arquivo\"." +
                "</br>Após gravar o convênio, a senha do certificado não será mais exibida aqui por questões de segurança.";
    }

    public String getTitleSenhaCertificadoDigitalBradesco() {
        return "<b>Atenção: Recomendamos primeiramente configurar o convênio sem nenhum certificado e consequentemente sem nenhuma senha, pois dessa forma será utilizado o certificado e senha padrão da Pacto que já funciona na maioria das vezes. </br>" +
                " Se dessa forma não funcionar, aí sim você deve tentar configurar o seu certificado do Bradesco, mas conforme mencionado, primeiramente priorize configurar sem. </b> </br></br>" +
                "</br>Caso informe a senha, após gravar o convênio, a senha do certificado não será mais exibida aqui por questões de segurança.";
    }

    public String getTitleArquivo1Existente() throws Exception {
        try {
            StringBuilder title = new StringBuilder();
            if (convenioCobrancaArquivo1 != null && convenioCobrancaArquivo1.getCodigo() != 0) {
                if (getConvenioCobrancaVO().isPixInter()) {
                    title.append("Certificado único gerado automaticamente pela Pacto com base no certificado público (.crt) e privado (.key).</br> ");
                }
                title.append("<b>Certificado:</b> " + convenioCobrancaArquivo1.getNomeArquivoOriginal());
                title.append("<br/><b>Data do upload:</b> " + convenioCobrancaArquivo1.getDataUploadString());
                UsuarioVO usuarioVO = getFacade().getUsuario().consultarPorChavePrimaria(convenioCobrancaArquivo1.getUsuario(), Uteis.NIVELMONTARDADOS_MINIMOS);
                title.append("<br/><b>Usuário:</b> " + usuarioVO.getNome());
                title.append("<br/><b>Cód. Interno:</b> " + convenioCobrancaArquivo1.getCodigo());

                return title.toString();
            }
            return "Não foi possível obter detalhes do certificado";
        } catch (Exception e) {
            return "Não foi possível obter detalhes do certificado";
        }
    }

    public String getTitleArquivo2Existente() throws Exception {
        try {
            StringBuilder title = new StringBuilder();
            if (convenioCobrancaArquivo2 != null && convenioCobrancaArquivo2.getCodigo() != 0) {
                title.append("<b>Certificado:</b> " + convenioCobrancaArquivo2.getNomeArquivoOriginal());
                title.append("<br/><b>Data do upload:</b> " + convenioCobrancaArquivo2.getDataUploadString());
                UsuarioVO usuarioVO = getFacade().getUsuario().consultarPorChavePrimaria(convenioCobrancaArquivo2.getUsuario(), Uteis.NIVELMONTARDADOS_MINIMOS);
                title.append("<br/><b>Usuário:</b> " + usuarioVO.getNome());
                title.append("<br/><b>Cód. Interno:</b> " + convenioCobrancaArquivo2.getCodigo());

                return title.toString();
            }
            return "Não foi possível obter detalhes do certificado";
        } catch (Exception e) {
            return "Não foi possível obter detalhes do certificado";
        }
    }

    public String getTitleZeroDollar() throws Exception {
        StringBuilder title = new StringBuilder();
        title.append("Caso você esteja tendo problemas com as verificações de cartões, sugerimos habilitar essa configuração. <br>");
        title.append("O diferencial desse recurso, quando habilitado, é que não há cobrança para o titular do cartão, ou seja, o cartão é validado sem que o portador seja sensibilizado com algum valor na fatura. <br>");
        title.append("<b>Obs: Este recurso precisa ser liberado pela Stone, portanto o proprietário da conta Stone deverá entrar em contato com a Stone e solicitar a liberação do Zero Dollar Auth.</b><br>");
        title.append("<b>Só habilite essa configuração caso a Stone já tenha liberado o recurso na sua conta.<b>");
        return title.toString();
    }

    public String getTitleTipoParcelamentoStone() throws Exception {
        StringBuilder title = new StringBuilder();
        title.append("Na Stone existem dois tipos de parcelamentos e ambos possuem juros. A diferença é de quem os juros serão cobrados: <br>");
        title.append("<b>a) do lojista:</b> Nesta modalidade os juros serão cobramos da loja e o valor repassado ao portador do cliente é <b> 'sem juros'.</b><br>");
        title.append("<b>b) do portador do cartão:</b> Aqui os juros não são pagos pelo lojista e são repassados diretamente ao portador do cartão <b>'com juros'.</b></br>");
        title.append("Dentro do nosso sistema, a transação parcelada ficará um valor menor ao parcelado real cobrado no cartão do cliente pois quem controla os juros neste caso é a Stone.");

        return title.toString();
    }

    public String getSenhaArquivo() {
        return senhaArquivo;
    }

    public void setSenhaArquivo(String senhaArquivo) {
        this.senhaArquivo = senhaArquivo;
    }

    public boolean isExibirCampoSenhaArquivo() {
        return exibirCampoSenhaArquivo;
    }

    public void setExibirCampoSenhaArquivo(boolean exibirCampoSenhaArquivo) {
        this.exibirCampoSenhaArquivo = exibirCampoSenhaArquivo;
    }


    public String getTipoConsultaPix() {
        if (tipoConsultaPix == null){
            return "";
        }
        return tipoConsultaPix;
    }

    public void setTipoConsultaPix(String tipoConsultaPix) {
        this.tipoConsultaPix = tipoConsultaPix;
    }

    public String getInfoPixPesquisar() {
        if (infoPixPesquisar == null) {
            return "";
        }
        return infoPixPesquisar;
    }

    public void setInfoPixPesquisar(String infoPixPesquisar) {
        this.infoPixPesquisar = infoPixPesquisar;
    }

    public JSONObject getJsonConsultaPixResposta() {
        if (jsonConsultaPixResposta == null) {
            return new JSONObject();
        }
        return jsonConsultaPixResposta;
    }

    public void setJsonConsultaPixResposta(JSONObject jsonConsultaPixResposta) {
        this.jsonConsultaPixResposta = jsonConsultaPixResposta;
    }

    public String getJsonWebhookPixAtivadoResposta() {
        if (jsonWebhookPixAtivadoResposta == null) {
            return "{}";
        }
        try {
            // Verifica se é JSONObject
            if (jsonWebhookPixAtivadoResposta instanceof JSONObject) {
                JSONObject obj = (JSONObject) jsonWebhookPixAtivadoResposta;
                if (obj.length() == 0) {
                    return "{}";
                }
                return obj.toString(4);
            }
            return "{}";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "{}";
        }
    }

    public void setJsonWebhookPixAtivadoResposta(Object jsonWebhookPixAtivadoResposta) {
        this.jsonWebhookPixAtivadoResposta = jsonWebhookPixAtivadoResposta;
    }

    public boolean isPossuiRespostaConsultaPix() {
        return possuiRespostaConsultaPix;
    }

    public void setPossuiRespostaConsultaPix(boolean possuiRespostaConsultaPix) {
        this.possuiRespostaConsultaPix = possuiRespostaConsultaPix;
    }

    private void incluirConvenioCobrancaArquivo1() throws Exception {
        try {
            convenioCobrancaArquivo1.setConvenioCobranca(getConvenioCobrancaVO().getCodigo());
            convenioCobrancaArquivo1.setUsuario(getUsuarioLogado().getCodigo());
            convenioCobrancaArquivo1.setConvenioCobrancaVO(getConvenioCobrancaVO());
            if (getConvenioCobrancaVO().isPixBradesco()) {
                convenioCobrancaArquivo1.setSenha(getSenhaArquivo());
            }
            getFacade().getConvenioCobrancaArquivo().incluir(convenioCobrancaArquivo1);
        } catch (Exception e) {
            limparCamposUploadArquivo1();
            throw e;
        }
    }

    private void incluirConvenioCobrancaArquivo2() throws Exception {
        try {
            convenioCobrancaArquivo2.setConvenioCobranca(getConvenioCobrancaVO().getCodigo());
            convenioCobrancaArquivo2.setUsuario(getUsuarioLogado().getCodigo());
            convenioCobrancaArquivo2.setConvenioCobrancaVO(getConvenioCobrancaVO());
            getFacade().getConvenioCobrancaArquivo().incluir(convenioCobrancaArquivo2);
        } catch (Exception e) {
            limparCamposUploadArquivo2();
            throw e;
        }
    }

    public String getTitleUsaSplitPagamentoStoneV5() {
        try {
            return "Se você já é um cliente Stone há um tempo, pode ser que precise gerar novas credenciais para uso com split.</br>" +
                    "Isso só é necessário quando suas credenciais atuais são do modelo Gateway. Se suas credenciais já são do modelo PSP, não será necessário gerar novas e você já consegue usar o split.</br>" +
                    "<b>Atenção: Se for necessário gerar novas credenciais, isso implica em ter que deixar de usar o portal da Stone e usar o portal da pagar.me para acompanhar as novas transações com split.</b></br>" +
                    "<b>Se tiver dúvidas se as suas credenciais atuais permitem ou não transacionar com split, entre em contato com a Pacto.</b>";
        } catch (Exception ignore) {
        }
        return "";
    }

    public String getValueTipoCredencialStoneV5() {
        try {
            if (getConvenioCobrancaVO().isUsaSplitPagamentoStoneV5()) {
                return "PSP";
            } else {
                return "PSP ou Gateway";
            }
        } catch (Exception ignore) {
        }
        return "Nenhum";
    }

    public String getTitleTipoCredencialStoneV5() {
        try {
            return "Informe o tipo da sua credencial Stone. Se você não souber o tipo da sua credencial, entre em contato com a Pacto.</br>" +
                    "<b>Atenção:</b></br>" +
                    "Uma credencial do tipo <b>'PSP' funciona com ou sem split de pagamentos.</b></br>" +
                    "Uma credencial do tipo <b>'Gateway' só funciona sem split de pagamentos.</b>";
        } catch (Exception ignore) {
        }
        return "";
    }

    public String getTitleRecebedorPrincipalSplit() {
        StringBuilder sb = new StringBuilder();
        sb.append("O recebedor principal é o responsável por:</br></br>");
        sb.append("1- Responsável principal da transação em caso de chargeback</br>");
        sb.append("2- Responsável pelas taxas da transação</br>");
        sb.append("3- Responsável por receber o restante dos recebíveis após uma divisão (quando houver)</br>");
        return sb.toString();
    }

    public String getTitleAmbiente() {
        return "Informe o ambiente desejado em que as cobranças deverão ser realizadas.";
    }

    public String getTitleExpiracaoCertificado() {
        return "O certificado só é válido até essa data. Você precisa renová-lo antes do vencimento, caso contrário </br> a integração para de funcionar quando o certificado vencer.";
    }

    public String getTitleWarningAmbiente() {
        if (getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
            return "Para convênio \"ONE PAYMENT\" não é possível editar o ambiente, pois a url é a mesma para Produção e Sandbox. " +
                    "<br/>O que vai diferir se é sandbox ou produção é do lado de lá, de acordo com o \"Private Secret Key\" informado aqui no convênio";
        } else {
            return "AMBIENTE SANDBOX - Este ícone aparecerá em todas as cobranças que forem realizadas utilizando Sandbox. Você poderá vê-lo no detalhe de cada transação.";
        }
    }

    public String getTitleNotificoesAsaas() {
            return "<b>*Atenção*</b> Este recurso é cobrado pelo Asaas.</br>" +
                    "Para verificar o valor, consulte as suas taxas Asaas através do botão \"Taxas\".</br>" +
                    "Ao habilitar, será cobrado uma taxa pela Asaas pelo envio de notificações da cobrança via Email/SMS para cada aluno.</br>" +
                    "Se você deixar essa configuração desmarcada, o Asaas irá descontar apenas o valor da cobrança, sem nenhuma taxa extra.</br>" +
                    "Se marcar/desmarcar a configuração agora, só irá valer para novos cadastros.";
    }

    public String getTitleDesativarBoletoHibridoAsaas() {
        return "Não é possível desativar o boleto híbrido aqui pelo sistema Pacto.</br>" +
                "Se houver qualquer chave pix cadastrada lá no portal, automaticamente qualquer boleto já é gerado com QRCode.</br>" +
                "<b>Para desativar</b>, acesse o portal Asaas, depois vá em: <b>Pix --> Minhas Chaves</b> e remova <b>todas</b> as chaves pix existentes lá.</br>";
    }

    public List getListaAdquirenteMaxiPago() {
        return AdquirenteMaxiPagoEnum.getSelectListAdquirenteMaxiPago();
    }

    public List<SelectItem> getListaTipoConsultaPix() {
        List<SelectItem> tipoConsultapix = new ArrayList<SelectItem>();
        tipoConsultapix.add(new SelectItem("txId"));
        //pix santander não permite buscar pelo e2eId, somente por txId
        if (!getConvenioCobrancaVO().isPixSantander()) {
            tipoConsultapix.add(new SelectItem("e2eid (EndToEndId)"));
        }
        return tipoConsultapix;
    }

    public void consultarPix() throws Exception {
        try {
            limparMsg();
            setOnComplete("");
            setPossuiRespostaConsultaPix(false);
            setJsonConsultaPixResposta(null);
            if (UteisValidacao.emptyString(getTipoConsultaPix())) {
                throw new Exception("Marque a opção desejada em \"consultar por\" ");
            }
            if (UteisValidacao.emptyString(getInfoPixPesquisar()) && getTipoConsultaPix().equals("txId")) {
                throw new Exception("Informe o txId a ser pesquisado!");
            }
            if (UteisValidacao.emptyString(getInfoPixPesquisar()) && getTipoConsultaPix().contains("e2eid")) {
                throw new Exception("Informe o e2eid a ser pesquisado!");
            }
            PixRequisicaoDto pixRequisicaoDto;
            ConvenioCobrancaVO convenioCobrancaVOCompleto = getFacade().getConvenioCobranca().consultarPorChavePrimaria(getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pixRequisicaoDto = getFacade().getPixService().consultarCobranca(getInfoPixPesquisar(), convenioCobrancaVOCompleto, getTipoConsultaPix());
            if (pixRequisicaoDto.getResposta().contains("erros") || pixRequisicaoDto.getResposta().contains("violacoes")) {
                throw new Exception(pixRequisicaoDto.getResposta());
            }
            JSONObject json = new JSONObject(pixRequisicaoDto.getResposta());
            setJsonConsultaPixResposta(json);
            setPossuiRespostaConsultaPix(true);
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Consulta realizada com sucesso! Verifique o retorno.");
            setOnComplete("Richfaces.showModalPanel('modalResultadoPix');");
        } catch (Exception e) {
            setMensagem("");
            setPossuiRespostaConsultaPix(false);
            setJsonConsultaPixResposta(null);
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
        }
    }

    public void iniciarAbaPesquisarPix() {
        limparMsg();
        //valor padrão ao abrir a aba
        setTipoConsultaPix("txId");
        setInfoPixPesquisar("");
        setJsonConsultaPixResposta(null);
        setPossuiRespostaConsultaPix(false);
    }

    public void iniciarAbaWebhookPix() {
        setAbrirFecharModalWebhookAtivado("Richfaces.hideModalPanel('modalWebhookAtivado');");
        setAbrirFecharModalWebhookAtivado("Richfaces.hideModalPanel('logPixWebhook');");
        setPossuiWebhookPixAtivado(false);
        setJsonWebhookPixAtivadoResposta(new JSONObject());
        PixWebhookService pixWebhookService;
        try {
            pixWebhookService = new PixWebhookService(Conexao.getFromSession());
            JSONObject json = pixWebhookService.consultarWebhookAtivo(getConvenioCobrancaVO());
            if (json == null) {
                throw new Exception("Não foi possível consultar o webhook do pix!");
            }
            if (json.has("chave")) {
                setPossuiWebhookPixAtivado(true);
                setJsonWebhookPixAtivadoResposta(json);
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            pixWebhookService = null;
        }
    }

    public void iniciarAbaWebhookBoleto() {
        limparMsg();
        setAbrirFecharModalWebhookAtivado("Richfaces.hideModalPanel('modalWebhookAtivado');");
        setPossuiWebhookPixAtivado(false);
        setJsonWebhookPixAtivadoResposta(new JSONObject());
        ItauService itauService;
        try {
            itauService = new ItauService(Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getConvenioCobrancaVO().getCodigo());
            JSONObject retorno = itauService.consultarRetornoCallBackInserido();

            if (retorno.length() == 0) {
                setPossuiWebhookPixAtivado(false);
                setJsonWebhookPixAtivadoResposta(new JSONObject());
            } else {
                setPossuiWebhookPixAtivado(true);
                setJsonWebhookPixAtivadoResposta(retorno);
            }

        } catch (Exception ex) {
            setPossuiWebhookPixAtivado(false);
            montarErro(ex.getMessage());
        } finally {
            itauService = null;
        }
    }

    public void configurarUrlCallbackBoleto() {
        limparMsg();
        ItauService itauService;
        try {
            if (UteisValidacao.emptyNumber(getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Primeiro grave as configurações do convenio de cobrança");
            }
            if (UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao02())) {
                throw new Exception("Informe o Client ID do convenio de cobrança");
            }
            if (UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao03())) {
                throw new Exception("Informe o Client Secret do convenio de cobrança");
            }
            itauService = new ItauService(Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getConvenioCobrancaVO().getCodigo());
            String retorno = itauService.montarUrlCallBackEnviarBanco();
            if (!UteisValidacao.emptyString(retorno) && new JSONObject(retorno).getInt("statusCode") == 201) {
                setPossuiWebhookPixAtivado(true);
                setJsonWebhookPixAtivadoResposta(new JSONObject(retorno));
                montarSucessoGrowl("Webhook configurado com sucesso!");
            } else {
                montarErro("Não foi possível configurar o Webhook");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            itauService = null;
        }
    }

    public void excluirUrlCallbackBoleto() {
        limparMsg();
        ItauService itauService;
        try {
            itauService = new ItauService(Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getConvenioCobrancaVO().getCodigo());

            JSONObject retornoConsulta = itauService.consultarRetornoCallBackInserido();
            if (retornoConsulta.length() == 0) {
                throw new Exception("Nenhum webhook encontrado para excluir.");
            }

            List<String> idsNotificacao = new ArrayList<>();
            if (retornoConsulta.has("webhooks")) {
                JSONArray webhooksArray = retornoConsulta.getJSONArray("webhooks");
                for (int i = 0; i < webhooksArray.length(); i++) {
                    JSONObject item = webhooksArray.getJSONObject(i);
                    if (item.has("id_notificacao_boleto")) {
                        idsNotificacao.add(item.getString("id_notificacao_boleto"));
                    }
                }
            }

            if (idsNotificacao.isEmpty()) {
                throw new Exception("Nenhum ID de notificação encontrado para excluir.");
            }

            boolean sucesso = itauService.excluirUrlCallback(idsNotificacao);
            if (sucesso) {
                setPossuiWebhookPixAtivado(false);
                setJsonWebhookPixAtivadoResposta(new JSONObject());
                montarSucessoGrowl("Webhook excluído com sucesso!");
            } else {
                montarErro("Não foi possível excluir o Webhook.");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            itauService = null;
        }
    }

    public void configurarUrlCallbackPix() {
        limparMsg();
        PixWebhookService pixWebhookService;
        try {
            pixWebhookService = new PixWebhookService(Conexao.getFromSession());
            boolean sucesso = pixWebhookService.configurarUrlCallback(getConvenioCobrancaVO(), getKey());
            if (sucesso) {
                try {
                    pixWebhookService = new PixWebhookService(Conexao.getFromSession());
                    JSONObject json = pixWebhookService.consultarWebhookAtivo(getConvenioCobrancaVO());
                    if (json.has("chave")) {
                        setPossuiWebhookPixAtivado(true);
                        setJsonWebhookPixAtivadoResposta(json);
                    }
                } catch (Exception ex) {
                    montarErro(ex.getMessage());
                }
                montarSucessoGrowl("Webhook configurado com sucesso!");

                try {
                    registrarLogInclusaoWebhookPix();
                } catch (Exception ignore){}

            } else {
                montarErro("Não foi possível configurar o Webhook.");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            pixWebhookService = null;
        }
    }

    private void registrarLogInclusaoWebhookPix() throws Exception {
            LogVO obj = new LogVO();
            obj.setNomeEntidade("PIXWEBHOOK");
            obj.setNomeEntidadeDescricao("PIXWEBHOOK");
            obj.setChavePrimaria(convenioCobrancaVO.getCodigo().toString());
            obj.setOperacao("INCLUSÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("Campo(s)");
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("Habilitou o webhook do pix");
            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, convenioCobrancaVO.getCodigo());
    }

    public void excluirUrlCallbackPix() {
        limparMsg();
        PixWebhookService pixWebhookService;
        try {
            pixWebhookService = new PixWebhookService(Conexao.getFromSession());
            boolean sucesso = pixWebhookService.excluirUrlCallback(getConvenioCobrancaVO());
            if (sucesso) {
                montarSucessoGrowl("Webhook excluído com sucesso!");
                try {
                    registrarLogExclusaoWebhookPix();
                } catch (Exception ignore){}
                iniciarAbaWebhookPix();
            } else {
                montarErro("Não foi possível excluir o Webhook.");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            pixWebhookService = null;
        }
    }

    private void registrarLogExclusaoWebhookPix() throws Exception {
        LogVO obj = new LogVO();
        obj.setNomeEntidade("PIXWEBHOOK");
        obj.setNomeEntidadeDescricao("PIXWEBHOOK");
        obj.setChavePrimaria(convenioCobrancaVO.getCodigo().toString());
        obj.setOperacao("EXCLUSÃO");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("Campo(s)");
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado("Excluiu o webhook do pix");
        obj.setDataAlteracao(Calendario.hoje());
        registrarLogObjetoVO(obj, convenioCobrancaVO.getCodigo());
    }

    public boolean getApresentarAbaPixWebhook() {
        return getConvenioCobrancaVO() != null &&
                (getConvenioCobrancaVO().isPixSantander() || getConvenioCobrancaVO().isPixBB() ||
                        getConvenioCobrancaVO().isPixBradesco() || getConvenioCobrancaVO().isPixInter() ||
                        getConvenioCobrancaVO().isPixItau());
    }

    public boolean getApresentarAbaSplitDePagamento() {
        return getConvenioCobrancaVO() != null &&
                (getConvenioCobrancaVO().isPagarMe() || getConvenioCobrancaVO().isPagoLivre() ||
                        (getConvenioCobrancaVO().isStoneV5() && getConvenioCobrancaVO().isUsaSplitPagamentoStoneV5()) ||
                        getConvenioCobrancaVO().isFacilitePay());
    }

    public boolean getApresentarAbaWebhookBoleto() {
        return getConvenioCobrancaVO() != null && getConvenioCobrancaVO().isBoletoItauOnline();
    }

    public void abrirModalWebhookAtivado() {
        limparMsg();

        if (getConvenioCobrancaVO().isBoletoItauOnline()) {
            ItauService itauService = null;
            try {
                itauService = new ItauService(Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getConvenioCobrancaVO().getCodigo());
                JSONObject retorno = itauService.consultarRetornoCallBackInserido();
                if (retorno.length() > 0) {
                    setJsonWebhookPixAtivadoResposta(retorno);
                } else {
                    setJsonWebhookPixAtivadoResposta(new JSONObject());
                }
            } catch (Exception ex) {
                montarErro("Erro ao consultar webhook: " + ex.getMessage());
                setJsonWebhookPixAtivadoResposta(new JSONObject());
            } finally {
                itauService = null;
            }
        }

        setAbrirFecharModalWebhookAtivado("Richfaces.showModalPanel('modalWebhookAtivado');");
    }

    public void consultarPixWebhook() {
        limparMsg();
        setPixWebhookString("");
        PixWebhookService pixWebhookService;
        try {
            pixWebhookService = new PixWebhookService(Conexao.getFromSession());
            String result = pixWebhookService.consultarPixWebhook(getConvenioCobrancaVO());
            if (UteisValidacao.emptyString(result)) {
                throw new Exception("Não encontrei nenhum registro no banco de dados para exibir");
            }
            setPixWebhookString(result);
            setAbrirFecharModalLogPixWebhook("Richfaces.showModalPanel('logPixWebhook');");
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            pixWebhookService = null;
        }
    }


    public String getTitleNumContrato() {
        try {
            //Boleto Sicredi
            if (getConvenioCobrancaVO() != null && getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                if (getConvenioCobrancaVO().getBanco().getCodigoBanco() != null && getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(748)) {
                    StringBuilder title = new StringBuilder();
                    title.append("Para boletos SICREDI você deve informar o número da conta corrente sem o hífen e sem o dígito.");
                    title.append("<br/>Ex: Conta corrente: 12345-6, então deverá colocar apenas 12345");
                    return title.toString();
                }
            }
        } catch (Exception e) {
        }
        try {
            //Boleto Banco Brasil Online
            if (getConvenioCobrancaVO() != null && getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
                return "Dado obrigatório e fornecido pelo banco.";
            }
        } catch (Exception e) {
        }
        return "Informe este campo caso o cliente for utilizar extrato (conciliação). Caso o cliente não for utilizar, pode deixar em branco.";
    }

    public String getTitleExtensaoRemessa() {
        try {
            //Boleto Sicredi
            if (getConvenioCobrancaVO() != null && getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                if (getConvenioCobrancaVO().getBanco().getCodigoBanco() != null && getConvenioCobrancaVO().getBanco().getCodigoBanco().equals(748)) {
                    return "Para boletos SICREDI a extensão do arquivo de remessa a ser utilizada é .CRM";
                }
            }
        } catch (Exception e) {
        }
        return "Informe a extensão desejada que será gerado os arquivos de remessa";
    }

    public String getTitleTaxaNotificacoesAsaas() {
        return "O valor é cobrado uma vez por aluno. Para desativar a cobrança dessa taxa para cada aluno </br> é necessário que a configuração \"Enviar Notificações Email/SMS para o cliente\" esteja desmarcada.";
    }

    public boolean isApresentarCampoTipoParcelamentoStone() {
        return this.getConvenioCobrancaVO().isStone() ||
                this.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT);
    }

    public boolean isApresentarCampoDescricao() {
        return !this.getConvenioCobrancaVO().isTipoNenhum();
    }

    public SituacaoCadastralContaAsaasDTO getSituacaoCadastralContaAsaasDTO() {
        return situacaoCadastralContaAsaasDTO;
    }

    public void setSituacaoCadastralContaAsaasDTO(SituacaoCadastralContaAsaasDTO situacaoCadastralContaAsaasDTO) {
        this.situacaoCadastralContaAsaasDTO = situacaoCadastralContaAsaasDTO;
    }

    public DadosComerciaisContaAsaasDTO getDadosComerciaisContaAsaasDTO() {
        return dadosComerciaisContaAsaasDTO;
    }

    public void setDadosComerciaisContaAsaasDTO(DadosComerciaisContaAsaasDTO dadosComerciaisContaAsaasDTO) {
        this.dadosComerciaisContaAsaasDTO = dadosComerciaisContaAsaasDTO;
    }

    public TaxasContaAsaasDTO getTaxasContaAsaasDTO() {
        return taxasContaAsaasDTO;
    }

    public void setTaxasContaAsaasDTO(TaxasContaAsaasDTO taxasContaAsaasDTO) {
        this.taxasContaAsaasDTO = taxasContaAsaasDTO;
    }

    public void gerarConvenioCieloSandbox() {
        getConvenioCobrancaVO().setTipo(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE);
        getConvenioCobrancaVO().setAmbiente(AmbienteEnum.HOMOLOGACAO);
        getConvenioCobrancaVO().setDescricao("CIELO ONLINE SANDBOX");
        getConvenioCobrancaVO().setCodigoAutenticacao01("11839d94-fd0f-44e8-aae4-bad1d32f5904");
        getConvenioCobrancaVO().setCodigoAutenticacao02("QDKHZKNYYHYPQDIHALSUFTPBVWXRHOABYBOINPFF");
        setGerouConvenioCieloSandbox(true);
    }

    public boolean isAmbienteDesenvolvimento() {
        if (!isHttps()) {
            //ambiente de desenvolvimento (localhost ou swarm)
            return true;
        }
        return false;
    }

    public boolean isGerouConvenioCieloSandbox() {
        return gerouConvenioCieloSandbox;
    }

    public void setGerouConvenioCieloSandbox(boolean gerouConvenioCieloSandbox) {
        this.gerouConvenioCieloSandbox = gerouConvenioCieloSandbox;
    }

    public boolean isGerarBoletoHibrido() {
        return gerarBoletoHibrido;
    }

    public void setGerarBoletoHibrido(boolean gerarBoletoHibrido) {
        this.gerarBoletoHibrido = gerarBoletoHibrido;
    }

    public boolean getExibirAbaConciliacao() throws Exception {
        if(this.convenioCobrancaVO != null && this.convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)){
            return true;
        } else if(this.convenioCobrancaVO != null && !this.convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) && getUsuarioLogado().getUsuarioPactoSolucoes()){
            return true;
        }
        return false;
    }

    public boolean isPossuiWebhookPixAtivado() {
        return possuiWebhookPixAtivado;
    }

    public void setPossuiWebhookPixAtivado(boolean possuiWebhookPixAtivado) {
        this.possuiWebhookPixAtivado = possuiWebhookPixAtivado;
    }

    public String getPixWebhookString() {
        return pixWebhookString;
    }

    public void setPixWebhookString(String pixWebhookString) {
        this.pixWebhookString = pixWebhookString;
    }

    public String getAbrirFecharModalWebhookAtivado() {
        return abrirFecharModalWebhookAtivado;
    }

    public void setAbrirFecharModalWebhookAtivado(String abrirFecharModalWebhookAtivado) {
        this.abrirFecharModalWebhookAtivado = abrirFecharModalWebhookAtivado;
    }

    public String getAbrirFecharModalLogPixWebhook() {
        return abrirFecharModalLogPixWebhook;
    }

    public void setAbrirFecharModalLogPixWebhook(String abrirFecharModalLogPixWebhook) {
        this.abrirFecharModalLogPixWebhook = abrirFecharModalLogPixWebhook;
    }
    public String getAbrirFecharModalTokenOperacao() {
        if (UteisValidacao.emptyString(abrirFecharModalTokenOperacao)) {
            return "";
        }
        return abrirFecharModalTokenOperacao;
    }

    public void setAbrirFecharModalTokenOperacao(String abrirFecharModalTokenOperacao) {
        this.abrirFecharModalTokenOperacao = abrirFecharModalTokenOperacao;
    }

    public String getLabelsCamposExibirNoModalTokenOperacao() {
        if (!UteisValidacao.emptyString(labelsCamposExibirNoModalTokenOperacao)) {
            return labelsCamposExibirNoModalTokenOperacao;
        }
        return "";
    }

    public void setLabelsCamposExibirNoModalTokenOperacao(String labelsCamposExibirNoModalTokenOperacao) {
        this.labelsCamposExibirNoModalTokenOperacao = labelsCamposExibirNoModalTokenOperacao;
    }

    public int getQtdCamposSensiveisAlterados() {
        return qtdCamposSensiveisAlterados;
    }

    public void setQtdCamposSensiveisAlterados(int qtdCamposSensiveisAlterados) {
        this.qtdCamposSensiveisAlterados = qtdCamposSensiveisAlterados;
    }

    public PixWebhookVO getPixWebhookVO() {
        if (pixWebhookVO == null) {
            return new PixWebhookVO();
        }
        return pixWebhookVO;
    }

    public void setPixWebhookVO(PixWebhookVO pixWebhookVO) {
        this.pixWebhookVO = pixWebhookVO;
    }

    public List<ConvenioCobrancaArquivoVO> getListaConvenioCobrancaArquivoVO() {
        if (UteisValidacao.emptyList(listaConvenioCobrancaArquivoVO)) {
            return new ArrayList<>();
        }
        return listaConvenioCobrancaArquivoVO;
    }

    public void setListaConvenioCobrancaArquivoVO(List<ConvenioCobrancaArquivoVO> listaConvenioCobrancaArquivoVO) {
        this.listaConvenioCobrancaArquivoVO = listaConvenioCobrancaArquivoVO;
    }

    public ConvenioCobrancaArquivoVO getConvenioCobrancaArquivo1() {
        return convenioCobrancaArquivo1;
    }

    public void setConvenioCobrancaArquivo1(ConvenioCobrancaArquivoVO convenioCobrancaArquivo1) {
        this.convenioCobrancaArquivo1 = convenioCobrancaArquivo1;
    }

    public ConvenioCobrancaArquivoVO getConvenioCobrancaArquivo2() {
        return convenioCobrancaArquivo2;
    }

    public void setConvenioCobrancaArquivo2(ConvenioCobrancaArquivoVO convenioCobrancaArquivo2) {
        this.convenioCobrancaArquivo2 = convenioCobrancaArquivo2;
    }

    public boolean isPossuiArquivo1() {
        return possuiArquivo1;
    }

    public void setPossuiArquivo1(boolean possuiArquivo1) {
        this.possuiArquivo1 = possuiArquivo1;
    }

    public boolean isPossuiArquivo2() {
        return possuiArquivo2;
    }

    public void setPossuiArquivo2(boolean possuiArquivo2) {
        this.possuiArquivo2 = possuiArquivo2;
    }

    public String getNomeArquivo1() {
        return nomeArquivo1;
    }

    public void setNomeArquivo1(String nomeArquivo1) {
        this.nomeArquivo1 = nomeArquivo1;
    }

    public String getNomeArquivo2() {
        return nomeArquivo2;
    }

    public void setNomeArquivo2(String nomeArquivo2) {
        this.nomeArquivo2 = nomeArquivo2;
    }

    public boolean isFezUploadArquivo1() {
        return fezUploadArquivo1;
    }

    public void setFezUploadArquivo1(boolean fezUploadArquivo1) {
        this.fezUploadArquivo1 = fezUploadArquivo1;
    }

    public boolean isFezUploadArquivo2() {
        return fezUploadArquivo2;
    }

    public void setFezUploadArquivo2(boolean fezUploadArquivo2) {
        this.fezUploadArquivo2 = fezUploadArquivo2;
    }

    public static Map<String, RedeEmpresaVO> getRedeEmpresaVosJaConsultados() {
        return redeEmpresaVosJaConsultados;
    }

    public static void setRedeEmpresaVosJaConsultados(Map<String, RedeEmpresaVO> redeEmpresaVosJaConsultados) {
        ConvenioCobrancaControle.redeEmpresaVosJaConsultados = redeEmpresaVosJaConsultados;
    }

    public void testarConciliacaoPagoLivre() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCodigoAutenticacao04())) {
                throw new Exception("Informe o token de conciliação.");
            }

            String retVenda = "";
            Set<String> cliente = new HashSet<>();
            try {
                String retorno = PagoLivreConciliacaoService.executarRequest(this.convenioCobrancaVO, Calendario.somarDias(Calendario.hoje(), -1), TipoConciliacaoEnum.VENDAS);
                try {
                    JSONArray array = new JSONArray(retorno);
                    for (int i = 0; i < array.length(); i++) {
                        JSONObject item = array.getJSONObject(i);
                        JSONObject merchant = item.optJSONObject("merchant");
                        String username = merchant != null ? merchant.optString("name") : "";
                        if (!UteisValidacao.emptyString(username)) {
                            cliente.add(username);
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } catch (Exception ex) {
                retVenda = ex.getMessage();
            }

            String retPagamento = "";
            try {
                String retorno = PagoLivreConciliacaoService.executarRequest(this.convenioCobrancaVO, Calendario.somarDias(Calendario.hoje(), -1), TipoConciliacaoEnum.PAGAMENTOS);
                try {
                    JSONArray array = new JSONArray(retorno);
                    for (int i = 0; i < array.length(); i++) {
                        JSONObject item = array.getJSONObject(i);
                        String username = item.optString("userName");
                        if (!UteisValidacao.emptyString(username)) {
                            cliente.add(username);
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } catch (Exception ex) {
                retPagamento = ex.getMessage();
            }

            if (!UteisValidacao.emptyString(retVenda) ||
                    !UteisValidacao.emptyString(retPagamento)) {
                throw new Exception(retVenda + " " + retPagamento);
            }

            StringBuilder clienteAp = new StringBuilder();
            for (String cl : cliente) {
                clienteAp.append(",").append(cl);
            }
            montarSucessoGrowl("Token correto. " +
                    (clienteAp.length() > 0 ? ("Cliente: " + clienteAp.toString().replaceFirst(",", "")) : ""));
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isExibirCampoInputTextArea() {
        return getConvenioCobrancaVO().isBoletoAsaas() || getConvenioCobrancaVO().isPixAsaas();
    }

    public void abrirLinkAutorizacaoContaPagBank() {
        try {
            if (getConvenioCobrancaVO().getAmbiente().equals(AmbienteEnum.NENHUM)) {
                throw new Exception("Informe o ambiente do convênio antes de realizar a autorização.");
            }
            setMsgAlert("");
            String url = "";
            if (getConvenioCobrancaVO().getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                url = PropsService.getPropertyValue(PropsService.urlApiConnectPagBankProducao);
                url += "oauth2/authorize?client_id=" + PropsService.getPropertyValue(PropsService.clientIdAplicacaoPactoPagbankProducao);
            } else {
                url = PropsService.getPropertyValue(PropsService.urlApiConnectPagBankSandbox);
                url += "oauth2/authorize?client_id=" + PropsService.getPropertyValue(PropsService.clientIdAplicacaoPactoPagbankSandbox);
            }
            url += "&response_type=code";
            url += "&redirect_uri=" + PropsService.getPropertyValue(PropsService.redirectUriConnectPagBank);
            url += "&scope=" + PropsService.getPropertyValue(PropsService.scopeUtilizarAutorizarConnectPagbank);
            url += "&op=Auth";
            String state = getKey() + "/" + getConvenioCobrancaVO().getCodigo();
            url += "&state=" + state;
            Uteis.logarDebug("**AUTORIZAÇÃO PAGBANX X PACTO --> GEROU LINK PARA AUTORIZAÇÃO** | Chave: " + getKey() + " | URL: " + url);

            String openWindow = "window.open('" + url + "')";
            setMsgAlert(openWindow);
        } catch (Exception exception) {
            montarErro(exception.getMessage());
        }
    }

    public String getNomeBtnGerarAutorizacaoPagbank() {
        if (UteisValidacao.emptyString(getConvenioCobrancaVO().getCodigoAutenticacao01())) {
            return "Gerar Autorização PagBank X Pacto";
        }
        return "Renovar Autorização PagBank X Pacto";
    }

    public boolean getExibirCampoExpiracaoAccessToken() {
        return getConvenioCobrancaVO() != null && getConvenioCobrancaVO().getDataGeracaoAccessToken() != null;
    }

    public String getTipoPortalStoneV5() {
        if (getConvenioCobrancaVO().getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.GATEWAY)) {
            return "Portal Stone ou Pagar.me";
        } else if (getConvenioCobrancaVO().getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.PSP)){
            return "Portal Pagar.me";
        } else {
            return "";
        }
    }

    public String getStyleCSSFieldCodigoAutenticacao01() {
        if (getConvenioCobrancaVO() != null) {
            if (getConvenioCobrancaVO().isBoletoAsaas() || getConvenioCobrancaVO().isPixAsaas()) {
                return "width: 503px;height: 50px";
            }
        }
        return "width: 329px;"; //tamanho padrão do campo codigoautenticacao01
    }
}
