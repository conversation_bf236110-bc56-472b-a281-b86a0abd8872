<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ConvenioCobranca_tituloForm}"/>
    </title>

    <style>
        .subordinado {
            padding: 5px !important;
        }
    </style>

<%--    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConvenioCobranca_tituloForm}"/>--%>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ConvenioCobrancaControle.liberarBackingBeanMemoria}"
                           id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel id="tabConvenioCobranca" width="100%" activeTabClass="true" headerAlignment="rigth" switchType="ajax">
                    <rich:tab label="Convênio Cobrança">

                        <h:panelGroup layout="block"
                                      rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.novoObj && ConvenioCobrancaControle.usuarioPacto && !ConvenioCobrancaControle.gerouConvenioCieloSandbox}"
                                      style="padding: 3px; margin-left: 35%;">
                            <a4j:commandButton id="gerarConvenioCieloSandbox"
                                             action="#{ConvenioCobrancaControle.gerarConvenioCieloSandbox}"
                                             reRender="form"
                                             title="Clique para gerar um convênio Cielo Sandbox pré-configurado e depois clique em gravar. </br> Esse botão só aparece para usuário Pactobr e Admin"
                                             value="Gerar Convênio Cielo Sandbox" styleClass="botoes tooltipster"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="text-align: center; padding: 10px"
                                      rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.bloquearCobrancaAutomatica and ConvenioCobrancaControle.convenioCobrancaVO.tipo.transacaoOnline}">
                            <h:outputText style="color: #FF5555; font-size: 20px; font-weight: bold;"
                                          value="AS COBRANÇAS AUTOMÁTICAS ESTÃO BLOQUEADAS!"/>
                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.cobrancaCobrancaDCO}">
                                <br/>
                                <h:outputText styleClass="texto-size-18 cinza"
                                              style="font-style: italic"
                                              escape="false"
                                              value="<b>*DCO (Débito Em Conta)</b>, quando você reativá-lo deverá lembrar de criar uma nova remessa<br/>
                                              de repescagem para envio das cobranças passadas, selecionando o período de<br/>vencimento desejado no gestão de remessas no momento da criação."/>
                            </c:if>
                        </h:panelGroup>

                        <h:panelGrid id="gridForm" columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ConvenioCobranca_codigo}"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigo}"/>

                            <c:if test="${not empty ConvenioCobrancaControle.convenioCobrancaVO.tipo.documentacao &&
                            LoginControle.usuarioLogado.usuarioPactoSolucoes && !ConvenioCobrancaControle.convenioCobrancaVO.somenteExtrato}">
                                <h:outputText styleClass="tituloCampos"
                                              value=""/>
                                <h:outputLink id="documentacao"
                                              styleClass="tooltipster"
                                              value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipo.documentacao}"
                                              title="Documentação API"
                                              target="_blank">
                                    <h:outputText styleClass="texto-size-16" value="DOCUMENTAÇÃO API"/>
                                </h:outputLink>
                            </c:if>

                            <c:if test="${not empty ConvenioCobrancaControle.convenioCobrancaVO.tipo.downloadAuxiliar}">
                                <h:outputText styleClass="tituloCampos"
                                              value=""/>
                                <h:outputLink id="downloadAuxiliar"
                                              styleClass="tooltipster"
                                              value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipo.downloadAuxiliar}"
                                              title="Download Arquivos"
                                              target="_blank">
                                    <h:outputText styleClass="texto-size-16" value="DOWNLOAD ARQUIVOS"/>
                                </h:outputLink>
                            </c:if>

                            <c:if test="${not empty ConvenioCobrancaControle.urlWebhook && !ConvenioCobrancaControle.convenioCobrancaVO.somenteExtrato}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="URL Webhook:"
                                              title="#{ConvenioCobrancaControle.urlWebhookTitle}"/>
                                <h:panelGroup>
                                    <a4j:commandLink onclick="copiar('#{ConvenioCobrancaControle.urlWebhook}')">
                                        <h:outputText id="webhook"
                                                      styleClass="tooltipster"
                                                      value="#{ConvenioCobrancaControle.urlWebhook}"
                                                      title="#{ConvenioCobrancaControle.urlWebhookTitle}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </c:if>

                            <!--A situação da Banderira ser um Cadastro, está gerando problemas, pois fica gerando vários cadastros para mesma Bandeira, atrapalhando integrações com outros sistemas.
                            O ideal é ter apenas um para cada Bandeira. Então, geramos no Banco de Importação um cadastro para cada Operadora (Bandeira).
                            Agora, bloqueamos as opções de Cadastrar ou Editar novas.
                            Se tiver algum impacto, precisa resolver via Ajuste BD e arrumar também no banco de Importação para os clientes futuros.-->
<%--                            <c:if test="${ConvenioCobrancaControle.apresentarCriarOperadorasAutomatico && !ConvenioCobrancaControle.convenioCobrancaVO.somenteExtrato}">--%>
<%--                                <h:outputText styleClass="tituloCampos" value=""/>--%>
<%--                                <h:panelGroup>--%>
<%--                                    <a4j:commandLink value="CONFIGURAR OPERADORA DE CARTÃO"--%>
<%--                                                     action="#{ConvenioCobrancaControle.confirmarCriarOperadorasAutomatico}"--%>
<%--                                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"--%>
<%--                                                     reRender="formModalCriarOperadora"--%>
<%--                                                     title="Aqui você pode configurar todas as operadoras aceitas no sistema sem a necessidade de cadastrar uma a uma vinculando a bandeira.--%>
<%--                                                     <br/>Basta configurar por aqui, logo em seguida clique em “Gravar“ na nova janela que será aberta."--%>
<%--                                                     styleClass="texto-size-16 tooltipster"/>--%>
<%--                                </h:panelGroup>--%>
<%--                            </c:if>--%>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.stone || ConvenioCobrancaControle.convenioCobrancaVO.stoneConnect ||
                             (ConvenioCobrancaControle.convenioCobrancaVO.stoneV5 && ConvenioCobrancaControle.convenioCobrancaVO.tipoCredencialStoneEnum.codigo == 1)}">
                                <h:outputText styleClass="tituloCampos"
                                              value=""/>
                                <a4j:commandLink id="solicitarConcessaoAcessoStone"
                                                 styleClass="texto-size-16 tooltipster"
                                                 value="SOLICITAR CONCILIAÇÃO AUTOMÁTICA STONE"
                                                 title="Você pode solicitar a liberação para utilização de conciliação automática de recebimentos por aqui.<br/>
                                                        Ao prosseguir, a Stone irá conceder acesso automaticamente ao extrato do StoneCode informado aqui neste convênio."
                                                 action="#{ConvenioCobrancaControle.solicitarConcessaoAcessoStone}"
                                                 oncomplete="#{ConvenioCobrancaControle.onComplete};#{ConvenioCobrancaControle.mensagemNotificar}"/>
                                <h:outputText styleClass="tituloCampos"
                                              value=""/>
                                <a4j:commandLink id="verificarConcessaoAcessoStone"
                                                 styleClass="texto-size-16 tooltipster"
                                                 value="VERIFICAR STATUS CONCILIAÇÃO AUTOMÁTICA STONE"
                                                 title="Quando solicita a liberação da conciliação, muitas vezes a Stone retorna que já está liberado quanto na verdade não está. <br/>
                                                        Clicando aqui é realizado uma consulta teste na data de ontem para verificar se realmente está liberado ou não."
                                                 action="#{ConvenioCobrancaControle.verificarStatusConcessaoAcessoStone}"
                                                 oncomplete="#{ConvenioCobrancaControle.onComplete};#{ConvenioCobrancaControle.mensagemNotificar}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.redeOnline}">
                                <h:outputText styleClass="tituloCampos"
                                              value=""/>
                                <a4j:commandLink id="solicitarConcessaoAcessoRedeOnline"
                                                 styleClass="texto-size-16 tooltipster"
                                                 value="SOLICITAR CONCILIAÇÃO AUTOMÁTICA REDE ONLINE"
                                                 title="Você pode solicitar a liberação para utilização de conciliação automática de recebimentos por aqui.<br/>
                                                        Ao prosseguir, a Rede irá conceder acesso automaticamente ao extrato do Ponto de Venda informado aqui neste convênio."
                                                 action="#{ConvenioCobrancaControle.solicitarConcessaoAcessoRedeOnline()}"
                                                 oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"
                                                 reRender="tabMensagensConvenio"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.redeOnline}">
                                <h:outputText styleClass="tituloCampos"
                                              value=""/>
                                <a4j:commandLink id="cancelarConcessaoAcessoRedeOnline"
                                                 styleClass="texto-size-16 tooltipster"
                                                 value="LIMPAR DADOS CONCILIAÇÃO AUTOMÁTICA REDE ONLINE"
                                                 title="Utilize esse recurso para limpar do sistema os dados da Integração.<br/>
                                                        Ele é necessário, caso seja bloqueano no Portal da Rede o acesso da Pacto e precise solicitar novamente."
                                                 action="#{ConvenioCobrancaControle.limparConcessaoAcessoRedeOnline()}"
                                                 oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"
                                                 reRender="tabMensagensConvenio"/>
                            </c:if>

                            <h:outputText styleClass="tituloCampos"
                                          value="Tipo Convênio:"/>
                            <h:outputText styleClass="tituloCampos"
                                          rendered="#{ConvenioCobrancaControle.somenteExibirTipoConvenio}"
                                          value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipo.descricao}"/>
                            <h:panelGroup rendered="#{!ConvenioCobrancaControle.somenteExibirTipoConvenio}">
                                <h:selectOneMenu id="tipoConvenio" styleClass="form" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipo}">
                                    <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemTipoConvenio}"/>
                                    <a4j:support event="onchange" reRender="form"
                                                 oncomplete="#{ConvenioCobrancaControle.onComplete}"
                                                 action="#{ConvenioCobrancaControle.prepararTela}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarAmbiente}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Ambiente:"
                                              title="#{ConvenioCobrancaControle.titleAmbiente}"/>
                                <h:panelGroup>
                                <c:if test="${!LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  title="#{ConvenioCobrancaControle.titleAmbiente}"
                                                  rendered="#{!LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                                  value="#{ConvenioCobrancaControle.convenioCobrancaVO.ambiente.descricao}"/>
                                </c:if>

                                <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:selectOneMenu id="listaSelectItemAmbiente" styleClass="form"
                                                     disabled="#{ConvenioCobrancaControle.convenioCobrancaVO.onePayment}"
                                                     rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.ambiente}">
                                        <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemAmbiente}"/>
                                        <a4j:support event="onchange"
                                                     reRender="form"/>
                                    </h:selectOneMenu>
                                </c:if>
                                    <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.onePayment}">
                                        <h:graphicImage id="iconWarningSandboxOnePayment"
                                                        styleClass="tooltipster"
                                                        title="#{ConvenioCobrancaControle.titleWarningAmbiente}"
                                                        style="inline-size: 20px; margin-left: 5px; vertical-align: -5px;"
                                                        value="images/warning.png"/>
                                    </c:if>
                                    <c:if test="${!ConvenioCobrancaControle.convenioCobrancaVO.onePayment && ConvenioCobrancaControle.convenioCobrancaVO.sandbox}">
                                        <h:graphicImage id="iconWarningSandbox"
                                                        styleClass="tooltipster"
                                                        title="#{ConvenioCobrancaControle.titleWarningAmbiente}"
                                                        style="inline-size: 20px; margin-left: 5px; vertical-align: -5px;"
                                                        value="images/warning.png"/>
                                    </c:if>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.apresentarCampoDescricao}">
                                <h:outputText styleClass="tituloCampos"
                                              value="* Descrição:"/>
                                <h:panelGroup>
                                    <h:inputText id="descricao" size="50" maxlength="255" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.descricao}"/>
                                    <h:message for="descricao" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${!ConvenioCobrancaControle.convenioCobrancaVO.tipoNenhum}">
                                <h:panelGrid columns="2" style="display: inline">
                                <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                                styleClass="tooltipster"
                                                title="Necessário código de autenticação para alterar"/>
                                <h:outputText styleClass="tituloCampos"
                                              value="Situação:"/>
                                </h:panelGrid>
                                <h:panelGroup>
                                    <h:selectOneMenu id="situacaoConvenio" styleClass="form" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.situacao}">
                                        <f:selectItems
                                                value="#{ConvenioCobrancaControle.listaSelectItemSituacaoConvenioCobranca}"/>
                                        <a4j:support event="onchange"
                                                     action="#{ConvenioCobrancaControle.acaoAlterarSituacao}"
                                                     reRender="form"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </c:if>

                            <%--Convênio de cartão online e está inativo--%>
                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoCobranca.id == 2 && ConvenioCobrancaControle.convenioCobrancaVO.situacao.codigo == 0}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Com essa configuração marcada, mesmo com este convênio aqui inativo, ele ficará disponível para filtrar lá nos filtros do PactoPay"
                                              value="Apresentar este convênio inativo nos filtros do PactoPay:"/>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="apresentarInativoPactoPay"
                                                             styleClass="tituloCampos tooltipster"
                                                             title="Com essa configuração marcada, mesmo com este convênio aqui inativo, ele ficará disponível para filtrar lá nos filtros do PactoPay"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarInativoNoPactoPay}">
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>
                            </c:if>

                            <%--Convênio de cartão online e está inativo--%>
                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.stone}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.titleZeroDollar}"
                                              value="Habilitar verificação de cartão Zero Dollar Auth:"/>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="zeroDollarAuth"
                                                             styleClass="tituloCampos tooltipster"
                                                             title="#{ConvenioCobrancaControle.titleZeroDollar}"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.verificacaoZeroDollar}">
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes && ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoCurrency}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Currency:"
                                              title="Informe a moeda em que as cobranças serão realizadas."/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="moedaConvenio" styleClass="form"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.currencyConvenioEnum}">
                                        <f:selectItems
                                                value="#{ConvenioCobrancaControle.listaSelectItemCurrencyConvenio}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${(ConvenioCobrancaControle.convenioCobrancaVO.tipo.pix or ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil)
                                        and !ConvenioCobrancaControle.convenioCobrancaVO.pixPjBank
                                        and !ConvenioCobrancaControle.convenioCobrancaVO.pixAsaas}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="#{ConvenioCobrancaControle.convenioCobrancaVO.valueCampoChavePix}"
                                              title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleApenasNomeBancoPix}"
                                              rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}"/>
                                <h:panelGroup rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}">
                                    <h:inputText id="pixChave" size="50" styleClass="form tooltipster"
                                                 title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleApenasNomeBancoPix}"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.pixChave}"/>
                                    <h:message for="pixChave" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  title="#{ConvenioCobrancaControle.titleExpiracaoPix}"
                                                  value="Tempo de validade do pix, em segundos:"
                                                  rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}"/>
                                    <h:panelGroup rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}">
                                        <h:inputText converter="javax.faces.Long"
                                                     title="#{ConvenioCobrancaControle.titleExpiracaoPix}"
                                                     converterMessage="O tempo de expiração deve ser um número que corresponde aos segundos que o pix ficará ativo para pagamento"
                                                     id="pixExpiracao"
                                                     styleClass="form tooltipster"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.pixExpiracao}"/>
                                        <h:message for="pixExpiracao" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>

                                <%--Nome do campo varia de um banco para o outro--%>
                                <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.pixBB or ConvenioCobrancaControle.convenioCobrancaVO.pixSantander
                                                or ConvenioCobrancaControle.convenioCobrancaVO.pixInter or ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil or ConvenioCobrancaControle.convenioCobrancaVO.pixItau}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Client ID:"
                                                  title="Client ID fornecido pelo Banco"/>
                                </c:if>
                                <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.pixBradesco}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Client key:"
                                                  title="Client key fornecida pelo Banco"/>
                                </c:if>

                                <h:panelGroup>
                                    <h:panelGroup layout="block">
                                        <h:inputText id="pixClientId"
                                                     style="width: 470px;" styleClass="form"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.pixClientId}"/>
                                        <h:message for="pixClientId" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <c:if test="${(ConvenioCobrancaControle.convenioCobrancaVO.tipo.pix and !ConvenioCobrancaControle.convenioCobrancaVO.pixPjBank) or
                                                ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Client secret:"
                                                  title="Client secret fornecido pelo Banco"/>

                                    <h:panelGroup>
                                        <h:panelGroup layout="block">
                                            <h:inputText id="pixClientSecret"
                                                         style="width: 470px;" styleClass="form"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.pixClientSecret}"/>
                                            <h:message for="pixClientSecret" styleClass="mensagemDetalhada"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </c:if>

                                <%--Somente Banco do Brasil é necessário informar o Basic Token no convênio--%>
                                <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.pixBB or ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Client basic auth token:"
                                                  title="Client basic auth token fornecido pelo Banco"/>
                                    <h:panelGroup>
                                        <h:inputTextarea id="pixBasicAuthToken" style="width: 470px;height: 100px" styleClass="form"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.pixBasicAuth}"/>
                                        <h:message for="pixBasicAuthToken" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>


                                <%--PAINEL CERTIFICADO PRIVADO BRADESCO--%>
                                <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.pixBradesco}">
                                    <h:outputText value="Certificado digital A1:<br/>Formato (.P12, .PFX)"
                                                  escape="false"
                                                  styleClass="tituloCampos tooltipster"
                                                  title="#{ConvenioCobrancaControle.titleCampoCertificadoDigitalBradesco}"/>
                                    <c:if test="${ConvenioCobrancaControle.possuiArquivo1}">
                                        <h:panelGroup>
                                            <h:outputText value="#{ConvenioCobrancaControle.nomeArquivo1}"
                                                          styleClass="tooltipster"
                                                          style="font-weight: bold;"
                                                          title="#{ConvenioCobrancaControle.titleArquivo1Existente}"/>
                                            <a4j:commandLink action="#{ConvenioCobrancaControle.excluirArquivo1}"
                                                             style="margin: 10px;"
                                                             oncomplete="#{ConvenioCobrancaControle.msgAlert}"
                                                             onclick="if(!confirm('O certificado será excluído imediatamente. Tem certeza que deseja excluir o certificado?')){return false;};"
                                                             value="Excluir certificado"
                                                             reRender="form"/>
                                        </h:panelGroup>
                                    </c:if>
                                    <c:if test="${!ConvenioCobrancaControle.possuiArquivo1}">
                                        <h:panelGroup style="vertical-align: middle;"
                                                      layout="block" id="panelArquivo">
                                            <rich:fileUpload id="upload"
                                                             rendered="#{!ConvenioCobrancaControle.possuiArquivo1}"
                                                             listHeight="60"
                                                             listWidth="565"
                                                             noDuplicate="false"
                                                             fileUploadListener="#{ConvenioCobrancaControle.uploadArquivo1}"
                                                             maxFilesQuantity="1"
                                                             allowFlash="false"
                                                             immediateUpload="false"
                                                             acceptedTypes="P12, PFX, p12, pfx"
                                                             addControlLabel="Adicionar"
                                                             cancelEntryControlLabel="Cancelar"
                                                             doneLabel="Upload realizado com sucesso! Não esqueça de gravar o convênio para concluir!"
                                                             sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                             progressLabel="Enviando"
                                                             clearControlLabel="Limpar"
                                                             clearAllControlLabel="Limpar todos"
                                                             stopControlLabel="Parar"
                                                             uploadControlLabel="Enviar arquivo"
                                                             transferErrorLabel="Falha de Transmissão"
                                                             stopEntryControlLabel="Parar">
                                                <a4j:support event="onerror"
                                                             oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"/>
                                                <a4j:support event="onclear" reRender="panelArquivo"
                                                             action="#{ConvenioCobrancaControle.limparCamposUploadArquivo1}"/>
                                            </rich:fileUpload>
                                        </h:panelGroup>

                                        <h:outputText styleClass="tituloCampos tooltipster"
                                                      rendered="#{ConvenioCobrancaControle.exibirCampoSenhaArquivo}"
                                                      value="Senha do Certificado:"
                                                      title="#{ConvenioCobrancaControle.titleSenhaCertificadoDigitalBradesco}"/>
                                        <h:panelGroup rendered="#{ConvenioCobrancaControle.exibirCampoSenhaArquivo}">
                                            <h:inputSecret id="senhaArquivo" style="width: 152px;"
                                                         styleClass="form tooltipster"
                                                         title="#{ConvenioCobrancaControle.titleSenhaCertificadoDigitalBradesco}"
                                                         value="#{ConvenioCobrancaControle.senhaArquivo}"/>
                                        </h:panelGroup>
                                    </c:if>

                                    <h:panelGroup rendered="#{ConvenioCobrancaControle.possuiArquivo1}">
                                        <h:outputText value="Vencimento do certificado:"
                                                      escape="false"
                                                      title="O certificado só é válido até essa data. Você precisa renová-lo antes do vencimento lá no portal </br> do banco Inter, caso contrário a integração para de funcioanr quando o certificado vence"
                                                      styleClass="tituloCampos tooltipster"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{ConvenioCobrancaControle.possuiArquivo1}">
                                        <h:outputText value="#{ConvenioCobrancaControle.convenioCobrancaArquivo1.dataExpiracaoCertificado}"
                                                      styleClass="tooltipster"
                                                      style="font-weight: bold;"
                                                      title="#{ConvenioCobrancaControle.titleExpiracaoCertificado}"/>
                                    </h:panelGroup>
                                </c:if>

                                <%--PAINEL CERTIFICADOS INTER--%>
                                <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.pixInter}">

                                    <%--CAMPOS CERTIFICADO PUBLICO CRT--%>
                                    <h:panelGroup style="display: grid;">
                                        <h:outputText rendered="#{!ConvenioCobrancaControle.possuiArquivo1}"
                                                      value="Certificado digital A1 público:<br/>Formato (.crt)"
                                                      escape="false"
                                                      styleClass="tituloCampos tooltipster"/>
                                        <h:outputText rendered="#{ConvenioCobrancaControle.possuiArquivo1}"
                                                      value="Certificado digital:<br/>Formato (.pfx)"
                                                      escape="false"
                                                      styleClass="tituloCampos tooltipster"/>

                                        <h:panelGroup style="margin-top: 6px; margin-left: 4px;">
                                            <h:outputLink styleClass="tooltipster"
                                                          value="#{SuperControle.urlBaseConhecimento}como-ativar-o-convenio-de-cobranca-pix-com-banco-inter/"
                                                          title="Clique e veja onde e como gerar os certificados para utilizar na integração do pix para o banco Inter"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                        <h:panelGroup rendered="#{ConvenioCobrancaControle.possuiArquivo1}">
                                            <h:outputText value="#{ConvenioCobrancaControle.nomeArquivo1}"
                                                          styleClass="tooltipster"
                                                          style="font-weight: bold;"
                                                          title="#{ConvenioCobrancaControle.titleArquivo1Existente}"/>
                                            <a4j:commandLink action="#{ConvenioCobrancaControle.excluirArquivo1}"
                                                             style="margin: 10px;"
                                                             oncomplete="#{ConvenioCobrancaControle.msgAlert}"
                                                             onclick="if(!confirm('O certificado será excluído imediatamente. Tem certeza que deseja excluir o certificado?')){return false;};"
                                                             value="Excluir certificado"
                                                             reRender="form"/>
                                        </h:panelGroup>

                                    <h:panelGroup rendered="#{ConvenioCobrancaControle.possuiArquivo1}">
                                    <h:outputText value="Vencimento do certificado:"
                                                  escape="false"
                                                  title="O certificado só é válido até essa data. Você precisa renová-lo antes do vencimento lá no portal </br> do banco Inter, caso contrário a integração para de funcioanr quando o certificado vence"
                                                  styleClass="tituloCampos tooltipster"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{ConvenioCobrancaControle.possuiArquivo1}">
                                        <h:outputText value="#{ConvenioCobrancaControle.convenioCobrancaArquivo1.dataExpiracaoCertificado}"
                                                      styleClass="tooltipster"
                                                      style="font-weight: bold;"
                                                      title="#{ConvenioCobrancaControle.titleExpiracaoCertificado}"/>
                                    </h:panelGroup>


                                        <h:panelGroup style="vertical-align: middle;"
                                                      layout="block" id="panelArquivo1"
                                                      rendered="#{!ConvenioCobrancaControle.possuiArquivo1}">
                                            <rich:fileUpload id="upload"
                                                             rendered="#{!ConvenioCobrancaControle.possuiArquivo1}"
                                                             listHeight="60"
                                                             listWidth="565"
                                                             noDuplicate="false"
                                                             fileUploadListener="#{ConvenioCobrancaControle.uploadArquivo1}"
                                                             maxFilesQuantity="1"
                                                             allowFlash="false"
                                                             immediateUpload="false"
                                                             acceptedTypes="CRT, crt, pfx"
                                                             addControlLabel="Adicionar"
                                                             cancelEntryControlLabel="Cancelar"
                                                             doneLabel="Upload realizado com sucesso! Não esqueça de gravar o convênio para concluir!"
                                                             sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                             progressLabel="Enviando"
                                                             clearControlLabel="Limpar"
                                                             clearAllControlLabel="Limpar todos"
                                                             stopControlLabel="Parar"
                                                             uploadControlLabel="Enviar arquivo"
                                                             transferErrorLabel="Falha de Transmissão"
                                                             stopEntryControlLabel="Parar">
                                                <a4j:support event="onerror"
                                                             oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"/>
                                                <a4j:support event="onclear" reRender="panelArquivo1"
                                                             action="#{ConvenioCobrancaControle.limparCamposUploadArquivo1}"/>
                                            </rich:fileUpload>
                                        </h:panelGroup>




                                    <%--CAMPOS CERTIFICADO PRIVADO KEY--%>
                                    <h:panelGroup style="display: grid;" rendered="#{!ConvenioCobrancaControle.possuiArquivo1}">
                                        <h:outputText value="Chave Privada:<br/>Formato (.key)"
                                                      escape="false"
                                                      styleClass="tituloCampos tooltipster"/>

                                        <h:panelGroup style="margin-top: 6px; margin-left: 4px;">
                                            <h:outputLink styleClass="tooltipster"
                                                          value="#{SuperControle.urlBaseConhecimento}como-ativar-o-convenio-de-cobranca-pix-com-banco-inter/"
                                                          title="Clique e veja onde e como gerar os certificados para utilizar na integração do pix para o banco Inter"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>


                                        <h:panelGroup style="vertical-align: middle;"
                                                      rendered="#{!ConvenioCobrancaControle.possuiArquivo2 && !ConvenioCobrancaControle.possuiArquivo1}"
                                                      layout="block" id="panelArquivo2">
                                            <rich:fileUpload id="upload2"
                                                             rendered="#{!ConvenioCobrancaControle.possuiArquivo2 && !ConvenioCobrancaControle.possuiArquivo1}"
                                                             listHeight="60"
                                                             listWidth="565"
                                                             noDuplicate="false"
                                                             fileUploadListener="#{ConvenioCobrancaControle.uploadArquivo2}"
                                                             maxFilesQuantity="1"
                                                             allowFlash="false"
                                                             immediateUpload="false"
                                                             acceptedTypes="KEY, key"
                                                             addControlLabel="Adicionar"
                                                             cancelEntryControlLabel="Cancelar"
                                                             doneLabel="Upload realizado com sucesso! Não esqueça de gravar o convênio para concluir!"
                                                             sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                             progressLabel="Enviando"
                                                             clearControlLabel="Limpar"
                                                             clearAllControlLabel="Limpar todos"
                                                             stopControlLabel="Parar"
                                                             uploadControlLabel="Enviar arquivo"
                                                             transferErrorLabel="Falha de Transmissão"
                                                             stopEntryControlLabel="Parar">
                                                <a4j:support event="onerror"
                                                             oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"/>
                                                <a4j:support event="onclear" reRender="panelArquivo2"
                                                             action="#{ConvenioCobrancaControle.limparCamposUploadArquivo2}"/>
                                            </rich:fileUpload>
                                        </h:panelGroup>
                                </c:if>

                            </c:if>

                            <%--PAINEL DCC CAIXA ONLINE--%>
                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.dccCaixaOnline}">

                                <%--CAMPOS CERTIFICADO PUBLICO SSH PEM--%>
                                <h:panelGroup style="display: grid;">
                                    <h:outputText value="Certificado digital SSH PEM público:<br/>Formato (.key.pub)"
                                                  escape="false"
                                                  styleClass="tituloCampos tooltipster"
                                    title="Salva um arquivo por vez. Após Upload do arquivo, precisa clicar no Gravar. </br>
                                            Se fizer o upload dos dois arquivos, ao clicar no Gravar, só vai salvar um."/>

                                </h:panelGroup>

                                <h:panelGroup rendered="#{ConvenioCobrancaControle.possuiArquivo1}">
                                    <h:outputText value="#{ConvenioCobrancaControle.nomeArquivo1}"
                                                  styleClass="tooltipster"
                                                  style="font-weight: bold;"
                                                  title="#{ConvenioCobrancaControle.titleArquivo1Existente}"/>
                                    <a4j:commandLink action="#{ConvenioCobrancaControle.excluirArquivo1}"
                                                     style="margin: 10px;"
                                                     oncomplete="#{ConvenioCobrancaControle.msgAlert}"
                                                     onclick="if(!confirm('O certificado será excluído imediatamente. Tem certeza que deseja excluir o certificado?')){return false;};"
                                                     value="Excluir certificado"
                                                     reRender="form"/>
                                </h:panelGroup>


                                <h:panelGroup style="vertical-align: middle;"
                                              layout="block"
                                              id="panelArquivo1DCCCaixaOnline"
                                              rendered="#{!ConvenioCobrancaControle.possuiArquivo1}">
                                    <rich:fileUpload id="uploadArquivo1DCCCaixaOnline"
                                                     rendered="#{!ConvenioCobrancaControle.possuiArquivo1}"
                                                     listHeight="60"
                                                     listWidth="565"
                                                     noDuplicate="false"
                                                     fileUploadListener="#{ConvenioCobrancaControle.uploadArquivo1}"
                                                     maxFilesQuantity="1"
                                                     allowFlash="false"
                                                     immediateUpload="false"
                                                     acceptedTypes="pub, PUB, key.pub, KEY.PUB"
                                                     addControlLabel="Adicionar"
                                                     cancelEntryControlLabel="Cancelar"
                                                     doneLabel="Upload realizado com sucesso! Não esqueça de gravar o convênio para concluir!"
                                                     sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                     progressLabel="Enviando"
                                                     clearControlLabel="Limpar"
                                                     clearAllControlLabel="Limpar todos"
                                                     stopControlLabel="Parar"
                                                     uploadControlLabel="Enviar arquivo"
                                                     transferErrorLabel="Falha de Transmissão"
                                                     stopEntryControlLabel="Parar">
                                        <a4j:support event="onerror"
                                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"/>
                                        <a4j:support event="onclear" reRender="panelArquivo1DCCCaixaOnline"
                                                     action="#{ConvenioCobrancaControle.limparCamposUploadArquivo1}"/>
                                    </rich:fileUpload>
                                </h:panelGroup>

                                <%--CAMPOS CERTIFICADO PRIVADO SSH PEM--%>
                                <h:panelGroup style="display: grid;">
                                    <h:outputText value="Certificado digital SSH PEM privado:<br/>Formato (.key)"
                                                  escape="false"
                                                  styleClass="tituloCampos tooltipster"
                                                  title="Salva um arquivo por vez. Após Upload do arquivo, precisa clicar no Gravar. </br>
                                                        Se fizer o upload dos dois arquivos, ao clicar no Gravar, só vai salvar um."/>

                                </h:panelGroup>

                                <h:panelGroup rendered="#{ConvenioCobrancaControle.possuiArquivo2}">
                                    <h:outputText value="#{ConvenioCobrancaControle.nomeArquivo2}"
                                                  styleClass="tooltipster"
                                                  style="font-weight: bold;"
                                                  title="#{ConvenioCobrancaControle.titleArquivo2Existente}"/>
                                    <a4j:commandLink action="#{ConvenioCobrancaControle.excluirArquivo2}"
                                                     style="margin: 10px;"
                                                     oncomplete="#{ConvenioCobrancaControle.msgAlert}"
                                                     onclick="if(!confirm('O certificado será excluído imediatamente. Tem certeza que deseja excluir o certificado?')){return false;};"
                                                     value="Excluir certificado"
                                                     reRender="form"/>
                                </h:panelGroup>

                                <h:panelGroup style="vertical-align: middle;"
                                              rendered="#{!ConvenioCobrancaControle.possuiArquivo2}"
                                              layout="block" id="panelArquivo2DCCCaixaOnline">
                                    <rich:fileUpload id="upload2DCCCaixaOnline"
                                                     rendered="#{!ConvenioCobrancaControle.possuiArquivo2}"
                                                     listHeight="60"
                                                     listWidth="565"
                                                     noDuplicate="false"
                                                     fileUploadListener="#{ConvenioCobrancaControle.uploadArquivo2}"
                                                     maxFilesQuantity="1"
                                                     allowFlash="false"
                                                     immediateUpload="false"
                                                     acceptedTypes="KEY, key"
                                                     addControlLabel="Adicionar"
                                                     cancelEntryControlLabel="Cancelar"
                                                     doneLabel="Upload realizado com sucesso! Não esqueça de gravar o convênio para concluir!"
                                                     sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                     progressLabel="Enviando"
                                                     clearControlLabel="Limpar"
                                                     clearAllControlLabel="Limpar todos"
                                                     stopControlLabel="Parar"
                                                     uploadControlLabel="Enviar arquivo"
                                                     transferErrorLabel="Falha de Transmissão"
                                                     stopEntryControlLabel="Parar">
                                        <a4j:support event="onerror"
                                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"/>
                                        <a4j:support event="onclear" reRender="panelArquivo2DCCCaixaOnline"
                                                     action="#{ConvenioCobrancaControle.limparCamposUploadArquivo2}"/>
                                    </rich:fileUpload>
                                </h:panelGroup>
                            </c:if>

                            <%--PIX PJBANK--%>
                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo.pix and ConvenioCobrancaControle.convenioCobrancaVO.pixPjBank}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.titleExpiracaoPix}"
                                              value="Tempo de validade do pix, em dias:"/>
                                <h:panelGroup>
                                    <h:inputText
                                            title="#{ConvenioCobrancaControle.titleExpiracaoPix}"
                                            id="diasExpirarPix"
                                            styleClass="form tooltipster"
                                            value="#{ConvenioCobrancaControle.convenioCobrancaVO.diasExpirarPix}"/>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ChavePJBANK}:"/>
                                <h:panelGroup layout="block">
                                    <h:inputText id="chavePJBank"
                                                 styleClass="form" size="50"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.chavePJBank}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CredencialPJBANK}:"/>
                                <h:panelGroup layout="block">
                                    <h:inputText id="credencialPJBank"
                                                 styleClass="form" size="50"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.credencialPJBank}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Clique para verificar se as informações das credenciais lá do PJBank estão corretas com a unidade configurada para este convênio."
                                              value="Consultar informações da credencial:"/>
                                <a4j:commandLink styleClass="tooltipster"
                                                 title="Clique para verificar se as informações das credenciais lá do PJBank estão corretas com a unidade configurada para este convênio."
                                                 id="consultarCredencialPJBank"
                                                 value="Informações da Credencial"
                                                 reRender="formModalCredencialPJBank"
                                                 oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                 action="#{ConvenioCobrancaControle.consultarCredencialPJBank}"/>
                                    <h:outputText styleClass="tituloCamposNegrito tooltipster"
                                                  value="Aviso:"/>
                                    <h:outputText styleClass="tituloCamposNegrito texto-cor-vermelho tooltipster"
                                                  title="Por uma limitação da própria PjBank, no momento só será possível gerar pix de valor superior ou igual a R$7,50"
                                                  value="No momento só é possível gerar pix de valor superior ou igual a R$7,50 para convênio PJBank"/>

                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.apresentarSomenteExtrato && ConvenioCobrancaControle.exibirAbaConciliacao}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Somente para extrato (Conciliação):"
                                              title="Marque caso este convênio for utilizado somente para extrato. O convênio com esta configuração marcada não irá aparecer em outros locais do sistema, somente na conciliação."/>
                                <h:selectBooleanCheckbox id="somenteExtrato"
                                                         styleClass="tituloCampos tooltipster"
                                                         title="Marque caso este convênio for utilizado somente para extrato. O convênio com esta configuração marcada não irá aparecer em outros locais do sistema, somente na conciliação."
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.somenteExtrato}">
                                    <a4j:support event="onchange"
                                                 action="#{ConvenioCobrancaControle.acaoSomenteExtrato}"
                                                 reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.apresentarBloquearCobrancas}">
                                <h:panelGrid columns="2" style="display: inline">
                                <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                                styleClass="tooltipster"
                                                title="Necessário código de autenticação para alterar"/>
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.convenioCobrancaVO.bloquearCobrancaAutomaticaTitle}"
                                              value="Bloquear cobranças automáticas:"/>
                                </h:panelGrid>
                                <h:selectBooleanCheckbox id="bloquearCobrancaAutomatica"
                                                         styleClass="tituloCampos tooltipster"
                                                         title="#{ConvenioCobrancaControle.convenioCobrancaVO.bloquearCobrancaAutomaticaTitle}"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.bloquearCobrancaAutomatica}">
                                    <a4j:support event="onchange"
                                                 action="#{ConvenioCobrancaControle.acaoBloquearDesbloquearCobrancas}"
                                                 oncomplete="#{ConvenioCobrancaControle.msgAlert};"
                                                 reRender="modalDesbloqueioCobrancasAutomaticas"/>
                                </h:selectBooleanCheckbox>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.maxipago}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Informe a Adquirente que você possui disponível lá no portal da Maxipago."
                                              value="Adquirente MaxiPago"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="adquirenteMaxiPago"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.adquirenteMaxiPago}"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form">
                                        <f:selectItems
                                                value="#{ConvenioCobrancaControle.listaAdquirenteMaxiPago}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes && ConvenioCobrancaControle.exibirPactoPay}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Utilizar integração micro serviço PactoPay:"/>
                                <h:selectBooleanCheckbox id="pactoPay"
                                                         styleClass="tituloCampos tooltipster"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.pactoPay}">
                                    <a4j:support event="onchange" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </c:if>

                            <c:if test="${!ConvenioCobrancaControle.convenioCobrancaVO.boletoPjBank}">

                                <c:if test="${ConvenioCobrancaControle.boleto}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Dias para compensação do recebimento (Boleto):"
                                                  title="Quantidade de dias que o banco fará a compensação do pagamento para a empresa."/>
                                    <h:panelGroup>
                                        <h:inputText size="4" onblur="blurinput(this);"
                                                     id="diasCompesacaoReceb"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     style="padding-down: 10px"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.diasParaCompensacao}">
                                        </h:inputText>

                                        <a4j:commandLink rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                                         id="btnAtualizarDatasCompensacaoBoletos"
                                                         value="Atualizar recebimentos"
                                                         reRender="mdlMensagemGenerica"
                                                         oncomplete="#{ConvenioCobrancaControle.msgAlert};"
                                                         style="padding-left: 5px"
                                                         styleClass="tituloCampos tooltipster"
                                                         title="Atualiza as datas de compensação de todos os boletos já lançados anteriormente no sistema."
                                                         action="#{ConvenioCobrancaControle.modalAtualizarCompRecebimentosBoleto}"/>

                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ConvenioCobrancaControle.exibirDiasAntecipacaoCobrancaDCO}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Dias para antecipação de parcelas"
                                                  title="
                                          Quantidade de dias para antecipação do vencimento de parcelas para incluir no arquivo de remessa.<br>
                                          Este campo é considerado somente no <b>processo automatizado que gera o arquivo de remessa</b> todos os dias e envia ao banco.<br>
                                          Obs.: A data de vencimento das parcelas não serão alteradas, <b>elas serão somente enviadas antecipadamente aos bancos</b>, mas mantendo os vencimentos originais.<br>
                                          Este campo também é utilizado no processo automatizado de envio de parcelas em repescagem.<br>
                                          Parcelas processadas como <b>repescagem</b> serão enviadas com a <b>data de vencimento</b> no <b>dia do processamento da remessa + a quantidade de dias</b> informadas aqui.">
                                        <rich:toolTip followMouse="true" direction="top-right"
                                                      style="width:200px; height:120px;" showDelay="500">

                                        </rich:toolTip>
                                    </h:outputText>
                                    <h:inputText size="4"
                                                 styleClass="form"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.diasAntecipacaoRemessaDCO}"/>

                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="(Processo Automático) Não cobrar automaticamente do cliente com parcela vencida a mais de"
                                                  title="Não cobrar automaticamente do cliente com parcela vencida a mais de X dias.">
                                    </h:outputText>

                                    <h:panelGroup layout="block">
                                        <h:inputText size="4"
                                                     styleClass="form"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.diasLimiteVencimentoParcelaDCO}"/>
                                        <h:outputText value="dias" styleClass="tituloCampos tooltipster"
                                                      style="padding-left: 5px"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ConvenioCobrancaControle.exibirQuantidadeLimiteItensRemessa}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Quantidade máxima de itens por remessa"
                                                  title="Quantidade máxima de itens que uma remessa."/>
                                    <h:inputText size="4"
                                                 styleClass="form"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.limiteItensRemessa}"/>
                                </c:if>
                            </c:if>

                            <c:if test="${(ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoAutorizacao == 'BOLETO_BANCARIO' || ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoAutorizacao == 'DEBITOCONTA' ||
                                          (ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoRemessa == 'PIX' && EmpresaControle.configuracaoSistema.isUtilizarServicoSesiSC())) &&
                                          !ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas}">
                                <h:outputText styleClass="tituloCampos" value="Banco: "/>
                                <h:selectOneMenu
                                        readonly="#{ConvenioCobrancaControle.convenioCobrancaVO.tipo == 'BOLETO_ITAU'}"
                                        id="bancoCobranca"
                                        value="#{ConvenioCobrancaControle.convenioCobrancaVO.banco.codigo}"
                                        tabindex="5">
                                    <f:selectItems value="#{ConvenioCobrancaControle.bancos}"/>
                                    <a4j:support event="onchange" action="#{ConvenioCobrancaControle.preencherBanco}"
                                                 reRender="gridForm"/>
                                </h:selectOneMenu>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_contaEmpresa}" rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoRemessa != 'PIX'}"/>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ContaCorrente_label_contaCorrente}" rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoRemessa == 'PIX'}"/>

                                <h:panelGroup>
                                    <h:selectOneMenu id="contaEmpresa" styleClass="form" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.contaEmpresa.codigo}">
                                        <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemContaEmpresa}"/>
                                    </h:selectOneMenu>
                                    <h:message for="contaEmpresa" styleClass="mensagemDetalhada"/>
                                    <a4j:commandButton id="atualizar_contaEmpresa"
                                                       action="#{ConvenioCobrancaControle.montarListaSelectItemContaEmpresa}"
                                                       image="imagens/atualizar.png" ajaxSingle="true"
                                                       styleClass="tooltipster"
                                                       title="Atualiza as contas correntes!"
                                                       reRender="form:contaEmpresa"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo.codigo == '45'}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Registrar Boleto somente na impressão:"
                                              title="Marque caso deseje que o boleto seja registrado no banco apenas quando clicar para imprimir ou enviar por e-mail na tela do aluno, Vendas Online, Link de Pagamento, App, Totem e PactoFlow."/>
                                <h:selectBooleanCheckbox id="checkboxRegistrarBoletoImpressao"
                                                         styleClass="tituloCampos tooltipster"
                                                         title="Marque caso deseje que o boleto seja registrado no banco apenas quando clicar para imprimir ou enviar por e-mail na tela do aluno, Vendas Online, Link de Pagamento, App, Totem e PactoFlow."
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.registrarBoletoOnlineSomenteNaImpressao}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.apresentarCNPJ}">
                                <h:outputText styleClass="tituloCampos" value="CNPJ:"/>
                                <h:panelGroup>
                                    <h:inputText id="CNPJ" size="20" maxlength="18"
                                                 onkeypress="return mascara(this.form, 'form:CNPJ', '99.999.999/9999-99', event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.cnpj}"/>
                                    <h:message for="CNPJ" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarExtensaoRemessa}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.titleExtensaoRemessa}"
                                              value="* Extensão do Arquivo de Remessa:"/>
                                <h:panelGroup>
                                    <h:inputText id="extensaoArquivoRemessa" size="5" maxlength="5" styleClass="form tooltipster"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 title="#{ConvenioCobrancaControle.titleExtensaoRemessa}"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.extensaoArquivoRemessa}"/>
                                    <h:message for="extensaoArquivoRemessa" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="* Extensão do Arquivo de Retorno:"/>
                                <h:panelGroup>
                                    <h:inputText id="extensaoArquivoRetorno" size="5" maxlength="5" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.extensaoArquivoRetorno}"/>
                                    <h:message for="extensaoArquivoRetorno" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarSequencialDoArquivo && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_sequencialDoArquivo}"/>
                                <h:panelGroup>
                                    <h:inputText id="sequencialDoArquivo" size="10" maxlength="10" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.sequencialDoArquivo}"/>
                                    <h:message for="sequencialDoArquivo" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText
                                        rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoSequencialUnico}"
                                        styleClass="tituloCampos"
                                        value="#{msg_aplic.prt_ConvenioCobranca_sequencialUnico}"/>
                                <h:panelGroup
                                        rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoSequencialUnico}">
                                    <h:selectBooleanCheckbox id="sequencialUnico" styleClass="form"
                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.usarSequencialUnico}">
                                        <a4j:support event="onchange" reRender="form"/>
                                    </h:selectBooleanCheckbox>
                                    <h:message for="sequencialUnico" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText
                                        rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoGerarMultaEJurosRemessaItauCNAB400}"
                                        styleClass="tituloCampos tooltipster"
                                        value="#{msg_aplic.prt_ConvenioCobranca_utilizarMultaJurosSistemaBoletoItau}"
                                        title="Boleto do banco Itaú pode ser configurado Multa e Juros no Portal do Itaú ou no sistema. </br>
                                        Se for utilizar no sistema, marcar essa configuração, configurar na Empresa os valores de Multa e Juros e não configurar no Portal do Itaú. </br>
                                        Se for utilizar no Portal do Banco, desmarcar essa configuração e configurar os valores de Multa e Juros no Portal do Banco." />
                                <h:panelGroup rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoGerarMultaEJurosRemessaItauCNAB400}">
                                    <h:selectBooleanCheckbox id="multaJurosBoletoItau" styleClass="form"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.gerarMultaEJurosRemessaItauCNAB400}">
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.apresentarCampoTipoParcelamentoStone}">
                                <h:outputText styleClass="tituloCampos" value="* Tipo de parcelamento:"/>
                                <h:panelGrid columns="2" style="display: inline">
                                    <h:panelGroup>
                                        <h:selectOneMenu id="tipoParcelamentoStone" styleClass="form"
                                                         onblur="blurinput(this);"
                                                         onfocus="focusinput(this);"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipoParcelamentoStone}">
                                            <f:selectItem itemLabel=""/>
                                            <f:selectItem itemLabel="Lojista - Sem juros | MCHT | (lojista paga os juros)"
                                                          itemValue="MCHT"/>
                                            <f:selectItem itemLabel="Emissor - Com juros | ISSR | (portador do cartão paga os juros)"
                                                          itemValue="ISSR"/>
                                        </h:selectOneMenu>
                                        <h:message for="tipoParcelamentoStone" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="font-size: 18px;"
                                       title="${ConvenioCobrancaControle.titleTipoParcelamentoStone}"></i>
                                </h:panelGrid>
                            </c:if>

                            <%--                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo == 'BOLETO' && ConvenioCobrancaControle.convenioCobrancaVO.banco.codigoBanco == 237}">--%>
                            <%--                                <h:outputText rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoSequencialUnico}"  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioCobranca_sequencialUnico}" />--%>
                            <%--                                <h:panelGroup rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoSequencialUnico}">--%>
                            <%--                                    <h:selectBooleanCheckbox  id="sequencialUnico" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ConvenioCobrancaControle.convenioCobrancaVO.usarSequencialUnico}">--%>
                            <%--                                        <a4j:support event="onchange" reRender="form"/>--%>
                            <%--                                    </h:selectBooleanCheckbox>--%>
                            <%--                                    <h:message for="sequencialUnico" styleClass="mensagemDetalhada"/>--%>
                            <%--                                </h:panelGroup>--%>
                            <%--                            </c:if>--%>
                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.stoneV5}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.titleUsaSplitPagamentoStoneV5}"
                                              value="Usar Split de Pagamentos:"/>

                                <h:panelGrid columns="2" style="display: inline">
                                    <h:selectBooleanCheckbox id="checkSplitPagamentoStoneV5"
                                                             styleClass="tituloCampos tooltipster"
                                                             title="#{ConvenioCobrancaControle.titleUsaSplitPagamentoStoneV5}"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.usaSplitPagamentoStoneV5}">
                                        <a4j:support event="onchange"
                                                     reRender="form"/>
                                    </h:selectBooleanCheckbox>

                                    <h:graphicImage id="iconWarningSplitStone"
                                                    styleClass="tooltipster"
                                                    title="#{ConvenioCobrancaControle.titleUsaSplitPagamentoStoneV5}"
                                                    style="inline-size: 20px; margin-left: 5px; vertical-align: -5px;"
                                                    value="images/warning.png"/>
                                </h:panelGrid>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Tipo da Credencial:"/>
                                <h:panelGrid columns="2" style="display: inline">
                                    <h:panelGroup>
                                        <h:selectOneMenu id="listaSelectItemTipoCredStone" styleClass="form"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipoCredencialStoneEnum}">
                                            <f:selectItems
                                                    value="#{ConvenioCobrancaControle.listaSelectItemTipoCredencialStone}"/>
                                            <a4j:support event="onchange"
                                                         reRender="form"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="font-size: 18px;"
                                       title="${ConvenioCobrancaControle.titleTipoCredencialStoneV5}"></i>
                                </h:panelGrid>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Portal ao qual você também poderá acompanhar as transações de pagamento."
                                              value="Portal disponível:"/>
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Portal ao qual você também poderá acompanhar as transações de pagamento."
                                              value="#{ConvenioCobrancaControle.tipoPortalStoneV5}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo == 'BOLETO' && ConvenioCobrancaControle.convenioCobrancaVO.banco.codigoBanco == 237 && !ConvenioCobrancaControle.convenioCobrancaVO.boletoPjBank}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_Permitir_Receber_Boleto_Apos_Vencimento}"/>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox styleClass="campos"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.permitirReceberBoletoAposVencimento}"/>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos" value="Desconto no Boleto:"/>
                                <h:panelGroup>
                                    <h:inputText id="descBoletoBradesco" size="4" styleClass="form"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.descontoBoleto}"/>
                                    <h:outputText styleClass="tituloCampos" value=" %"/>
                                    <h:message for="descBoletoBradesco" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${(!ConvenioCobrancaControle.convenioCobrancaVO.tipo.pix && ConvenioCobrancaControle.convenioCobrancaVO.apresentarCampoSequencialItem) ||
                                        ConvenioCobrancaControle.convenioCobrancaVO.apresentarApenasSequencialItem}">
                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.itauRegistroOnline}"
                                              value="Sequencial:"/>
                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.caixaRegistroOnline}"
                                              value="* Nosso Número:"
                                              title="Esse valor é obrigatório e precisa verificar qual o último número utilizado para seguir a sequencia ou vai dar erro '54 - Hash Inválido' na geração do boleto."/>
                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}"
                                              value="* Nosso Número:"
                                              title="Esse valor é obrigatório e precisa verificar qual o último número utilizado para seguir a sequencia ou vai dar erro 'Título já incluído anteriormente' na geração do boleto."/>
                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.itauRegistroOnline &&
                                                          !ConvenioCobrancaControle.convenioCobrancaVO.caixaRegistroOnline &&
                                                          !ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}"
                                              value="* Sequencial dos itens de remessa:"/>
                                <h:panelGroup>
                                    <h:inputText id="sequencialItem" size="10" maxlength="10" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.sequencialItem}"/>
                                    <h:message for="sequencialItem" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarNumeroContrato}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Num. Contrato/Num. Estabelecimento:"
                                              title="#{ConvenioCobrancaControle.titleNumContrato}"
                                              rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.caixaRegistroOnline && !ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}" />
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Num. Contrato/Cód. Beneficiário:"
                                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.caixaRegistroOnline}" />
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="* Código Convênio:"
                                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoBancoBrasil}" />
                                <h:panelGroup>
                                    <h:inputText id="numeroContrato" size="30" maxlength="30"
                                                 styleClass="form tooltipster"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.numeroContrato}"
                                                 title="#{ConvenioCobrancaControle.titleNumContrato}">
                                    </h:inputText>
                                    <h:message for="numeroContrato" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarOperacao && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:outputText styleClass="tituloCampos" value="Operação"/>
                                <h:panelGroup>
                                    <h:inputText id="operacao" size="30" maxlength="30" styleClass="form"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.operacao}"/>
                                </h:panelGroup>
                            </c:if>

                            <h:outputText
                                    rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarNumeroLogico}"
                                    styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioCobranca_numeroLogico}"/>
                            <h:panelGroup
                                    rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarNumeroLogico}">
                                <h:inputText size="30" maxlength="30" styleClass="form" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.numeroLogico}"/>
                                <h:message for="numeroContrato" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo == 'BOLETO' && ConvenioCobrancaControle.convenioCobrancaVO.banco.codigoBanco == 33}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_codigoTransmissao}"/>
                                <h:panelGroup>
                                    <h:inputText id="codigoTransmissao" size="20" maxlength="20" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoTransmissao}"/>
                                    <h:message for="codigoTransmissao" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.apresentarCampoCarteira}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_carteira}"/>
                                <h:panelGroup>
                                    <h:inputText id="carteira" size="10" maxlength="10" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.carteira}"/>
                                    <h:message for="carteira" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.apresentarCampoCarteiraBoleto}">

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_carteiraBoleto}"
                                              title="Dado fornecido pelo banco."/>
                                <h:panelGroup>
                                    <h:inputText id="carteiraBoleto" size="10" maxlength="10" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.carteiraBoleto}"/>
                                    <h:message for="carteiraBoleto" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_variacao}"
                                              rendered="#{ConvenioCobrancaControle.apresentarCampoVariacao}"
                                              title="Dado fornecido pelo banco."/>
                                <h:panelGroup rendered="#{ConvenioCobrancaControle.apresentarCampoVariacao}">
                                    <h:inputText id="variacao" size="10" maxlength="10" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.variacao}"/>
                                    <h:message for="variacao" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Número de dias APÓS o vencimento do título em que será protestado automaticamente. Informe 0 para NÃO protestar automaticamente"
                                              value="Nr. Dias para Protesto:"
                                              rendered="#{ConvenioCobrancaControle.apresentarCampoNrDiasProtesto}"/>
                                <h:panelGroup rendered="#{ConvenioCobrancaControle.apresentarCampoNrDiasProtesto}">
                                    <h:inputText id="nrDiasProtesto" size="3" maxlength="2" styleClass="form tooltipster"
                                                 title="Número de dias APÓS o vencimento do título em que será protestado automaticamente. Informe 0 para NÃO protestar automaticamente"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.nrDiasProtesto}"/>
                                    <h:message for="nrDiasProtesto" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_nrDiasBaixaAutomatica}"
                                              rendered="#{ConvenioCobrancaControle.apresentarCampoNrDiasBaixaAutomatica}"/>
                                <h:panelGroup
                                        rendered="#{ConvenioCobrancaControle.apresentarCampoNrDiasBaixaAutomatica}">
                                    <h:inputText id="nrDiasBaixaAutomatica" size="5" maxlength="3" styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.nrDiasBaixaAutomatica}"/>
                                    <h:message for="nrDiasBaixaAutomatica" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarTipoRemessa}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_tipoRemessa}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="tipoRemessa" styleClass="form" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipoRemessa.codigo}">
                                        <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemTipoRemessa}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_tipoRemessa"
                                                       action="#{ConvenioCobrancaControle.montarListaSelectItemTipoRemessa}"
                                                       image="imagens/atualizar.png" ajaxSingle="true"
                                                       reRender="form:tipoRemessa"/>
                                    <h:message for="tipoRemessa" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarLayoutBoletoOnline}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Layout impressão boleto:"/>
                                <h:selectOneMenu id="layoutBoletoOnline" styleClass="form" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.layoutBoletoOnline}">
                                    <f:selectItems value="#{ConvenioCobrancaControle.listaLayoutBoletoOnline}"/>
                                </h:selectOneMenu>
                            </c:if>

                            <h:outputText rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.layoutFebrabanDCO}"
                                          styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ConvenioCobranca_identificadorClienteEmpresa}"/>
                            <h:panelGroup rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.layoutFebrabanDCO}">
                                <h:selectOneMenu id="idClienteEmpresa" styleClass="form" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.identificadorClienteEmpresa.codigo}">
                                    <f:selectItems
                                            value="#{ConvenioCobrancaControle.listaSelectItemIdentificadorClienteEmpresa}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.boletoPjBank and !ConvenioCobrancaControle.convenioCobrancaVO.pixPjBank}">
                            <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.titleTipoBoletoPJBank}"
                                              value="Tipo Boleto (Boleto/Pix):"/>
                                <h:selectOneMenu id="tipoBoletoPJBank" styleClass="form"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.tipoBoletoPJBank}">
                                    <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemTipoBoletoPJBank}"/>
                                </h:selectOneMenu>
                            </c:if>
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ChavePJBANK}:"/>
                                <h:panelGroup layout="block">
                                    <h:inputText id="chavePJBank"
                                                 styleClass="form" size="50"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.chavePJBank}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CredencialPJBANK}:"/>
                                <h:panelGroup layout="block">
                                    <h:inputText id="credencialPJBank"
                                                 styleClass="form" size="50"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.credencialPJBank}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Clique para verificar se as informações das credenciais lá do PJBank estão corretas com a unidade configurada para este convênio."
                                              value="Consultar informações da credencial:"/>
                                <a4j:commandLink styleClass="tooltipster"
                                                 title="Clique para verificar se as informações das credenciais lá do PJBank estão corretas com a unidade configurada para este convênio."
                                                 id="consultarCredencialPJBank"
                                                 value="Informações da Credencial"
                                                 reRender="formModalCredencialPJBank"
                                                 oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                 action="#{ConvenioCobrancaControle.consultarCredencialPJBank}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoAutorizacao == 'BOLETO_BANCARIO'}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_instrucoesBoleto}"/>
                                <h:panelGroup layout="block" id="panelBoletoConveTable" style="display: flex">
                                    <h:inputTextarea id="instrucoesBoleto"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.instrucoesBoleto}"
                                                     cols="80" rows="5"/>
                                    <h:panelGroup layout="block" id="painelTagsBoleto" style="display: grid;">
                                        <a4j:commandButton id="tagDesconto"
                                                           action="#{ConvenioCobrancaControle.adicionarTagDesconto}"
                                                           style="vertical-align: bottom; height: 27px;" reRender="instrucoesBoleto"
                                                           value="Tag Desconto" alt="Tag Desconto" accesskey="2"
                                                           styleClass="botoes nvoBt"
                                                           rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.boletoPjBank && !ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas}"/>
                                        <a4j:commandButton id="tagMatricula"
                                                           action="#{ConvenioCobrancaControle.adicionarTagMatricula}"
                                                           style="vertical-align: bottom; height: 27px;" reRender="instrucoesBoleto"
                                                           value="Tag Matrícula" alt="Tag Matrícula" accesskey="2"
                                                           styleClass="botoes nvoBt"
                                                           />
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_sitesBoleto}"
                                              rendered="#{ConvenioCobrancaControle.apresentarCampoSitesNoBoleto}"/>
                                <h:panelGroup rendered="#{ConvenioCobrancaControle.apresentarCampoSitesNoBoleto}">
                                    <h:inputTextarea id="sitesBoleto"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.sitesBoleto}"
                                                     cols="80" rows="5"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarConfiguracaoEnvioRemessa && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                <!-- dados de envio de remessa -->
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_hostSFTP}"/>
                                <h:inputText id="hostSFTP" size="70" maxlength="70" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.hostSFTP}"/>


                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_portSFTP}"/>
                                <h:inputText id="portSFTP" size="70" maxlength="70" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.portSFTP}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_userSFTP}"/>
                                <h:inputText id="userSFTP" size="50" maxlength="50" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.userSFTP}">
                                    <a4j:support event="onchange" action="#{ConvenioCobrancaControle.setarUsuarioSFTP}"
                                                 reRender="form"/>
                                </h:inputText>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_pwdSFTP}"/>
                                <h:inputText id="pwdSFTP" size="50" maxlength="50" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.pwdSFTP}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_TivitOUTFTP}"/>
                                <h:inputText id="diretorioRemotoTIVIT_OUT" size="70" maxlength="120" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioRemotoTIVIT_OUT}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_TivitINFTP}"/>
                                <h:inputText id="diretorioRemotoTIVIT_IN" size="70" maxlength="120" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioRemotoTIVIT_IN}"/>

                                <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo == 'DCC_BIN'}">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_ConvenioCobranca_chaveBIN}"/>
                                    <h:panelGroup>
                                        <rich:fileUpload listHeight="0"
                                                         listWidth="150"
                                                         noDuplicate="false"
                                                         fileUploadListener="#{ConvenioCobrancaControle.uploadChaveBIN}"
                                                         maxFilesQuantity="1"
                                                         addControlLabel="Adicionar"
                                                         cancelEntryControlLabel="Cancelar"
                                                         doneLabel="Pronto"
                                                         sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                         progressLabel="Enviando"
                                                         stopControlLabel="Parar"
                                                         uploadControlLabel="Enviar"
                                                         transferErrorLabel="Falha de Transmissão"
                                                         stopEntryControlLabel="Parar"
                                                         id="uploadChaveBIN"
                                                         immediateUpload="true"
                                                         autoclear="true"
                                                         acceptedTypes="asc,txt,key">
                                            <a4j:support event="oncomplete" ajaxSingle="true" reRender="form"/>
                                        </rich:fileUpload>
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{ConvenioCobrancaControle.convenioCobrancaVO.nomeChaveBIN}"/>
                                    </h:panelGroup>
                                </c:if>

                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo.tipoCobranca == 'EDI_DCO'}">

                                <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="Processar remessas automático (Geração e Retorno):"/>
                                    <h:selectBooleanCheckbox id="processarRemessasAutomatico" styleClass="campos"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.processarRemessasAutomatico}"/>

                                    <h:outputText styleClass="tituloCampos" value="Gerar Arquivo Único:"/>
                                    <h:selectBooleanCheckbox id="gerarArquivoUnico" styleClass="campos"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.gerarArquivoUnico}"/>

                                    <h:outputText styleClass="tituloCampos" value="Agrupar itens por pessoa:"/>
                                    <h:selectBooleanCheckbox id="agruparPorPessoaParcela" styleClass="campos"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.agruparPorPessoaParcela}"/>

                                    <h:outputText styleClass="tituloCampos" value="Nomenclatura Arquivo:"/>
                                    <h:selectOneMenu id="nomenclaturaArquivo" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.nomenclaturaArquivo.codigo}">
                                        <f:selectItems
                                                value="#{ConvenioCobrancaControle.listaSelectItemNomenclaturaArquivo}"/>
                                    </h:selectOneMenu>

                                    <h:outputText styleClass="tituloCampos" value="Número do compromisso:"/>
                                    <h:inputText id="numeroCompromissoDCO" size="10" maxlength="10" styleClass="form"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.numeroCompromisso}"/>
                                </c:if>

                                <h:outputText styleClass="tituloCampos" value="Omitir sequencial do arquivo:"/>
                                <h:selectBooleanCheckbox id="omitirSequencialArquivo" styleClass="campos"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.omitirSequencialArquivo}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarEnviarRemessaSFTPNow && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_SFTPNow}"/>
                                <h:selectBooleanCheckbox id="enviarRemessaSFTPNow" styleClass="campos"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.enviarRemessaSFTPNow}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarConfiguracaoEnvioRemessa && LoginControle.usuarioLogado.usuarioPactoSolucoes}">

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_diretorioLocalTIVIT}"/>
                                <h:inputText id="diretorioLocalTIVIT" size="70" maxlength="70" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioLocalTIVIT}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_diretorioLocalUploadTIVIT}"/>
                                <h:inputText id="diretorioLocalUploadTIVIT" size="70" maxlength="70" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioLocalUploadTIVIT}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_diretorioLocalDownloadTIVIT}"/>
                                <h:inputText id="diretorioLocalDownloadTIVIT" size="70" maxlength="70" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioLocalDownloadTIVIT}"/>

                                <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.convenioGetnetEDI}">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_ConvenioCobranca_chaveGETNET}"/>
                                    <h:panelGroup>
                                        <rich:fileUpload listHeight="0"
                                                         listWidth="150"
                                                         noDuplicate="false"
                                                         fileUploadListener="#{ConvenioCobrancaControle.uploadChaveGETNET}"
                                                         maxFilesQuantity="1"
                                                         addControlLabel="Adicionar"
                                                         cancelEntryControlLabel="Cancelar"
                                                         doneLabel="Pronto"
                                                         sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                         progressLabel="Enviando"
                                                         stopControlLabel="Parar"
                                                         uploadControlLabel="Enviar"
                                                         transferErrorLabel="Falha de Transmissão"
                                                         stopEntryControlLabel="Parar"
                                                         id="uploadChaveGETNET"
                                                         immediateUpload="true"
                                                         autoclear="true"
                                                         acceptedTypes="asc,txt,key">
                                            <a4j:support event="oncomplete" ajaxSingle="true" reRender="form"/>
                                        </rich:fileUpload>
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{ConvenioCobrancaControle.convenioCobrancaVO.nomeChaveGETNET}"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_ConvenioCobranca_nossaChave}"/>
                                    <h:panelGroup>
                                        <rich:fileUpload listHeight="0"
                                                         listWidth="150"
                                                         noDuplicate="false"
                                                         fileUploadListener="#{ConvenioCobrancaControle.uploadNossaChave}"
                                                         maxFilesQuantity="1"
                                                         addControlLabel="Adicionar"
                                                         cancelEntryControlLabel="Cancelar"
                                                         doneLabel="Pronto"
                                                         sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                         progressLabel="Enviando"
                                                         stopControlLabel="Parar"
                                                         uploadControlLabel="Enviar"
                                                         transferErrorLabel="Falha de Transmissão"
                                                         stopEntryControlLabel="Parar"
                                                         id="nossaChave"
                                                         immediateUpload="true"
                                                         autoclear="true"
                                                         acceptedTypes="asc,txt,key">
                                            <a4j:support event="oncomplete" ajaxSingle="true" reRender="form"/>
                                        </rich:fileUpload>
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{ConvenioCobrancaControle.convenioCobrancaVO.nomeNossaChave}"/>
                                    </h:panelGroup>
                                </c:if>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.convenioGetnetEDI && LoginControle.usuarioLogado.usuarioPactoSolucoes}">

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_nossaSenha}"/>
                                <h:inputText id="nossaSenha" size="40" maxlength="70" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.nossaSenha}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_sequencialDoArquivoCancelamento}"/>
                                <h:panelGroup>
                                    <h:inputText id="sequencialDoArquivoCancelamento" size="10" maxlength="10"
                                                 styleClass="form"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.sequencialArquivoCancelamento}"/>
                                    <h:message for="sequencialDoArquivoCancelamento" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_CancelamentoGETNETOUT}"/>
                                <h:inputText id="diretorioCancelamentoGETNET_OUT" size="70" maxlength="120"
                                             styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioGETNET_CANCELAMENTO_OUT}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_CancelamentoGETNETIN}"/>
                                <h:inputText id="diretorioCancelamentoGETNET_IN" size="70" maxlength="120"
                                             styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioGETNET_CANCELAMENTO_IN}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.pagarMe}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="As informações de Metadata irão aparecer no detalhamento da transação lá no portal da própria Pagar.me.<br/>Este campo aqui é opcional e irá apenas acrescentar uma descrição opcional de metadata."
                                              value="Metadata:"/>
                                <h:inputText id="metadataPagarme" size="50" maxlength="150"
                                             styleClass="form tooltipster"
                                             title="As informações de Metadata irão aparecer no detalhamento da transação lá no portal da própria Pagar.me.<br/>Este campo aqui é opcional e irá apenas acrescentar uma descrição opcional de metadata."
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.mensagem}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarCodigoAutenticacao01}">
                                <h:panelGrid columns="2" style="display: inline">
                                    <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                                    styleClass="tooltipster"
                                                    title="Necessário código de autenticação para alterar"/>
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao01}"
                                                  value="* #{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao01}"/>
                                </h:panelGrid>

                                <%-- codigoautenticacao01 campo tamanho normal "inputText" --%>
                                <h:panelGroup layout="block"
                                              rendered="#{!ConvenioCobrancaControle.exibirCampoInputTextArea}">
                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:inputText id="codigoAutenticacao01"
                                                     onkeydown="apagarCampoUmaVez(this);"
                                                     styleClass="form tooltipster" size="50"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao01Exibir}"
                                                     title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao01}"/>
                                        <a4j:commandLink
                                                onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao01Exibir}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Copiar credencial"></i>
                                        </a4j:commandLink>
                                    </h:panelGrid>
                                </h:panelGroup>

                                <%-- codigoautenticacao01 campo tamanho maior "inputTextarea" --%>
                                <h:panelGroup layout="block"
                                              rendered="#{ConvenioCobrancaControle.exibirCampoInputTextArea}">
                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:inputTextarea id="chaveApiAsaas" style="width: 503px;height: 50px"
                                                         styleClass="form tooltipster"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao01Exibir}"
                                                         title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao01}"/>
                                        <a4j:commandLink
                                                onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao01Exibir}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Copiar credencial"></i>
                                        </a4j:commandLink>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarCodigoAutenticacao02}">
                                <h:panelGrid columns="2" style="display: inline">
                                    <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                                    styleClass="tooltipster"
                                                    title="Necessário código de autenticação para alterar"/>
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao02}"
                                                  value="* #{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao02}"/>
                                </h:panelGrid>
                                <h:panelGroup layout="block">
                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:inputText id="codigoAutenticacao02"
                                                     onkeydown="apagarCampoUmaVez(this);"
                                                     styleClass="form tooltipster" size="50"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao02Exibir}"
                                                     title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao02}"/>
                                        <a4j:commandLink
                                                onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao02Exibir}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Copiar credencial"></i>
                                        </a4j:commandLink>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarCodigoAutenticacao03}">
                                <h:panelGroup layout="block">

                                    <h:panelGrid columns="2" style="display: inline"
                                                 rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.pagoLivre}">
                                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                                        styleClass="tooltipster"
                                                        title="Necessário código de autenticação para alterar"/>
                                        <h:outputText styleClass="tituloCampos tooltipster"
                                                      title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao03}"
                                                      style="padding-right: 3px"
                                                      value="*"/>
                                    </h:panelGrid>

                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                                        onkeydown="apagarCampoUmaVez(this);"
                                                        rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pagoLivre}"
                                                        styleClass="tooltipster"
                                                        title="Necessário código de autenticação para alterar"/>
                                        <h:outputText styleClass="tituloCampos tooltipster"
                                                      title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao03}"
                                                      value="#{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao03}"/>
                                    </h:panelGrid>

                                </h:panelGroup>
                                <h:panelGroup layout="block">
                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:inputText id="codigoAutenticacao03"
                                                     styleClass="form tooltipster" size="50"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao03Exibir}"
                                                     title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao03}"/>
                                        <a4j:commandLink
                                                onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao03Exibir}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Copiar credencial"></i>
                                        </a4j:commandLink>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.itauRegistroOnline or ConvenioCobrancaControle.convenioCobrancaVO.pixItau}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Certificado Itaú:"/>
                                <h:panelGroup>
                                    <rich:fileUpload listHeight="0"
                                                     listWidth="150"
                                                     noDuplicate="false"
                                                     fileUploadListener="#{ConvenioCobrancaControle.uploadChaveGETNET}"
                                                     maxFilesQuantity="1"
                                                     addControlLabel="Adicionar"
                                                     cancelEntryControlLabel="Cancelar"
                                                     doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                     progressLabel="Enviando"
                                                     stopControlLabel="Parar"
                                                     uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão"
                                                     stopEntryControlLabel="Parar"
                                                     id="uploadChaveGETNET"
                                                     immediateUpload="true"
                                                     autoclear="true"
                                                     acceptedTypes="crt">
                                        <a4j:support event="oncomplete" ajaxSingle="true" reRender="form"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{ConvenioCobrancaControle.convenioCobrancaVO.nomeChaveGETNET}"/>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="Chave Privada Certificado Itaú:"/>
                                <h:panelGroup>
                                    <rich:fileUpload listHeight="0"
                                                     listWidth="150"
                                                     noDuplicate="false"
                                                     fileUploadListener="#{ConvenioCobrancaControle.uploadNossaChave}"
                                                     maxFilesQuantity="1"
                                                     addControlLabel="Adicionar"
                                                     cancelEntryControlLabel="Cancelar"
                                                     doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                     progressLabel="Enviando"
                                                     stopControlLabel="Parar"
                                                     uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão"
                                                     stopEntryControlLabel="Parar"
                                                     id="nossaChave"
                                                     immediateUpload="true"
                                                     autoclear="true"
                                                     acceptedTypes="key">
                                        <a4j:support event="oncomplete" ajaxSingle="true" reRender="form"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{ConvenioCobrancaControle.convenioCobrancaVO.nomeNossaChave}"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarCodigoAutenticacao04}">

                                <h:panelGrid columns="2" style="display: inline">
                                    <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                                    styleClass="tooltipster"
                                                    title="Necessário código de autenticação para alterar"/>
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao04}"
                                                  value="* #{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao04}"/>
                                </h:panelGrid>

                                <h:panelGroup layout="block">
                                    <h:inputText id="codigoAutenticacao04"
                                                 onkeydown="apagarCampoUmaVez(this);"
                                                 styleClass="form tooltipster" size="50"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao04Exibir}"
                                                 title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao04}"/>

                                    <a4j:commandLink
                                            onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao04Exibir}')">
                                        <i class="fa-icon-copy tooltipster"
                                           style="margin-left: 3px;"
                                           title="Copiar credencial"></i>
                                    </a4j:commandLink>

                                    <a4j:commandButton
                                            rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pagoLivre || ConvenioCobrancaControle.convenioCobrancaVO.facilitePay}"
                                            oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                            action="#{ConvenioCobrancaControle.testarConciliacaoPagoLivre}"
                                            value="Testar" title="Testar token conciliação"
                                            style="margin-left: 5px;"
                                            styleClass="botoes tooltipster"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarCodigoAutenticacao05}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao05}"
                                              value="* #{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao05}"/>
                                <h:panelGroup layout="block">
                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:inputText id="codigoAutenticacao05"
                                                     onkeydown="apagarCampoUmaVez(this);"
                                                     styleClass="form tooltipster" size="50"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao05Exibir}"
                                                     title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao05}"/>
                                        <a4j:commandLink
                                                onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao05Exibir}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Copiar credencial"></i>
                                        </a4j:commandLink>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarCodigoAutenticacao06}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao06}"
                                              value="* #{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao06}"/>
                                <h:panelGroup layout="block">
                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:inputText id="codigoAutenticacao06"
                                                     onkeydown="apagarCampoUmaVez(this);"
                                                     styleClass="form tooltipster" size="50"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao06Exibir}"
                                                     title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao06}"/>
                                        <a4j:commandLink
                                                onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao06Exibir}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Copiar credencial"></i>
                                        </a4j:commandLink>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarCodigoAutenticacao07}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao07}"
                                              value="* #{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao07}"/>
                                <h:panelGroup layout="block">
                                    <h:panelGrid columns="2" style="display: inline">
                                        <h:inputText id="codigoAutenticacao07"
                                                     onkeydown="apagarCampoUmaVez(this);"
                                                     styleClass="form tooltipster" size="50"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao07Exibir}"
                                                     title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao07}"/>
                                        <a4j:commandLink
                                                onclick="copiarCredencial('#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutenticacao07Exibir}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Copiar credencial"></i>
                                        </a4j:commandLink>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </c:if>

                            <%--Campos Boleto Asaas--%>
                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas || ConvenioCobrancaControle.convenioCobrancaVO.pixAsaas}">

                                <%--Tempo de validade do pix--%>
                                <h:outputText rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pixAsaas}"
                                              styleClass="tituloCampos tooltipster"
                                              title="Dias que o pix ficará ativo para pagamento. O dia exato que o pix foi gerado não conta."
                                              value="Tempo de validade do pix, em dias:"/>
                                <h:inputText rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pixAsaas}"
                                             title="O tempo de expiração deve ser um número que corresponde aos dias que o pix ficará ativo para pagamento."
                                             id="diasExpirarPixAsaas"
                                             styleClass="form tooltipster"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diasExpirarPix}"/>

                                <%--Boleto Híbrido--%>
                                <h:outputText rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas}"
                                              styleClass="tituloCampos tooltipster"
                                              title="Com essa configuração ativada, será impresso o qrcode do pix junto com o boleto para pagamento. Com ela desativada, será impresso somente o boleto."
                                              value="Gerar boleto híbrido (Boleto/Pix):"/>
                                <h:panelGroup id="panelStatusBolHibrido" style="display: block;" rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas && !ConvenioCobrancaControle.convenioCobrancaVO.novoObj}">
                                    <%--Desativado--%>
                                    <h:panelGroup id="desativado" style="display: block;"
                                                  rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas && !ConvenioCobrancaControle.gerarBoletoHibrido}">
                                        <a4j:commandLink style="background-color: #777777 !important; border-radius: 20px!important; padding: 1px 9px 1px 10px!important"
                                                         value="Desativado"
                                                         oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                         action="#{ConvenioCobrancaControle.cadastrarChavePixContaAsaas}"
                                                         title="<b>Clique para ativar!</b>"
                                                         reRender="form"
                                                         styleClass="botaoPrimario texto-size-14-real tooltipster"/>
                                    </h:panelGroup>
                                <%--Ativado--%>
                                <h:panelGroup id="ativado" style="display: block;"
                                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas && ConvenioCobrancaControle.gerarBoletoHibrido}">
                                    <a4j:commandLink style="background-color: #00c350 !important; border-radius: 20px!important; padding: 1px 9px 1px 10px!important"
                                                     value="Ativado"
                                                     title="#{ConvenioCobrancaControle.titleDesativarBoletoHibridoAsaas}"
                                                     styleClass="botaoPrimario texto-size-14-real tooltipster"/>
                                </h:panelGroup>
                            </h:panelGroup>

                                <h:panelGroup id="infoGraveConv" style="display: block;"
                                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas && ConvenioCobrancaControle.convenioCobrancaVO.novoObj}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  title="Para habilitar essa configuração, primeiro grave o convênio de cobrança."
                                                  value="Você poderá habilitar essa configuração após gravar o convênio de cobrança"/>
                                </h:panelGroup>




                                <%--Botões de consulta info Asaas--%>
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Consultar informações da conta Asaas:"/>

                                <h:panelGrid id="gridAcoesContaAsaas" style="display: grid; grid-template-columns: 1fr 1fr 1fr;" columns="3" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita" width="100%">
                                    <a4j:commandButton styleClass="botoes nvoBt btSec tooltipster"
                                                       title="Ver taxas das cobranças da sua Conta Asaas."
                                                       id="consultarTaxasContaAsaas"
                                                       value="Taxas"
                                                       reRender="modalTaxasContaAsaas"
                                                       oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                       action="#{ConvenioCobrancaControle.consultarTaxasContaAsaas}"/>
                                    <a4j:commandButton styleClass="botoes nvoBt btSec tooltipster"
                                                     title="Ver os dados comerciais da sua Conta Asaas."
                                                     id="consultarDadosContaAsaas"
                                                     value="Dados comerciais"
                                                     reRender="modalDadosComerciaisContaAsaas"
                                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                     action="#{ConvenioCobrancaControle.consultarDadosComerciaisContaAsaas}"/>
                                    <a4j:commandButton styleClass="botoes nvoBt btSec tooltipster"
                                                     title="Ver a situação cadastral da sua Conta Asaas."
                                                     id="consultarStatusContaAsaas"
                                                     value="Situação cadastral"
                                                     reRender="modalStatusContaAsaas"
                                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                     action="#{ConvenioCobrancaControle.consultarSituacaoCadastralContaAsaas}"/>
                                </h:panelGrid>

                                <%--Enviar notificações Asaas--%>
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.titleNotificoesAsaas}"
                                              value="Enviar Notificações Email/SMS para o cliente: "/>
                                <h:selectBooleanCheckbox id="adicionarData" styleClass="campos tooltipster"
                                                         title="#{ConvenioCobrancaControle.titleNotificoesAsaas}"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.enviarNotificacoes}"/>

                                <%--Aviso Asaas--%>
                                <h:outputText styleClass="tituloCamposNegrito tooltipster"
                                              value="Aviso:"/>
                                <h:outputText rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.boletoAsaas}"
                                              styleClass="tituloCamposNegrito texto-cor-vermelho tooltipster"
                                              title="Por uma limitação do próprio Asaas, no momento só será possível gerar boleto de valor superior ou igual a R$5,00"
                                              value="No momento só é possível gerar boleto de valor superior ou igual a R$5,00 para convênio Asaas"/>
                                <h:outputText rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pixAsaas}"
                                              styleClass="tituloCamposNegrito texto-cor-vermelho tooltipster"
                                              title="Por uma limitação do próprio Asaas, no momento só será possível gerar boleto de valor superior ou igual a R$5,00"
                                              value="No momento só é possível gerar pix de valor superior ou igual a R$5,00 para convênio Asaas"/>
                            </c:if>

                            <c:if test="${not empty ConvenioCobrancaControle.observacaoConvenioCobranca}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Observação:"/>
                                <h:outputText id="observacaoConvenioCobranca"
                                              styleClass="form"
                                              escape="false"
                                              value="#{ConvenioCobrancaControle.observacaoConvenioCobranca}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.apresentarOpcaoAdicionarData}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Adicionar pasta com data para buscar extratos: "/>
                                <h:selectBooleanCheckbox id="adicionarData" styleClass="campos"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.adicionarData}"/>

                                <h:outputText styleClass="tituloCampos" value="Buscar da Central da Pacto: "/>
                                <h:selectBooleanCheckbox id="buscarCentralPacto" styleClass="campos"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.buscarCentralPacto}"/>

                                <h:outputText styleClass="tituloCampos" value="Máscara de data Extrato: "/>
                                <h:inputText id="mascaraDataArquivo" size="50" maxlength="50" styleClass="form"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.mascaraDataArquivo}"/>
                            </c:if>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.tipo == 'ITAU' && ConvenioCobrancaControle.convenioCobrancaVO.banco.codigoBanco == 341}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConvenioCobranca_retornaTarifaSeparadaValorPago}"/>
                                <h:selectBooleanCheckbox id="tarifaBoletoSeparadaValorPago" styleClass="campos"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.tarifaBoletoSeparadaValorPago}"/>
                            </c:if>

                            <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes && !ConvenioCobrancaControle.convenioCobrancaVO.tipo.pix && ConvenioCobrancaControle.apresentarDiretorioExtrato
                                && !ConvenioCobrancaControle.convenioCobrancaVO.tipoNenhum && !ConvenioCobrancaControle.convenioCobrancaVO.caixaRegistroOnline}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Diretório remoto extrato: "/>
                                <h:inputText id="diretorioRemotoExtrato" size="70" maxlength="120" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioRemotoExtrato}"/>
                            </c:if>
                        </h:panelGrid>

                        <%--DADOS EXCLUSIVOS PAGBANK--%>
                        <h:panelGroup
                                rendered="#{!ConvenioCobrancaControle.convenioCobrancaVO.novoObj && ConvenioCobrancaControle.convenioCobrancaVO.pagBank}">
                            <h:panelGroup layout="block"
                                          style="padding: 3px; margin-left: 35%;">
                                <a4j:commandLink
                                        action="#{ConvenioCobrancaControle.abrirLinkAutorizacaoContaPagBank}"
                                        title="Você será redirecionado para o site da Pagbank e deverá realizar o login na sua conta Pagbank e seguir o passo a passo que será exibido lá para gerar a autorização."
                                        styleClass="tooltipster" id="liberacaoPagbank"
                                        value="#{ConvenioCobrancaControle.nomeBtnGerarAutorizacaoPagbank}"
                                        oncomplete="#{ConvenioCobrancaControle.msgAlert};fecharJanela()">
                                </a4j:commandLink>
                            </h:panelGroup>

                            <h:panelGrid id="gridFormPagBank" columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <%--ACCESS TOKEN--%>
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao01}"
                                              value="#{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao01}"/>
                                <h:inputTextarea id="accesstokenpagbank"
                                             disabled="true"
                                             style="width: 503px;height: 50px" styleClass="form tooltipster"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutorizacao01PagBank_Exibir}"
                                             title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao01}"/>
                                <%--DT.EXPIRAÇÃO ACCESS TOKEN TOKEN--%>
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              rendered="#{ConvenioCobrancaControle.exibirCampoExpiracaoAccessToken}"
                                              title="Data de expiração do Access Token da PagBank. Você pode renová-lo à qualquer momento"
                                              value="Expiração do Access Token:"/>
                                <h:outputText id="expaccesstokenpagbank"
                                              rendered="#{ConvenioCobrancaControle.exibirCampoExpiracaoAccessToken}"
                                              styleClass="tituloCampos tooltipster"
                                              value="#{ConvenioCobrancaControle.convenioCobrancaVO.dataExpiracaoAccessToken_Apresentar}"
                                              title="Data de expiração do Access Token da PagBank. Você pode renová-lo à qualquer momento"/>
                                <%--CODE--%>
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao02}"
                                              value="#{ConvenioCobrancaControle.convenioCobrancaVO.labelCodigoAutenticacao02}"/>
                                <h:inputText id="codepagbank"
                                             disabled="true"
                                             style="width: 300px;" styleClass="form tooltipster"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.codigoAutorizacao02PagBank_Exibir}"
                                             title="#{ConvenioCobrancaControle.convenioCobrancaVO.titleCodigoAutenticacao02}"/>
                            </h:panelGrid>
                        </h:panelGroup>

                    </rich:tab>

                    <rich:tab id="abaExtrato" label="Extrato (Conciliação)"
                              rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.apresentarAbaExtrato && ConvenioCobrancaControle.exibirAbaConciliacao}">

                        <h:panelGrid id="panelGridExtrato" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">

                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         headerClass="subordinado"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">

                                <f:facet name="header">
                                    <h:outputText value="Configurações para Extrato (Conciliação)"/>
                                </f:facet>

                                <h:outputText styleClass="tituloCampos" value="Utilizar Extrato:"/>
                                <h:selectBooleanCheckbox id="utilizaExtrato" styleClass="tituloCampos"
                                                         value="#{ConvenioCobrancaControle.convenioCobrancaVO.utilizaExtrato}">
                                    <a4j:support event="onchange" reRender="form"
                                                 oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                                </h:selectBooleanCheckbox>
                            </h:panelGrid>

                            <c:if test="${ConvenioCobrancaControle.convenioCobrancaVO.utilizaExtrato}">

                                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                             headerClass="subordinado"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">

                                    <f:facet name="header">
                                        <h:outputText value="Configuração Automática"/>
                                    </f:facet>

                                    <h:outputText styleClass="tituloCampos" value=""/>
                                    <h:panelGroup layout="block">
                                        <a4j:commandLink
                                                styleClass="tooltipster"
                                                title="Configurar automaticamente para o padrão da CIELO"
                                                action="#{ConvenioCobrancaControle.convenioCobrancaVO.gerarPadraoExtratoCielo}"
                                                oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                reRender="form"
                                                value="Configuração para CIELO"/>
                                        <br/>
                                        <a4j:commandLink
                                                styleClass="tooltipster"
                                                title="Configurar automaticamente para o padrão da Rede utilizando TIVIT"
                                                action="#{ConvenioCobrancaControle.convenioCobrancaVO.gerarPadraoExtratoRedeTivit}"
                                                oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                reRender="form"
                                                value="Configuração para Rede com TIVIT"/>
                                        <br/>
                                        <a4j:commandLink
                                                styleClass="tooltipster"
                                                title="Configurar automaticamente para o padrão TIVIT para usar com outras adquirentes que não seja REDE"
                                                action="#{ConvenioCobrancaControle.convenioCobrancaVO.gerarPadraoExtratoTivit}"
                                                oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                reRender="form"
                                                value="Configuração TIVIT (não Rede)"/>
                                    </h:panelGroup>
                                </h:panelGrid>


                                <h:panelGrid columns="2" width="100%"
                                             rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.vanExtratoCielo}"
                                             rowClasses="linhaImpar, linhaPar"
                                             headerClass="subordinado"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">

                                    <f:facet name="header">
                                        <h:outputText value="Termo de Aceite Cielo"/>
                                    </f:facet>

                                    <h:outputText styleClass="tituloCampos" value="Email Cadastrado na Cielo"/>
                                    <h:panelGroup>
                                        <h:inputText id="cieloClienteId" size="50" maxlength="255" styleClass="form"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.cieloClientId}"/>
                                        <h:message for="cieloClienteId" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos" value="Termo de Aceite:"/>
                                    <h:panelGrid columns="2">
                                        <a4j:commandLink
                                                id="btnCredenciamentoCielo"
                                                action="#{ConvenioCobrancaControle.envioExtratoEletronicoCielo}"
                                                reRender="form"
                                                title="O credenciamento por e-mail irá credenciar todos os contratos vinculados"
                                                value="Credenciamento"
                                                styleClass="tooltipster"/>
                                        <a4j:commandLink
                                                id="btnConsultarStatusCredenciamentoCielo"
                                                action="#{ConvenioCobrancaControle.consultarStatusCredenciamentoCielo}"
                                                reRender="form"
                                                value="Consultar Status"/>
                                    </h:panelGrid>
                                </h:panelGrid>


                                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                             headerClass="subordinado"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">

                                    <f:facet name="header">
                                        <h:outputText value="Configurações"/>
                                    </f:facet>

                                    <h:outputText styleClass="tituloCampos" value="VAN:"/>
                                    <h:selectOneMenu id="vanExtrato" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form tooltipster"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.vanExtrato}">
                                        <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemVanExtrato}"/>
                                        <a4j:support event="onchange" reRender="form"
                                                     oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                                    </h:selectOneMenu>

                                    <h:outputText styleClass="tituloCampos" value="Buscar da central Pacto:"/>
                                    <h:selectBooleanCheckbox id="obterCentralPactoExtrato"
                                                             title="Ao marcar sistema irá buscar os arquivos no SFTP da Pacto:<br/>Endereço: <b>sftp.pactosolucoes.com.br</b>"
                                                             styleClass="tituloCampos tooltipster"
                                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.obterCentralPactoExtrato}">
                                        <a4j:support event="onchange" reRender="form"
                                                     oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                     action="#{ConvenioCobrancaControle.convenioCobrancaVO.gerarPadraoCentralPactoExtrato}"/>
                                    </h:selectBooleanCheckbox>

                                    <c:if test="${!ConvenioCobrancaControle.convenioCobrancaVO.obterCentralPactoExtrato}">

                                        <h:outputText styleClass="tituloCampos" value="Host:"/>
                                        <h:inputText id="hostSFTPExtrato" size="30" maxlength="100"
                                                     styleClass="form tooltipster"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     title="Endereço do SFTP onde os arquivos serão disponibilizados"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.hostSFTPExtrato}"/>

                                        <h:outputText styleClass="tituloCampos" value="Porta:"/>
                                        <h:inputText id="portSFTPExtrato" size="5" maxlength="10"
                                                     styleClass="form tooltipster"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                                     onkeyup="somenteNumeros(this);"
                                                     title="Porta do SFTP onde os arquivos serão disponibilizados"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.portSFTPExtrato}"/>

                                        <h:outputText styleClass="tituloCampos" value="Usuário:"/>
                                        <h:inputText id="userSFTPExtrato" size="15" maxlength="50"
                                                     styleClass="form tooltipster"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     title="Usuário do SFTP onde os arquivos serão disponibilizados"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.userSFTPExtrato}"/>

                                        <h:outputText styleClass="tituloCampos" value="Senha:"/>
                                        <h:inputText id="pwdSFTPExtrato" size="15" maxlength="50"
                                                     styleClass="form tooltipster"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     title="Senha do SFTP onde os arquivos serão disponibilizados"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.pwdSFTPExtrato}"/>

                                    </c:if>

                                    <h:outputText styleClass="tituloCampos" value="Nomenclatura arquivo:"/>
                                    <h:inputText id="nomenclaturaExtrato" size="50" maxlength="100"
                                                 styleClass="form tooltipster"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 title="#{ConvenioCobrancaControle.convenioCobrancaVO.nomenclaturaExtrato_Title}"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.nomenclaturaExtrato}"/>

                                    <h:outputText styleClass="tituloCampos" value="Diretório remoto:"/>
                                    <h:inputText id="diretorioRemotoExtrato2" size="50" maxlength="100"
                                                 styleClass="form tooltipster"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 title="Diretório remoto onde os arquivos serão disponibilizados"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioRemotoExtrato}"/>

                                    <h:outputText styleClass="tituloCampos" value="Máscara data diretório remoto:"/>
                                    <h:inputText id="mascaraDataDiretorioRemotoExtrato" size="50" maxlength="100"
                                                 styleClass="form tooltipster"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 title="#{ConvenioCobrancaControle.convenioCobrancaVO.mascaraDiretorioExtrato_Title}"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.mascaraDataDiretorioRemotoExtrato}"/>

                                    <h:outputText styleClass="tituloCampos" value="Diretório local:"/>
                                    <h:panelGroup>
                                        <h:inputText id="diretorioLocalExtrato" size="50" maxlength="100"
                                                     styleClass="form tooltipster"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     title="Diretório local onde os arquivos serão salvos na infra"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioLocalExtrato}"/>

                                        <a4j:commandLink id="gerarPadraoExtrato"
                                                         value="Diretório padrão"
                                                         styleClass="tooltipster"
                                                         style="padding-left: 5px"
                                                         reRender="form"
                                                         title="Utilizar o padrão de configuração de diretório local"
                                                         oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                         action="#{ConvenioCobrancaControle.convenioCobrancaVO.gerarPadraoDiretorioLocalExtrato}">
                                        </a4j:commandLink>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos" value="Máscara data diretório local:"/>
                                    <h:inputText id="mascaraDataDiretorioLocalExtrato" size="50" maxlength="100"
                                                 styleClass="form tooltipster"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 title="#{ConvenioCobrancaControle.convenioCobrancaVO.mascaraDiretorioExtrato_Title}"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaVO.mascaraDataDiretorioLocalExtrato}"/>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="Visualizar configuração de nomenclatura e diretórios:"/>
                                    <a4j:commandLink id="verificarExemplo"
                                                     value="Visualizar configuração" styleClass="tooltipster"
                                                     reRender="panelModalExemploConvenio"
                                                     title="Visualizar a configuração do extrato com as configurações informadas."
                                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.msgAlert};fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                     action="#{ConvenioCobrancaControle.verificarExemplo}">
                                    </a4j:commandLink>

                                    <h:outputText styleClass="tituloCampos" value="Diretório local log:"/>
                                    <h:panelGroup>
                                        <h:inputText id="diretorioLocalLogExtrato" size="50" maxlength="100"
                                                     styleClass="form tooltipster"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     title="Diretório local onde os arquivos serão salvos na infra"
                                                     value="#{ConvenioCobrancaControle.convenioCobrancaVO.diretorioLocalLogExtrato}"/>

                                        <a4j:commandLink id="visualizarLogExtrato"
                                                         value="Diretório padrão extrato"
                                                         styleClass="tooltipster"
                                                         style="padding-left: 5px"
                                                         reRender="form"
                                                         title="Utilizar o padrão de configuração de diretório local para extratos"
                                                         oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                         action="#{ConvenioCobrancaControle.gerarPadraoDiretorioLocalLogExtrato}">
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="Visualizar logs de processamento do extrato:"/>
                                    <h:panelGroup>
                                    <h:inputText size="2" maxlength="5"
                                                 styleClass="form tooltipster"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                                 onkeyup="somenteNumeros(this);"
                                                 title="Quantidade máxima de linhas visualizar"
                                                 value="#{ConvenioCobrancaControle.qtdMaximaLinhasApresentarLogExtrato}"/>
                                    <a4j:commandLink id="verificarLogExtrato"
                                                     value="Visualizar log extratos" styleClass="tooltipster"
                                                     reRender="form panelModalLogsExtratoConvenio"
                                                     style="padding-left: 5px"
                                                     title="Visualizar logs de processamento de extratos."
                                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.msgAlert};fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                     action="#{ConvenioCobrancaControle.verificarLogExtrato}">
                                    </a4j:commandLink>
                                </h:panelGroup>
                                </h:panelGrid>
                            </c:if>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Empresas">
                        <h:panelGrid id="cfgEmpresas" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configurações para empresa"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         rendered="#{ConvenioCobrancaControle.podeAdicionarConfiguracaoEmpresa}"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">

                                <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                                <h:selectOneMenu id="empresaCfg" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ConvenioCobrancaControle.convenioCobrancaEmpresa.empresa.codigo}">
                                    <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemEmpresa}"/>
                                </h:selectOneMenu>


                                <h:outputText styleClass="tituloCampos" value=""/>
                                <a4j:commandLink id="adicionarTodasEmpresas"
                                                 action="#{ConvenioCobrancaControle.adicionarTodasEmpresas}"
                                                 reRender="form"
                                                 value="Adicionar Todas"/>
                            </h:panelGrid>
                            <a4j:commandButton id="addempresaCfg" action="#{ConvenioCobrancaControle.adicionarCfg}"
                                               reRender="form"
                                               rendered="#{ConvenioCobrancaControle.podeAdicionarConfiguracaoEmpresa}"
                                               value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="7" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="tabelaCfgs" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.configuracoesEmpresa}"
                                             var="cfg">

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Código"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.empresa.codigo}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Empresa"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.empresa.nome}"/>
                                    </h:column>
                                    <h:column rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pagarMe}">
                                        <f:facet name="header">
                                            <h:outputText styleClass="tooltipster"
                                                          value="Sigla Pagar.Me"
                                                          title="Esta sigla só se faz necessária caso você tenha mais de uma unidade em bancos separados, pois para se utilizar 1 portal pagar.me em 2 empresas diferentes é necessário informar essa sigla pois é o que vai diferenciar o id do aluno lá no portal para não duplicar informações. É permitido no máximo 4 caracteres."/>
                                        </f:facet>
                                        <h:inputText styleClass="tooltipster"
                                                     title="Esta sigla só se faz necessária caso você tenha mais de uma unidade em bancos separados, pois para se utilizar 1 portal pagar.me em 2 empresas diferentes é necessário informar essa sigla pois é o que vai diferenciar o id do aluno lá no portal para não duplicar informações. É permitido no máximo 4 caracteres"
                                                     maxlength="4" size="4"
                                                     onkeyup="somenteLetras(this)"
                                                     style="text-transform: uppercase"
                                                     value="#{cfg.siglaPagarMe}"/>
                                    </h:column>
                                    <h:column rendered="#{ConvenioCobrancaControle.podeAdicionarConfiguracaoEmpresa}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="removerempresaCfg" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{ConvenioCobrancaControle.removerCfg}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="tabAjusteConvenio" label="Ajuste de convênio"
                              rendered="#{ConvenioCobrancaControle.acessoAjusteConvenio && !ConvenioCobrancaControle.convenioCobrancaVO.tipoPix}">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos" value="Convênio a ser alterado:"/>
                            <h:selectOneMenu id="convenioAlterado" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{ConvenioCobrancaControle.codigoConvenioAlterado}">
                                <f:selectItems value="#{ConvenioCobrancaControle.listaConvenioAlterado}"/>
                            </h:selectOneMenu>
                            <h:outputText styleClass="tituloCampos" value="Para convênio:"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{ConvenioCobrancaControle.descricaoConvenioNovo}"/>
                        </h:panelGrid>
                        <h:panelGroup layout="block" style="overflow:auto; text-align: center">
                            <br/>
                            <h:outputText
                                    value="Obs: Caso a academia trabalhe com a opção 'Retentativa Automática Cobrança', o novo convênio deverá ser cadastrado. Essa alteração só afeta o cadastro dos clientes!"/>
                        </h:panelGroup>
                    </rich:tab>

                    <rich:tab id="tabSplitPagamento" label="Split de Pagamentos"
                              rendered="#{ConvenioCobrancaControle.apresentarAbaSplitDePagamento}">

                        <h:panelGrid id="panelRateioConvenio" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">

                            <f:facet name="header">
                                <h:panelGroup layout="block" style="padding: 5px">
                                    <h:outputText value="Rateio para recebedores cadastrados no Pagar.me" rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pagarMe or ConvenioCobrancaControle.convenioCobrancaVO.stoneV5}"/>
                                    <h:outputText value="Rateio para recebedores cadastrados no PagoLivre" rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pagoLivre}"/>
                                    <h:outputText value="Rateio para recebedores cadastrados no Fypay" rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.facilitePay}"/>

                                    <h:outputText styleClass="tooltipster"
                                                  style="border-radius: 50%; border-color: #0090FF; background:#0090FF; margin-top: 2px;
                                  color: #fcf9f9; padding: 5px 9px; height:13px; font-size:11px;cursor: help;margin-left: 5px"
                                                  title="#{ConvenioCobrancaControle.titleExplicacaoRateio}"
                                                  value="?">
                                    </h:outputText>
                                </h:panelGroup>
                            </f:facet>

                            <h:panelGroup layout="block" id="panelNovoRateio">
                                <a4j:commandButton reRender="formModalRateio"
                                                   id="btnNovoRateio"
                                                   oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                   action="#{ConvenioCobrancaControle.novoConvenioCobrancaRateio}"
                                                   value="Novo rateio"
                                                   styleClass="botoes nvoBt btSec">
                                </a4j:commandButton>
                            </h:panelGroup>

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                         rendered="#{not empty ConvenioCobrancaControle.convenioCobrancaVO.listaConvenioCobrancaRateioVO}"
                                         columnClasses="colunaCentralizada">

                                <h:dataTable id="tabelaRateio" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar"
                                             columnClasses="colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaVO.listaConvenioCobrancaRateioVO}"
                                             var="rateio">

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Código"/>
                                        </f:facet>
                                        <h:outputText value="#{rateio.codigo}"/>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Descrição"/>
                                        </f:facet>
                                        <h:outputText value="#{rateio.descricao}"/>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Padrão"/>
                                        </f:facet>
                                        <h:outputText value="#{rateio.padrao ? 'SIM' : 'NÃO'}"/>
                                    </h:column>

                                    <h:column rendered="#{ConvenioCobrancaControle.existeRateioPlano}">
                                        <f:facet name="header">
                                            <h:outputText value="Plano"/>
                                        </f:facet>
                                        <h:outputText value="#{rateio.planoVO.descricao}"/>
                                    </h:column>

                                    <h:column rendered="#{ConvenioCobrancaControle.existeRateioProduto}">
                                        <f:facet name="header">
                                            <h:outputText value="Produto"/>
                                        </f:facet>
                                        <h:outputText value="#{rateio.produtoVO.descricao}"/>
                                    </h:column>

                                    <h:column rendered="#{ConvenioCobrancaControle.existeRateioTipoProduto}">
                                        <f:facet name="header">
                                            <h:outputText value="Tipo Produto"/>
                                        </f:facet>
                                        <h:outputText value="#{rateio.tipoProduto_Apresentar}"/>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Rateio"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:repeat value="#{rateio.itens}"
                                                        var="item">
                                                <h:panelGroup layout="block" id="item"
                                                              style="width: 100%; display: inline-flex; text-align: center; padding: 2px 0 2px 0;">
                                                    <h:panelGroup layout="block" style="width: 70%; text-align: right;">
                                                        <h:outputText styleClass="tooltipster"
                                                                      title="Conta bancária: #{item.nomeRecebedor}<br/>#{ConvenioCobrancaControle.titleIdRecebedor} #{item.idRecebedor}<br/>Percentual: #{item.porcentagem_Apresentar}"
                                                                      value="#{item.nomeRecebedor}"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="width: 30%; text-align: left; padding-left: 5px;">
                                                        <h:outputText styleClass="tooltipster"
                                                                      title="Conta bancária: #{item.nomeRecebedor}<br/>#{ConvenioCobrancaControle.titleIdRecebedor} #{item.idRecebedor}<br/>Percentual: #{item.porcentagem_Apresentar}"
                                                                      value=" - #{item.porcentagem_Apresentar}"/>
                                                        <h:outputText rendered="#{item.recebedorPrincipal and ConvenioCobrancaControle.convenioCobrancaVO.stoneV5}"
                                                                      id="iconRecebedorPrincipalModal"
                                                                      title="Este é o recebedor principal"
                                                                      styleClass="fa-icon-ok-sign tooltipster" style="color: #3cdb5c; margin-left: 6px;"/>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </a4j:repeat>
                                        </h:panelGroup>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Opções"/>
                                        </f:facet>
                                        <h:panelGroup layout="block" style="text-align: center; display: inline-flex">
                                            <a4j:commandButton reRender="formModalRateio"
                                                               oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                               action="#{ConvenioCobrancaControle.editarConvenioCobrancaRateio}"
                                                               image="./imagens/botaoEditar.png"
                                                               title="Editar"
                                                               styleClass="tooltipster"/>

                                            <a4j:commandButton reRender="panelRateioConvenio"
                                                               oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                               action="#{ConvenioCobrancaControle.excluirConvenioCobrancaRateio}"
                                                               image="./imagens/botaoRemover.png"
                                                               title="Excluir"
                                                               styleClass="tooltipster"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="tabPesquisarPix" label="Pesquisar Pix"
                              action="#{ConvenioCobrancaControle.iniciarAbaPesquisarPix}"
                              rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes && (ConvenioCobrancaControle.convenioCobrancaVO.pixBB or ConvenioCobrancaControle.convenioCobrancaVO.pixBradesco
                               or ConvenioCobrancaControle.convenioCobrancaVO.pixSantander or ConvenioCobrancaControle.convenioCobrancaVO.pixInter or ConvenioCobrancaControle.convenioCobrancaVO.pixItau)}">

                        <h:panelGroup styleClass="tituloCampos">
                            <h:outputText styleClass="tituloCamposNegritoMaior"
                                          value="Recurso de uso interno da Pacto"/>
                            </br>
                            <h:outputText
                                    value="Aqui é possível consultar o status de um pix na API do banco pesquisando por txId ou por e2eid (EndToEndId)."/>
                            </br>
                            <h:outputText
                                    value="Utilize o txId do pix ou o e2eid para obter informações em tempo real de como está a situação do pix lá na API do banco."/>
                            </br>
                            <h:outputText style="text-align:justify;" value="Geralmente o e2eid é usado em casos onde o aluno possui o comprovante de pagamento do pix mas no sistema o pix não deu baixa. Todo comprovante
                            de pagamento de pix tem o identificador e2eid (não é a mesma coisa que txId). Após consultar por e2eid, verifique se também tem o parâmetro txId no retorno. Se tiver o txId no retorno
                             é porque o aluno pagou o qrcode gerado pelo nosso sistema, agora se não tiver o txId (tem só o parâmetro e2eid) significa que esse aluno fez um pix direto para a conta bancária
                              do cliente via chave do pix."/>
                            </br></br>
                            <h:outputText styleClass="tituloCamposNegrito"
                                          rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.pixSantander}"
                                          value="Convênio de pix Santander não permite consultar pix por e2eid pois eles não possuem esse endpoint."/>
                            </br>
                            <h:outputText styleClass="tituloCamposNegrito"
                                          value="Se o botão \"Consultar Pix\" não funcionar, tente dar um F5 na página e refazer a consulta."/>
                        </h:panelGroup>
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText value="Consultar por:"/>
                            <h:panelGroup>
                                <h:selectOneRadio id="opcoesPesquisaPix"
                                                  onblur="blurinput(this);" onfocus="focusinput(this);"
                                                  styleClass="tituloCampos" style="border: none;"
                                                  value="#{ConvenioCobrancaControle.tipoConsultaPix}">
                                    <f:selectItems
                                            value="#{ConvenioCobrancaControle.listaTipoConsultaPix}"/>
                                    <a4j:support event="onclick" ajaxSingle="true"
                                                 reRender="labelTipoConsultaPix"/>
                                </h:selectOneRadio>
                            </h:panelGroup>
                            <h:panelGroup id="labelTipoConsultaPix">
                                <c:if test="${ConvenioCobrancaControle.tipoConsultaPix == 'txId'}">
                                    <h:outputText styleClass="tituloCampos" value="txId:"/>
                                </c:if>
                                <c:if test="${ConvenioCobrancaControle.tipoConsultaPix == 'e2eid (EndToEndId)'}">
                                    <h:outputText styleClass="tituloCampos" value="e2eid (EndToEndId):"/>
                                </c:if>
                            </h:panelGroup>
                            <h:inputText id="campoInfoPixPesquisar" size="50" maxlength="100"
                                         styleClass="form"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         value="#{ConvenioCobrancaControle.infoPixPesquisar}"/>
                        </h:panelGrid>
                        <a4j:commandButton id="consultarPix"
                                           reRender="formPix"
                                           style="margin-left: 35%;"
                                           action="#{ConvenioCobrancaControle.consultarPix}"
                                           value="Consultar Pix" styleClass="botoes nvoBt btSec btPerigo"
                                           oncomplete="#{ConvenioCobrancaControle.onComplete};#{ConvenioCobrancaControle.mensagemNotificar}"/>
                    </rich:tab>

                    <rich:tab id="tabWebhookPix" label="Webhook"
                              reRender="modalWebhookAtivado"
                              action="#{ConvenioCobrancaControle.iniciarAbaWebhookPix}"
                              oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}#{ConvenioCobrancaControle.msgAlert}"
                              rendered="#{ConvenioCobrancaControle.apresentarAbaPixWebhook}">

                        <h:panelGroup styleClass="tituloCampos">
                            <h:outputText styleClass="tituloCamposNegritoMaior"
                                          style="display: flex; margin-left: 25px;"
                                          value="Gerenciamento de Webhook do Pix"/>
                        </h:panelGroup>

                        <%--STATUS HABILITADO--%>
                        <h:panelGrid id="painelStatusWebhookAtivado"
                                     style="display: inline-flex;"
                                     rendered="#{ConvenioCobrancaControle.possuiWebhookPixAtivado}">
                            <h:panelGrid width="100%" columns="2" cellpadding="10"
                                         style="margin-top: 20px; margin-left: 10px; display: block;">
                                <h:outputText styleClass="texto-size-18 cinza"
                                              value="Status atual: "> </h:outputText>
                                <h:panelGroup id="webhookHabilitado" style="margin-right: 16px; display: block;">
                                    <a4j:commandLink
                                            style="background-color: #00c350 !important; border-radius: 20px!important; cursor: default"
                                            value="Habilitado"
                                            styleClass="botaoPrimario texto-size-14-real tooltipster"
                                            title="O Webhook para este convênio está habilitado."/>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>


                        <%--STATUS DESABILITADO--%>
                        <h:panelGrid id="painelStatusWebhookDesativado"
                                     style="display: inline-flex;"
                                     rendered="#{!ConvenioCobrancaControle.possuiWebhookPixAtivado}">
                            <h:panelGrid width="100%" columns="2" cellpadding="10"
                                         style="margin-top: 20px; margin-left: 10px">
                                <h:outputFormat styleClass="texto-size-18 cinza"
                                              value="Status atual: "> </h:outputFormat>
                                <h:panelGroup id="webhookDesabilitado" style="margin-right: 16px; display: block;">
                                    <a4j:commandLink style="background-color: #777777 !important; border-radius: 20px!important; cursor: default"
                                            value="Desabilitado"
                                            styleClass="botaoPrimario texto-size-14-real tooltipster"
                                            title="O Webhook para este convênio de cobrança ainda não está habilitado. </br> Utilize o botão 'Habilitar' para configurar uma nova URL de Callback automaticamente lá no banco"/>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>

                        <%--BOTÕES--%>
                    <h:panelGroup id="panelGeraisPix" style="margin-left: 19px; display: block; margin-top: 15px;">
                        <a4j:commandButton
                                rendered="#{!ConvenioCobrancaControle.possuiWebhookPixAtivado}"
                                title="Clique para configurar automaticamente a URL de callback lá no banco"
                                action="#{ConvenioCobrancaControle.configurarUrlCallbackPix}"
                                reRender="form"
                                id="btnHabilitarWebhookAtivado"
                                value="Habilitar"
                                styleClass="botoes nvoBt btSec btPerigo tooltipster"
                                oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}#{ConvenioCobrancaControle.msgAlert}"/>
                        <a4j:commandButton
                                rendered="#{ConvenioCobrancaControle.possuiWebhookPixAtivado}"
                                title="Consultar URL de callback configurada lá no banco"
                                action="#{ConvenioCobrancaControle.abrirModalWebhookAtivado}"
                                reRender="form, modalWebhookAtivado"
                                value="Consultar Configuração"
                                id="btnConsultarWebhookAtivado"
                                styleClass="botoes nvoBt btSec btPerigo tooltipster"
                                oncomplete="#{ConvenioCobrancaControle.abrirFecharModalWebhookAtivado}"/>
                        <a4j:commandButton
                                title="Consultar os Webhooks recebidos pelo banco"
                                reRender="logPixWebhook"
                                id="btnConsultarWebhookTableAtivado"
                                action="#{ConvenioCobrancaControle.consultarPixWebhook}"
                                value="Consultar Webhooks Recebidos"
                                styleClass="botoes nvoBt btSec btPerigo tooltipster"
                                oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}#{ConvenioCobrancaControle.abrirFecharModalLogPixWebhook}"/>
                        <a4j:commandButton
                                rendered="#{ConvenioCobrancaControle.possuiWebhookPixAtivado}"
                                title="Excluir URL de callback configurada lá no banco"
                                reRender="form"
                                id="btnExcluirWebhookAtivado"
                                action="#{ConvenioCobrancaControle.excluirUrlCallbackPix}"
                                value="Excluir"
                                style="background-color: #BC2525"
                                styleClass="botoes nvoBt botaoExcluir btPerigo tooltipster"
                                oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"/>
                        <a4j:commandLink action="#{ConvenioCobrancaControle.realizarConsultaLogWebhookPix}"
                                         style="color: #6F747B; background: #E5E5E5; margin-top: 5px; display: inline-grid; width: 10px; height: 16px;"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                         title="Visualizar Log do cadastro de webhook do pix" styleClass="botoes nvoBt btSec tooltipster">
                            <i class="fa-icon-list"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                    </rich:tab>
                    <rich:tab id="tabWebhookBoleto" label="Webhook"
                              reRender="modalWebhookAtivado"
                              action="#{ConvenioCobrancaControle.iniciarAbaWebhookBoleto}"
                              oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}#{ConvenioCobrancaControle.msgAlert}"
                              rendered="#{ConvenioCobrancaControle.apresentarAbaWebhookBoleto}">

                        <h:panelGroup styleClass="tituloCampos">
                            <h:outputText styleClass="tituloCamposNegritoMaior"
                                          style="display: flex; margin-left: 25px;"
                                          value="Gerenciamento de Webhook do Boleto para Empresa: #{CondicaoPagamentoControle.empresaLogado.codigo} - #{CondicaoPagamentoControle.empresaLogado.nome}"/>
                        </h:panelGroup>

                        <%--STATUS HABILITADO--%>
                        <h:panelGrid id="painelStatusWebhookAtivado2"
                                     style="display: inline-flex;"
                                     rendered="#{ConvenioCobrancaControle.possuiWebhookPixAtivado}">
                            <h:panelGrid width="100%" columns="2" cellpadding="10"
                                         style="margin-top: 20px; margin-left: 10px; display: block;">
                                <h:outputText styleClass="texto-size-18 cinza"
                                              value="Status atual: "> </h:outputText>
                                <h:panelGroup id="webhookHabilitado2" style="margin-right: 16px; display: block;">
                                    <a4j:commandLink
                                            style="background-color: #00c350 !important; border-radius: 20px!important; cursor: default"
                                            value="Habilitado"
                                            styleClass="botaoPrimario texto-size-14-real tooltipster"
                                            title="O Webhook para este convênio está habilitado."/>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>


                        <%--STATUS DESABILITADO--%>
                        <h:panelGrid id="painelStatusWebhookDesativado2"
                                     style="display: inline-flex;"
                                     rendered="#{!ConvenioCobrancaControle.possuiWebhookPixAtivado}">
                            <h:panelGrid width="100%" columns="2" cellpadding="10"
                                         style="margin-top: 20px; margin-left: 10px">
                                <h:outputFormat styleClass="texto-size-18 cinza"
                                                value="Status atual: "> </h:outputFormat>
                                <h:panelGroup id="webhookDesabilitado2" style="margin-right: 16px; display: block;">
                                    <a4j:commandLink style="background-color: #777777 !important; border-radius: 20px!important; cursor: default"
                                                     value="Desabilitado"
                                                     styleClass="botaoPrimario texto-size-14-real tooltipster"
                                                     title="O Webhook para este convênio de cobrança ainda não está habilitado. </br> Utilize o botão 'Habilitar' para configurar uma nova URL de Callback automaticamente lá no banco"/>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>

                        <%--BOTÕES--%>
                        <h:panelGroup id="panelGeraisBoleto" style="margin-left: 19px; display: block; margin-top: 15px;">
                            <a4j:commandButton
                                    rendered="#{!ConvenioCobrancaControle.possuiWebhookPixAtivado}"
                                    title="Clique para configurar automaticamente a URL de callback lá no banco"
                                    action="#{ConvenioCobrancaControle.configurarUrlCallbackBoleto}"
                                    reRender="form"
                                    id="btnHabilitarWebhookAtivado2"
                                    value="Habilitar"
                                    styleClass="botoes nvoBt btSec btPerigo tooltipster"
                                    oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}#{ConvenioCobrancaControle.msgAlert}"/>
                            <a4j:commandButton
                                    rendered="#{ConvenioCobrancaControle.possuiWebhookPixAtivado}"
                                    title="Consultar URL de callback configurada lá no banco"
                                    action="#{ConvenioCobrancaControle.abrirModalWebhookAtivado}"
                                    reRender="form, modalWebhookAtivado"
                                    value="Consultar Configuração"
                                    id="btnConsultarWebhookAtivado2"
                                    styleClass="botoes nvoBt btSec btPerigo tooltipster"
                                    oncomplete="#{ConvenioCobrancaControle.abrirFecharModalWebhookAtivado}"/>
                            <a4j:commandButton
                                    rendered="#{ConvenioCobrancaControle.possuiWebhookPixAtivado}"
                                    title="Excluir URL de callback configurada lá no banco"
                                    reRender="form"
                                    id="btnExcluirWebhookAtivadoBoleto"
                                    action="#{ConvenioCobrancaControle.excluirUrlCallbackBoleto}"
                                    value="Excluir"
                                    style="background-color: #BC2525"
                                    styleClass="botoes nvoBt botaoExcluir btPerigo tooltipster"
                                    oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}"/>
                        </h:panelGroup>
                    </rich:tab>
                </rich:tabPanel>

                <h:inputHidden value="#{ConvenioCobrancaControle.extratoEletronicoCielo}"
                               id="retornoExtratoEletronicoCielo"/>

                <h:panelGrid id="tabMensagensConvenio" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{ConvenioCobrancaControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ConvenioCobrancaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ConvenioCobrancaControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{ConvenioCobrancaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ConvenioCobrancaControle.novo}"
                                               value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                               styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar"
                                               action="#{ConvenioCobrancaControle.salvar}"
                                               reRender="form, modalTokenOperacao"
                                               oncomplete="#{ConvenioCobrancaControle.mensagemNotificar}#{ConvenioCobrancaControle.msgAlert}#{ConvenioCobrancaControle.abrirFecharModalTokenOperacao}"
                                               value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                               styleClass="botoes nvoBt"/>
                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.codigo > 0}"
                                                   oncomplete="#{ConvenioCobrancaControle.msgAlert}"
                                                   action="#{ConvenioCobrancaControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true"
                                               action="#{ConvenioCobrancaControle.inicializarConsultar}"
                                               value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                               accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink action="#{ConvenioCobrancaControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                             style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="modalExemploConvenio" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Exemplo"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalExemploConvenio"/>
                <rich:componentControl for="modalExemploConvenio" attachTo="hidelinkModalExemploConvenio"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <h:panelGroup id="panelModalExemploConvenio" layout="block" style="padding: 15px">
            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                          escape="false"
                          value="#{ConvenioCobrancaControle.exemplo}"/>
        </h:panelGroup>
    </rich:modalPanel>

    <rich:modalPanel id="modalLogsExtratoConvenio" styleClass="novaModal" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Logs Processamento Extrato"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalLogsExtratoConvenio"/>
                <rich:componentControl for="modalLogsExtratoConvenio" attachTo="hidelinkModalLogsExtratoConvenio"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <h:panelGroup id="panelModalLogsExtratoConvenio" layout="block" style="overflow-x: auto; overflow-y: auto; width: 900px; height: 500px;">
            <c:forEach items="${ConvenioCobrancaControle.logsExtrato}" var="logExtrato">
                        <span class="texto-font texto-size-14-real texto-cor-cinza texto-bold">${logExtrato}</span><br/>
            </c:forEach>
        </h:panelGroup>
    </rich:modalPanel>

    <rich:modalPanel id="modalCriarOperadora" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Operadora de cartão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalCriarOperadora"/>
                <rich:componentControl for="modalCriarOperadora" attachTo="hidelinkModalCriarOperadora" operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalCriarOperadora" ajaxSubmit="true">
            <h:panelGroup layout="block" style="padding: 10px" id="panelGeralModalCriarOperadora">

                <h:panelGroup layout="block" id="panelCriarOperadora"
                              style="padding-top: 10px;padding-bottom: 15px;">
                    <h:outputText
                            value="Operadoras de cartão disponíveis para #{ConvenioCobrancaControle.convenioCobrancaVO.tipo.descricao}:"
                            styleClass="texto-size-20 cinza"/>
                </h:panelGroup>

                <h:panelGroup layout="block" style="max-height: 300px; overflow-y: auto"
                              rendered="#{not empty ConvenioCobrancaControle.listaOperadoraCriarAutomatico}">
                    <rich:dataTable styleClass="tabelaDados semZebra"
                                    id="tblOperadoras"
                                    style="width: 100%;margin: 0;"
                                    value="#{ConvenioCobrancaControle.listaOperadoraCriarAutomatico}"
                                    var="ope" rows="20" rowKeyVar="status">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="CÓD."/>
                            </f:facet>
                            <h:outputText value="#{ope.codigo}"
                                          styleClass="tooltipster"
                                          title="Código da operadora.<br/>Caso esteja 0 é uma operadora nova que será incluída.<br/>
                                         Caso esteja preenchido, a operadora já está cadastrada e poderá ser editada por aqui mesmo."/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="DESCRIÇÃO"/>
                            </f:facet>
                            <h:inputText value="#{ope.descricao}"
                                         size="30" maxlength="100"
                                         styleClass="tooltipster"
                                         title="Descrição da operadora"/>
                        </rich:column>

                        <rich:column style="text-align: center">
                            <f:facet name="header">
                                <h:outputText value="ATIVA"/>
                            </f:facet>
                            <h:selectBooleanCheckbox value="#{ope.ativo}"
                                                     styleClass="form tooltipster"
                                                     title="Situação da operadora"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Qtd. Parcelas"
                                              styleClass="tooltipster"
                                              title="Quantidade máxima de parcelas"/>
                            </f:facet>
                            <h:inputText size="2" maxlength="3"
                                         styleClass="form tooltipster"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                         onkeyup="somenteNumeros(this);"
                                         title="Quantidade máxima de parcelas"
                                         value="#{ope.qtdeMaxParcelas}"/>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalCriarOperadora"
                              style="text-align: center; padding: 40px 0 30px 0px;">
                    <a4j:commandLink id="confirmarCriarOperadorasAutomatico"
                                     value="GRAVAR"
                                     action="#{ConvenioCobrancaControle.confirmarCriarOperadorasAutomatico}"
                                     oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalRateio" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="700">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Rateio para recebedores"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalRateio"/>
                <rich:componentControl for="modalRateio" attachTo="hidelinkModalRateio" operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalRateio">
            <h:panelGroup layout="block" style="padding: 5px" id="panelModalRateio">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <f:facet name="header">
                        <h:outputText value="Rateio"/>
                    </f:facet>

                    <h:outputText styleClass="tituloCampos"
                                  value="Descrição:"/>
                    <h:inputText size="50" maxlength="95" styleClass="inputAntigo"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 value="#{ConvenioCobrancaControle.convenioCobrancaRateioVO.descricao}"/>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Você pode definir este rateio como padrão para todos os recebimentos.
                                  <br/>Caso o sistema, no momento do recebimento da parcela, não encontre nenhuma
                                  <br/>condição de Plano, Produto ou Tipo Produto configurados nos rateios definidos,
                                  <br/>então irá pegar este rateio como padrão. Só é possível definir 1 rateio padrão."
                                  value="Padrão para todos recebimentos:"/>
                    <h:selectBooleanCheckbox styleClass="tooltipster"
                                             title="Você pode definir este rateio como padrão para todos os recebimentos.
                                             <br/>Caso o sistema, no momento do recebimento da parcela, não encontre nenhuma
                                             <br/>condição de Plano, Produto ou Tipo Produto configurados nos rateios definidos,
                                             <br/>então irá pegar este rateio como padrão. Só é possível definir 1 rateio padrão."
                                             value="#{ConvenioCobrancaControle.convenioCobrancaRateioVO.padrao}"/>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Exibe as empresas que foram definidas dentro do convênio de cobrança.
                                  <br/>Caso não esteja aparecendo alguma, é necessário primeiro colocá-la no
                                  <br/>convênio de cobrança, na aba \"Empresas\"."
                                  value="Empresa:"/>
                    <h:panelGroup>
                        <h:selectOneMenu value="#{ConvenioCobrancaControle.convenioCobrancaRateioVO.empresaVO.codigo}">
                            <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemEmpresaRateio}"/>
                            <a4j:support event="onchange"
                                         reRender="panelModalRateio"
                                         action="#{ConvenioCobrancaControle.carregarPlanos}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Caso queira que este rateio seja válido para um plano específico, defina-o aqui."
                                  value="Plano:"/>
                    <h:panelGroup>
                        <h:selectOneMenu value="#{ConvenioCobrancaControle.convenioCobrancaRateioVO.planoVO.codigo}">
                            <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemPlano}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Caso queira que este rateio seja válido para um produto específico, defina-o aqui."
                                  value="Produto:"/>
                    <h:panelGroup>
                        <h:selectOneMenu value="#{ConvenioCobrancaControle.convenioCobrancaRateioVO.produtoVO.codigo}">
                            <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemProduto}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Caso queira que este rateio seja válido para um tipo de produto específico, defina-o aqui."
                                  value="Tipo Produto:"/>
                    <h:panelGroup>
                        <h:selectOneMenu value="#{ConvenioCobrancaControle.convenioCobrancaRateioVO.tipoProduto}">
                            <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemTipoProduto}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <f:facet name="header">
                        <h:outputText value="Item Rateio"/>
                    </f:facet>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="#{ConvenioCobrancaControle.titleRecebedoresRateio}"
                                  value="Recebedor:"/>
                    <h:panelGroup>
                        <h:selectOneMenu
                                value="#{ConvenioCobrancaControle.convenioCobrancaRateioItemVO.idRecebedor}">
                            <f:selectItems value="#{ConvenioCobrancaControle.listaSelectItemRecebedores}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Você só precisa informar a vírgula em casos de valores quebrados..EX: 1,5 | 0,562 | 10,52</br> Agora se você quer valores inteiros, basta informar o valor sem vígula..Ex: 10, 50, 80"
                                  value="Percentual:"/>
                    <h:inputText size="6" maxlength="6"
                                 onkeypress="return formatarPercentual(event, this);"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="inputAntigo tooltipster"
                                 title="Você só precisa informar a vírgula em casos de valores quebrados..EX: 1,5 | 0,562 | 10,52</br> Agora se você quer valores inteiros, basta informar o valor sem vígula..Ex: 10, 50, 80"
                                 value="#{ConvenioCobrancaControle.convenioCobrancaRateioItemVO.porcentagem}">
                        <f:converter converterId="FormatadorNumerico3Casa"/>
                    </h:inputText>

                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="#{ConvenioCobrancaControle.titleRecebedorPrincipalSplit}"
                                  rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.stoneV5}"
                                  value="Recebedor Principal:"/>
                    <h:selectBooleanCheckbox id="apresentarInativoPactoPay"
                                             rendered="#{ConvenioCobrancaControle.convenioCobrancaVO.stoneV5}"
                                             styleClass="tituloCampos tooltipster"
                                             title="#{ConvenioCobrancaControle.titleRecebedorPrincipalSplit}"
                                             value="#{ConvenioCobrancaControle.convenioCobrancaRateioItemVO.recebedorPrincipal}">
                    </h:selectBooleanCheckbox>

                    <h:outputText styleClass="tituloCampos"
                                  value=""/>
                    <a4j:commandButton reRender="formModalRateio"
                                       oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                       action="#{ConvenioCobrancaControle.gravarConvenioCobrancaRateioItem}"
                                       image="./imagens/botaoAdicionar.png"
                                       title="Adicionar Item"
                                       styleClass="tooltipster"/>
                </h:panelGrid>

                <h:panelGroup layout="block" style="max-height: 300px; overflow-y: auto"
                              rendered="#{not empty ConvenioCobrancaControle.convenioCobrancaRateioVO.itens}">
                    <h:dataTable width="100%" headerClass="subordinado"
                                 styleClass="tabFormSubordinada"
                                 rowClasses="linhaImpar, linhaPar"
                                 columnClasses="colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                 value="#{ConvenioCobrancaControle.convenioCobrancaRateioVO.itens}"
                                 var="item">

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome"/>
                            </f:facet>
                            <h:outputText value="#{item.nomeRecebedor}"/>
                            <h:outputText rendered="#{item.recebedorPrincipal and ConvenioCobrancaControle.convenioCobrancaVO.stoneV5}"
                                          id="iconRecebedorPrincipal"
                                          title="Este é o recebedor principal"
                                          styleClass="fa-icon-ok-sign tooltipster" style="color: #3cdb5c; margin-left: 6px;"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Percentual"/>
                            </f:facet>
                            <h:outputText value="#{item.porcentagem_Apresentar}"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Opções"/>
                            </f:facet>
                            <h:panelGroup layout="block">
                                <a4j:commandButton reRender="panelModalRateio"
                                                   oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                   action="#{ConvenioCobrancaControle.editarConvenioCobrancaRateioItem}"
                                                   image="./imagens/botaoEditar.png"
                                                   title="Editar"
                                                   styleClass="tooltipster"/>

                                <a4j:commandButton reRender="panelModalRateio"
                                                   style="padding-left: 10px"
                                                   oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                                                   action="#{ConvenioCobrancaControle.excluirConvenioCobrancaRateioItem}"
                                                   image="./imagens/botaoRemover.png"
                                                   title="Excluir"
                                                   styleClass="tooltipster"/>
                            </h:panelGroup>

                        </h:column>
                    </h:dataTable>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalRateio"
                              style="text-align: center; padding: 40px 0 30px 0px;">
                    <a4j:commandLink id="gravarConvenioCobrancaRateio"
                                     value="Salvar rateio"
                                     reRender="form:panelRateioConvenio"
                                     action="#{ConvenioCobrancaControle.gravarConvenioCobrancaRateio}"
                                     oncomplete="#{ConvenioCobrancaControle.onComplete};#{ConvenioCobrancaControle.mensagemNotificar};"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalCredencialPJBank" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações Credencial PJBank"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalCredencialPJBank"/>
                <rich:componentControl for="modalCredencialPJBank" attachTo="hidelinkModalCredencialPJBank"
                                       operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalCredencialPJBank">
            <h:panelGrid id="panelGeralModalCredencialPJBank" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="CNPJ:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.credencialPJBankDTO.cnpjApresentar}"/>

                <h:outputText styleClass="tituloCampos" value="Tipo:"/>
                <h:outputText styleClass="tituloCampos" value="#{ConvenioCobrancaControle.credencialPJBankDTO.tipo}"/>

                <h:outputText styleClass="tituloCampos" value="Nome Empresa:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.credencialPJBankDTO.nome_empresa}"/>

                <h:outputText styleClass="tituloCampos" value="Nome Fantasia:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.credencialPJBankDTO.nome_fantasia}"/>

                <h:outputText styleClass="tituloCampos" value="Status Conta:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.credencialPJBankDTO.status_conta}"/>

                <h:outputText styleClass="tituloCampos" value="Tarifa:"/>
                <h:outputText styleClass="tituloCampos" value="#{ConvenioCobrancaControle.credencialPJBankDTO.tarifa}"/>

                <h:outputText styleClass="tituloCampos" value="Banco:"/>
                <h:outputText styleClass="tituloCampos" value="#{ConvenioCobrancaControle.credencialPJBankDTO.banco}"/>

                <h:outputText styleClass="tituloCampos" value="Agência:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.credencialPJBankDTO.agencia}"/>

                <h:outputText styleClass="tituloCampos" value="Conta:"/>
                <h:outputText styleClass="tituloCampos" value="#{ConvenioCobrancaControle.credencialPJBankDTO.conta}"/>

                <h:outputText styleClass="tituloCampos" value="E-mail:"/>
                <h:outputText styleClass="tituloCampos" value="#{ConvenioCobrancaControle.credencialPJBankDTO.email}"/>

                <h:outputText styleClass="tituloCampos" value="Telefone:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.credencialPJBankDTO.telefone}"/>

                <h:outputText styleClass="tituloCampos" value="Endereço:"/>
                <h:panelGroup layout="block" style="display: grid; justify-items: left;">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{ConvenioCobrancaControle.credencialPJBankDTO.endereco}"/>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{ConvenioCobrancaControle.credencialPJBankDTO.bairro}"/>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{ConvenioCobrancaControle.credencialPJBankDTO.cep}"/>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{ConvenioCobrancaControle.credencialPJBankDTO.cidade} - #{ConvenioCobrancaControle.credencialPJBankDTO.estado}"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <%--Modal Taxas Conta Asaas--%>
    <rich:modalPanel id="modalTaxasContaAsaas" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Taxas de cobranças Asaas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalTaxasAsaas"/>
                <rich:componentControl for="modalTaxasContaAsaas" attachTo="hidelinkModalTaxasAsaas"
                                       operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalTaxasContaAsaas">
            <h:panelGrid id="panelGeralModalTaxasContaAsaas" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="Boleto Pago:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.taxasContaAsaasDTO.payment.taxasBoletoApresentar}"/>
                <h:outputText styleClass="tituloCampos" value="Pix Pago:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.taxasContaAsaasDTO.payment.taxasPixApresentar}"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="#{ConvenioCobrancaControle.titleTaxaNotificacoesAsaas}"
                              value="Notificações:"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="#{ConvenioCobrancaControle.titleTaxaNotificacoesAsaas}"
                              value="#{ConvenioCobrancaControle.taxasContaAsaasDTO.taxasNotificacoesApresentar}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <%--Modal Situação Cadastral Conta Asaas--%>
    <rich:modalPanel id="modalStatusContaAsaas" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Situação cadastral da Conta Asaas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalStatusAsaas"/>
                <rich:componentControl for="modalStatusContaAsaas" attachTo="hidelinkModalStatusAsaas"
                                       operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalStatusContaAsaas">
            <h:panelGrid id="panelGeralModalStatusContaAsaas" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="ID:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.situacaoCadastralContaAsaasDTO.id}"/>

                <h:outputText styleClass="tituloCampos" value="Informações Comerciais:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.situacaoCadastralContaAsaasDTO.commercialInfo}"/>

                <h:outputText styleClass="tituloCampos" value="Informações Conta Bancária:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.situacaoCadastralContaAsaasDTO.bankAccountInfo}"/>

                <h:outputText styleClass="tituloCampos" value="Documentação:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.situacaoCadastralContaAsaasDTO.documentation}"/>

                <h:outputText styleClass="tituloCampos" value="Geral:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.situacaoCadastralContaAsaasDTO.general}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%--Modal Dados Comerciais Conta Asaas--%>
    <rich:modalPanel id="modalDadosComerciaisContaAsaas" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados Comerciais da Conta Asaas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalDadosAsaas"/>
                <rich:componentControl for="modalDadosComerciaisContaAsaas" attachTo="hidelinkModalDadosAsaas"
                                       operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalDadosContaAsaas">
            <h:panelGrid id="panelGeralModalDadosContaAsaas" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="Status:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.status}"/>

                <h:outputText styleClass="tituloCampos" value="CNPJ:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.cpfCnpj}"/>

                <h:outputText styleClass="tituloCampos" value="Nome da Empresa:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.name}"/>

                <h:outputText styleClass="tituloCampos" value="Tipo da Empresa:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.companyType}"/>

                <h:outputText styleClass="tituloCampos" value="Nome do propietário:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.name}"/>

                <h:outputText styleClass="tituloCampos" value="Tipo Pessoa:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.personType}"/>

                <h:outputText styleClass="tituloCampos" value="Email:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.email}"/>

                <h:outputText styleClass="tituloCampos" value="Telefone:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.phone}"/>

                <h:outputText styleClass="tituloCampos" value="Celular:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.mobilePhone}"/>

                <h:outputText styleClass="tituloCampos" value="Endereço:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.address}"/>

                <h:outputText styleClass="tituloCampos" value="Bairro:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.province}"/>

                <h:outputText styleClass="tituloCampos" value="CEP:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.postalCode}"/>

                <h:outputText styleClass="tituloCampos" value="Cidade:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{ConvenioCobrancaControle.dadosComerciaisContaAsaasDTO.city}"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="modalResultadoPix" autosized="true"
                     rendered="#{ConvenioCobrancaControle.possuiRespostaConsultaPix}"
                     shadowOpacity="true" width="900" height="400"
                     styleClass="novaModal" onbeforeshow="formatJSONPix()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações do Pix"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalResultadoPix"/>
                <rich:componentControl for="modalResultadoPix"
                                       attachTo="hidemodalResultadoPix" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formPix" ajaxSubmit="true">
            <h:inputHidden id="jsonConsultaPixJSON" value="#{ConvenioCobrancaControle.jsonConsultaPixResposta}"/>
            <pre id="json" style="white-space: pre-wrap"></pre>
        </a4j:form>
    </rich:modalPanel>
    <jsp:include page="includes/conveniocobranca/modal_desbloqueio_cobrancas_automaticas.jsp" flush="true"/>
    <jsp:include page="includes/modal_token_operacao.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_token_operacao_explicacao.jsp" flush="true"/>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>

    <rich:modalPanel id="modalWebhookAtivado" autosized="true"
                     shadowOpacity="true" width="900" height="400"
                     styleClass="novaModal" onbeforeshow="formatJSONWebhookAtivadoPix()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informações do Webhook"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalWebhookAtivado"/>
                <rich:componentControl for="modalWebhookAtivado"
                                       attachTo="hidemodalWebhookAtivado" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formWebhookPixAtivado" ajaxSubmit="true">
            <h:inputHidden id="jsonWebhookPixAtivadoHidden" value="#{ConvenioCobrancaControle.jsonWebhookPixAtivadoResposta}"/>
            <pre id="jsonWebhookPixAtivado" style="white-space: pre-wrap"></pre>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="logPixWebhook" autosized="true"
                     shadowOpacity="true" width="900" height="400"
                     styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Webhooks Recebidos"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalLogPixWebhook"/>
                <rich:componentControl for="logPixWebhook"
                                       attachTo="hidemodalLogPixWebhook" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formLogPixWebhook" ajaxSubmit="true">
            <h:outputText id="jsonLogPixWebhookHidden" value="#{ConvenioCobrancaControle.pixWebhookString}"/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalPropagandaFaciliteConciliacaoConv" styleClass="novaModal noMargin" shadowOpacity="true"
                     width="800" height="675">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Fypay"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalPropagandaFaciliteConciliacaoConv"/>
                <rich:componentControl for="modalPropagandaFaciliteConciliacaoConv" attachTo="hidemodalPropagandaFaciliteConciliacaoConv"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block" style="margin: 10 20 10 20;">
            <a href="https://fypay.com.br" target="_blank" >
                <img border="none" class="img-responsive imagemApresentacao" src="images/FACILITEPAY_CONCILIACAO_CARTAO.jpg"/>
            </a>
        </h:panelGroup>
    </rich:modalPanel>

    <rich:modalPanel id="modalPropagandaFaciliteStoneConnect" styleClass="novaModal noMargin" shadowOpacity="true"
                     width="700" height="600">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Fypay"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidemodalPropagandaFaciliteStoneConnect"/>
                <rich:componentControl for="modalPropagandaFaciliteStoneConnect" attachTo="hidemodalPropagandaFaciliteStoneConnect"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block" style="margin: 10 20 10 20;">
            <a href="https://fypay.com.br" target="_blank" >
                <img border="none" class="img-responsive imagemApresentacao" src="images/FACILITEPAY_STONE_CONNECT.jpg"/>
            </a>
        </h:panelGroup>
    </rich:modalPanel>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
    jQuery('.tooltipster').tooltipster({
        theme: 'tooltipster-light',
        position: 'bottom',
        animation: 'grow',
        contentAsHTML: true
    });

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

    function copiar(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Link copiado para a área de transferência!');
    }

    function copiarCredencial(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Credencial copiada para a área de transferência!');
    }

    function somenteLetras(num) {
        var er = /[^a-zA-Z]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

    function formatJSONPix() {
        try {
            document.getElementById("json").innerHTML = JSON.stringify(JSON.parse(document.getElementById("formPix:jsonConsultaPixJSON").value), null, 3);
        } catch (ex) {
            console.log(ex);
        }
    }

    function formatJSONWebhookAtivadoPix() {
        try {
            // O JSON já vem formatado do backend, apenas exibe diretamente
            var jsonValue = document.getElementById("formWebhookPixAtivado:jsonWebhookPixAtivadoHidden").value;
            document.getElementById("jsonWebhookPixAtivado").innerHTML = jsonValue;
        } catch (ex) {
            console.log(ex);
        }
    }

    function fecharJanela() {
        window.close();
    }

    function apagarCampoUmaVez(input) {
        if (!input._jaApagou) {
            input.value = '';
            input._jaApagou = true;
        }
    }

    function formatarPercentual(event, campo) {
        let tecla = event.key;

        // Permitir números, vírgula, backspace e setas
        if (!/[\d,]/.test(tecla) && event.keyCode !== 8 && event.keyCode !== 37 && event.keyCode !== 39) {
            event.preventDefault();
            return false;
        }

        let valor = campo.value;

        // Só permite uma vírgula
        if (tecla === ',' && valor.includes(',')) {
            event.preventDefault();
            return false;
        }

        // Impede mais de 3 casas decimais
        if (valor.includes(',')) {
            let partes = valor.split(',');
            if (partes[1].length >= 3 && campo.selectionStart > valor.indexOf(',')) {
                event.preventDefault();
                return false;
            }
        }

        return true;
    }

</script>
<style>
    .mascara-visual {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        color: black;
        background-color: white;
        pointer-events: none;
        z-index: 2;
        display: flex;
        align-items: center;
        padding-left: 5px;
        font-size: 14px;
        border: 1px solid transparent;
        box-sizing: border-box;
    }

    .mascado-com-overlay {
        color: transparent !important;
        caret-color: black;
        z-index: 1;
    }
</style>
